// import 'package:flutter_cache_store/flutter_cache_store.dart';
import 'package:masterg/utils/Log.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Preference {
  static late Preference _prefHelper;
  static const USERNAME = "USERNAME";

  static const FIREBASE_TOKEN = "FIREBASE_TOKEN";
  static const LOGGEDIN_WITH_EMAIL = "LOGGEDIN_WITH_EMAIL";
  static const USER_TOKEN = "USER_TOKEN";
  static const USER_EMAIL = "USER_EMAIL";
  static const PHONE = "phonenumber";
  static const NATIONALITY = "NATIONALITY";
  static const PROFILE_IMAGE = "PROFILE_IMAGE";
  static const PROFILE_VIDEO = "PROFILE_VIDEO";
  static const PROFILE_PERCENT = "PROFILE_PER";
  static const ABOUT_ME = "ABOUT_ME";
  static const USER_HEADLINE = "USER_HEADLINE";
  static const LOCATION = "LOCATION";
  static const GENDER = "GENDER";
  static const USER_ID = "USER_ID";
  static const FIRST_NAME = "FIRST_NAME";
  static const LAST_NAME = "LATS_NAME";
  static const IS_PRIMARY_LANGUAGE = "IS_PRIMARY_LANGUAGE";
  static const ORGANIZATION_ID = "ORGANIZATION_ID";
  static const ENABLE_PI = "ENABLE_PI";
  static const TERMS_AND_CON_URL = "TERMS_AND_CON_URL";
  static const RESUME_PARSER_DATA_COUNT = "resumeParserDataCount";
  static const RESUME_URL = "resumeURL";
  static const LEARNERDASHBOARDJOBPORTFOLIO = "learner_dashboard_jobportfolio";
  static const ENABLE_SKILL = "ENABLE_SKILL";
  static const ENABLE_MECAT = 'ENABLE_MECAT';
  static const MEC_LEARN = 'MEC_LEARN';

  static String IS_LOGIN = "isLogin";
  static const APP_VERSION = "APP_VERSION";

  static String APP_ENGLISH_NAME = "APPENGLISHNAME";
  static String APP_LANGUAGE = "APPLANGUAGE";
  static String CONTENT_LANGUAGE = "CONTEN_LANGUAGE";
  static String LANGUAGE = "LANGUAGE";
  static String DEFAULT_VIDEO_URL_CATEGORY = "DEFAULT_VIDEO_URL_CATEGORY";
  static String ORG_URL = "ORG_URL";

  static String USER_TYPE = "userType";
  static String CATEGORY_DATA = "CATEGORY_DATA";
  static String USER_COINS = "USER_COINS";
  static String USER_DATA = "USER_DATA";
  static String DESIGNATION = 'designation';
  static String THEMECOLOR = 'THEMECOLOE';
  static String FONTFAMILY = 'FONTFAMILY';
  static String MEC_REGD_ID = 'mecRegdId';
  static String SSOTOKEN = 'ssotoken';
  static String ROLE = 'role';
  static String SECONDARY_ROLE = 'secondary_role';
  static String LOGIN_ID = 'loginid';
  static String LOGIN_PASS = 'loginpass';
  static String rememberMe = 'rememberMe';
  static String isProfileShown = 'isProfileShown';
  static String bannerUrl = 'bannerUrl';
  static String timestampDiffToIndia = 'timestampDiffToIndia';
  static String GET_COUNT = 'GET_COUNT';
  static const SETUP_GOAL = "SETUP_GOAL";
  static const SETUP_GOAL_INTEREST_ID = "setup_goal_interest_id";
  static String SAVE_INTEREST = 'SAVE_INTEREST';
  static const AGE_GROUP = "age_group";
  static const HELP_ENABLE = "help_enable";
  static const USER_LOGIN_TYPE = "USER_LOGIN_TYPE";
  static const USER_LOGIN_TYPE_LOCALE = "USER_LOGIN_TYPE_LOCALE";
  static const USER_LOGIN_TYPE_DESC = "USER_LOGIN_TYPE_DESC";
  static const COUNTRY_CODE = "COUNTRY_CODE";
  static const REGISTER_TYPE = "REGISTER_TYPE";
  static const SELECTED_CLASS_STATUS = "REGISTER_TYPE";
  static const CURRENT_SEMESTER_ID = "CURRENT_SEMESTER_ID";
  static String LAST_RUN_DATE = "lastRunDate";
  static const IS_ACCEPTED = "is_accepted";
  static const IS_ACTION_BUTTON = "is_action_button";

  static getInstance() {
    // if (_prefHelper == null)
    _prefHelper = new Preference();
    return _prefHelper;
  }

  static SharedPreferences? _prefs;
  static Map<String, dynamic> _memoryPrefs = Map<String, dynamic>();
  // static late CacheStore store;

  static Future<SharedPreferences?> load() async {
    _prefs = await SharedPreferences.getInstance();
    return _prefs;
  }

  static Future<bool?> clearPref() async {
    ///init before clear
    int? enablePi = Preference.getInt(Preference.ENABLE_PI);
    print("hello $enablePi");

    ///clear pref
    bool? clear = await _prefs?.clear();
    if (clear != null) {
      _memoryPrefs.clear();
    }

    ///save key which should not be cleared
    Preference.setInt(Preference.ENABLE_PI, int.parse('${enablePi ?? 0}'));
    return clear;
  }

  static void setString(String key, String value) {
    // Capture the current stack trace
    final stackTrace = StackTrace.current;

    // Extract the caller's file name and line number from the stack trace
    final callerInfo = _getCallerInfo(stackTrace);

    Log.v('key is $key and value is $value and $callerInfo');

    _prefs?.setString(key, value);
    _memoryPrefs[key] = value;
  }

  static String _getCallerInfo(StackTrace stackTrace) {
    // Convert the stack trace to a string and split it into lines
    final traceString = stackTrace.toString().split('\n');

    // Find the line that contains information about the caller
    final callerLine = traceString[1]; // Adjust the index as needed

    // Extract the file name and line number using a regular expression
    final regex = RegExp(r'#\d+\s+(.+):(\d+):\d+');
    final match = regex.firstMatch(callerLine);

    if (match != null) {
      final fileName = match.group(1);
      final lineNumber = match.group(2);
      return '$fileName:$lineNumber';
    } else {
      return 'Unknown';
    }
  }

  static void setListString(String key, List<String> value) {
    _prefs?.setStringList(key, value);
    _memoryPrefs[key] = value;
  }

  static void setInt(String key, int value) {
    _prefs?.setInt(key, value);
    _memoryPrefs[key] = value;
  }

  static void setDouble(String key, double value) {
    _prefs?.setDouble(key, value);
    _memoryPrefs[key] = value;
  }

  static void setBool(String key, bool value) {
    _prefs?.setBool(key, value);
    _memoryPrefs[key] = value;
  }

  static String? getString(String key) {
    String? val;
    if (_memoryPrefs.containsKey(key)) {
      val = _memoryPrefs[key];
    }
    if (val == null) val = _prefs?.getString(key);

    // val = def;

    _memoryPrefs[key] = val;
    return val;
  }

  static List<String>? getListString(String key, {List<String>? def}) {
    List<String>? val;
    if (_memoryPrefs.containsKey(key)) {
      val = _memoryPrefs[key];
    }
    if (val == null) {
      val = _prefs?.getStringList(key);
    }
    if (val == null) {
      val = def;
    }
    _memoryPrefs[key] = val;
    return val;
  }

  static bool? exists(String key, {String? def}) {
    return _prefs?.containsKey(key);
  }

  static int? getInt(String key, {int? def}) {
    int? val;
    if (_memoryPrefs.containsKey(key)) {
      val = _memoryPrefs[key];
    }
    if (val == null) {
      val = _prefs?.getInt(key);
    }
    if (val == null) {
      val = def;
    }
    _memoryPrefs[key] = val;
    return val;
  }

  static double? getDouble(String key, {double? def}) {
    double? val;
    if (_memoryPrefs.containsKey(key)) {
      val = _memoryPrefs[key];
    }
    if (val == null) {
      val = _prefs?.getDouble(key);
    }
    if (val == null) {
      val = def;
    }
    _memoryPrefs[key] = val;
    return val;
  }

  static bool? getBool(String key, {bool def = false}) {
    bool? val = def;
    if (_memoryPrefs.containsKey(key)) {
      val = _memoryPrefs[key];
    }
    if (_prefs == null) {
      return val;
    }
    val = _prefs?.getBool(key);
    if (val == null) val = def;
    _memoryPrefs[key] = val;
    return val;
  }
}

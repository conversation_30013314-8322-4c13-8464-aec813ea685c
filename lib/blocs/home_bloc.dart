import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart' as Dio;
import 'package:hive/hive.dart';
import 'package:injector/injector.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/home_request/get_course_modules_request.dart';
import 'package:masterg/data/models/request/home_request/poll_submit_req.dart';
import 'package:masterg/data/models/request/home_request/submit_feedback_req.dart';
import 'package:masterg/data/models/request/home_request/submit_survey_req.dart';
import 'package:masterg/data/models/request/home_request/track_announcement_request.dart';
import 'package:masterg/data/models/request/home_request/user_program_subscribe.dart';
import 'package:masterg/data/models/request/home_request/user_tracking_activity.dart';
import 'package:masterg/data/models/request/save_answer_request.dart';
import 'package:masterg/data/models/response/auth_response/assessmentReportResp.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/competition_my_activity.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_view_resp.dart';
import 'package:masterg/data/models/response/auth_response/event_participate_response.dart';
import 'package:masterg/data/models/response/auth_response/oraganization_program_resp.dart';
import 'package:masterg/data/models/response/general_resp.dart';
import 'package:masterg/data/models/response/home_response/add_open_work_response.dart';
import 'package:masterg/data/models/response/home_response/add_portfolio_resp.dart';
import 'package:masterg/data/models/response/home_response/add_skill_response.dart';
import 'package:masterg/data/models/response/home_response/assessment_certificate_response.dart';
import 'package:masterg/data/models/response/home_response/assessment_details_response.dart';
import 'package:masterg/data/models/response/home_response/assignment_submissions_response.dart';
import 'package:masterg/data/models/response/home_response/company_job_list_response.dart';
import 'package:masterg/data/models/response/home_response/company_list_response.dart';
import 'package:masterg/data/models/response/home_response/competition_content_list_resp.dart';
import 'package:masterg/data/models/response/home_response/competition_response.dart';
import 'package:masterg/data/models/response/home_response/content_tags_resp.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/models/response/home_response/create_post_response.dart';
import 'package:masterg/data/models/response/home_response/delete_post_response.dart';
import 'package:masterg/data/models/response/home_response/delete_skill_response.dart';
import 'package:masterg/data/models/response/home_response/domain_filter_list.dart';
import 'package:masterg/data/models/response/home_response/domain_list_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/Attendance_percentage_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/assign_learner_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_assessment_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_assignment_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_class_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_details_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_module_list_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/hod_program_list.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/mark_attendance_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/matching_jobs_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/module_leader_program_list_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/program_completion_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/update_attendance_resp.dart';
import 'package:masterg/data/models/response/home_response/featured_video_response.dart';
import 'package:masterg/data/models/response/home_response/feedback_response.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/data/models/response/home_response/generate_certificate_resp.dart';
import 'package:masterg/data/models/response/home_response/get_certificates_resp.dart';
import 'package:masterg/data/models/response/home_response/get_comment_response.dart';
import 'package:masterg/data/models/response/home_response/get_content_resp.dart';
import 'package:masterg/data/models/response/home_response/get_course_leaderboard_resp.dart';
import 'package:masterg/data/models/response/home_response/get_course_modules_resp.dart';
import 'package:masterg/data/models/response/home_response/get_courses_resp.dart';
import 'package:masterg/data/models/response/home_response/get_kpi_analysis_resp.dart';
import 'package:masterg/data/models/response/home_response/get_module_leaderboard_resp.dart';
import 'package:masterg/data/models/response/home_response/greels_response.dart';
import 'package:masterg/data/models/response/home_response/job_domain_detail_resp.dart';
import 'package:masterg/data/models/response/home_response/joy_category_response.dart';
import 'package:masterg/data/models/response/home_response/joy_contentList_response.dart';
import 'package:masterg/data/models/response/home_response/language_response.dart';
import 'package:masterg/data/models/response/home_response/learning_space_response.dart';
import 'package:masterg/data/models/response/home_response/map_interest_response.dart';
import 'package:masterg/data/models/response/home_response/master_language_response.dart';
import 'package:masterg/data/models/response/home_response/my_assessment_response.dart';
import 'package:masterg/data/models/response/home_response/my_assignment_response.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/data/models/response/home_response/notification_list_resp.dart';
import 'package:masterg/data/models/response/home_response/notification_read_resp.dart';
import 'package:masterg/data/models/response/home_response/notification_resp.dart';
import 'package:masterg/data/models/response/home_response/onboard_sessions.dart';
import 'package:masterg/data/models/response/home_response/popular_courses_response.dart';
import 'package:masterg/data/models/response/home_response/portfolio_competition_response.dart';
import 'package:masterg/data/models/response/home_response/post_comment_response.dart';
import 'package:masterg/data/models/response/home_response/program_list_reponse.dart';
import 'package:masterg/data/models/response/home_response/report_content_response.dart';
import 'package:masterg/data/models/response/home_response/save_answer_response.dart';
import 'package:masterg/data/models/response/home_response/singularis_portfolio_deleteResp.dart';
import 'package:masterg/data/models/response/home_response/skill_rating_resp.dart';
import 'package:masterg/data/models/response/home_response/skill_suggestion_response.dart';
import 'package:masterg/data/models/response/home_response/submit_answer_response.dart';
import 'package:masterg/data/models/response/home_response/submit_feedback_resp.dart';
import 'package:masterg/data/models/response/home_response/survey_data_resp.dart';
import 'package:masterg/data/models/response/home_response/test_attempt_response.dart';
import 'package:masterg/data/models/response/home_response/test_review_response.dart';
import 'package:masterg/data/models/response/home_response/top_score.dart';
import 'package:masterg/data/models/response/home_response/topics_resp.dart';
import 'package:masterg/data/models/response/home_response/training_detail_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/models/response/home_response/update_user_profile_response.dart';
import 'package:masterg/data/models/response/home_response/user_analytics_response.dart';
import 'package:masterg/data/models/response/home_response/user_jobs_list_response.dart';
import 'package:masterg/data/models/response/home_response/user_profile_response.dart';
import 'package:masterg/data/models/response/home_response/user_program_subscribe_reponse.dart';
import 'package:masterg/data/models/response/home_response/wow_dashboard_response.dart';
import 'package:masterg/data/models/response/home_response/zoom_open_url_response.dart';
import 'package:masterg/data/repositories/home_repository.dart';
import 'package:masterg/pages/user_profile_page/model/MasterBrand.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import '../data/models/response/accept_fee_agreement_response.dart';
import '../data/models/response/home_response/ExploreJobListResponse.dart';
import '../data/models/response/home_response/analyse_video_resume_resp.dart';
import '../data/models/response/home_response/create_portfolio_response.dart';
import '../data/models/response/home_response/delete_portfolio_response.dart';
import '../data/models/response/home_response/download_assessment_report_response.dart';
import '../data/models/response/home_response/explore_job_details_response.dart';
import '../data/models/response/home_response/fee_agreement_response.dart';
import '../data/models/response/home_response/goal_intrest_area_responce.dart';
import '../data/models/response/home_response/leaderboard_resp.dart';
import '../data/models/response/home_response/list_portfolio_responsed.dart';
import '../data/models/response/home_response/list_resume_response.dart';
import '../data/models/response/home_response/pi_detail_resp.dart';
import '../data/models/response/home_response/remove_account_resp.dart';
import '../data/models/response/home_response/top_scroing_user_response.dart';
import '../data/models/response/home_response/video_resume_delete_response.dart';
import '../data/models/response/semester_list_response.dart';

abstract class HomeEvent {
  HomeEvent([List event = const []]) : super();
}

class LearningSpaceEvent extends HomeEvent {
  Box? box;

  LearningSpaceEvent({this.box}) : super([]);

  List<Object> get props => throw UnimplementedError();
}

class MyAssessmentEvent extends HomeEvent {
  Box? box;
  String? interestID;
  int? jobRoleID;
  int? skillID;

  MyAssessmentEvent({this.box, this.interestID, this.jobRoleID, this.skillID})
      : super([]);

  List<Object> get props => throw UnimplementedError();
}

class MyAssignmentEvent extends HomeEvent {
  Box? box;

  MyAssignmentEvent({this.box}) : super([]);

  List<Object> get props => throw UnimplementedError();
}

class UserAnalyticsEvent extends HomeEvent {
  UserAnalyticsEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class LanguageEvent extends HomeEvent {
  int? languageType;

  LanguageEvent({this.languageType}) : super([languageType]);

  List<Object> get props => throw UnimplementedError();
}

class MasterLanguageEvent extends HomeEvent {
  MasterLanguageEvent() : super();

  List<Object> get props => throw UnimplementedError();
}

class UserProgramSubscribeEvent extends HomeEvent {
  UserProgramSubscribeReq? subrReq;

  UserProgramSubscribeEvent({this.subrReq}) : super([subrReq]);

  List<Object> get props => throw UnimplementedError();
}

abstract class HomeState {
  HomeState([List states = const []]) : super();

  List<Object> get props => [];
}

class UserProgramSubscribeState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  UserProgramSubscribeRes? response;
  String? error;

  UserProgramSubscribeState(this.state, {this.response, this.error});
}

class MyAssessmentState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  MyAssessmentResponse? response;
  String? error;

  MyAssessmentState(this.state, {this.response, this.error});
}

class MyAssignmentState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  MyAssignmentResponse? response;
  String? error;

  MyAssignmentState(this.state, {this.response, this.error});
}

class LearningSpaceState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  LearningSpaceResponse? response;
  String? error;

  LearningSpaceState(this.state, {this.response, this.error});
}

class LanguageState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  LanguageResponse? response;
  String? error;

  LanguageState(this.state, {this.response, this.error});
}

class MasterLanguageState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  MasterLanguageResponse? response;
  String? error;

  MasterLanguageState(this.state, {this.response, this.error});
}

class AttemptTestState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AttemptTestResponse? response;
  String? error;

  AttemptTestState(this.state, {this.response, this.error});
}

class AttemptTestEvent extends HomeEvent {
  String? request;

  AttemptTestEvent({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class SaveAnswerState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SaveAnswerResponse? response;
  String? error;

  SaveAnswerState(this.state, {this.response, this.error});
}

class SaveAnswerEvent extends HomeEvent {
  SaveAnswerRequest? request;

  SaveAnswerEvent({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class SubmitAnswerState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SubmitAnswerResponse? response;
  String? error;

  SubmitAnswerState(this.state, {this.response, this.error});
}

//Email Code Send
class ExploreApplyJobState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  int? jobId;
  String? jobType;
  String? error;

  ExploreApplyJobState(this.state, {this.jobId, this.jobType, this.error});
}

class EmailCodeSendState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  String? email;
  String? error;

  EmailCodeSendState(this.state, {this.error, this.email});
}

class VerifyEmailCodeState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  String? email;
  String? code;
  String? error;

  VerifyEmailCodeState(this.state, {this.error, this.email, this.code});
}

class PasswordUpdateState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  String? email;
  String? pass;
  String? error;

  PasswordUpdateState(this.state, {this.error, this.email, this.pass});
}

class EmailCodeSendEvent extends HomeEvent {
  String? email;
  int? isSignup;
  int? forgotPass;

  EmailCodeSendEvent({this.email, this.isSignup, this.forgotPass})
      : super([email, isSignup, forgotPass]);

  List<Object> get props => throw UnimplementedError();
}

class ParticipateEvent extends HomeEvent {
  String? name;
  String? email;
  String? mobileNo;
  int? isMobile;
  int? programId;
  int? countryCode;

  ParticipateEvent(
      {this.name,
      this.email,
      this.mobileNo,
      this.programId,
      this.isMobile,
      this.countryCode})
      : super([name, email, mobileNo, programId, isMobile, countryCode]);

  List<Object> get props => throw UnimplementedError();
}

class ParticipateState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ParticiapteResp? response;
  String? error;

  ParticipateState(this.state, {this.response, this.error});
}

class ExploreApplyJobEvent extends HomeEvent {
  int? jobId;
  String? jobType;

  ExploreApplyJobEvent({this.jobId, this.jobType}) : super([jobId, jobType]);

  List<Object> get props => throw UnimplementedError();
}

class VerifyEmailCodeEvent extends HomeEvent {
  String? email;
  String? code;

  VerifyEmailCodeEvent({this.email, this.code}) : super([email, code]);

  List<Object> get props => throw UnimplementedError();
}

class PasswordUpdateEvent extends HomeEvent {
  String? email;
  String? pass;
  String? locale;

  PasswordUpdateEvent({this.email, this.pass, this.locale})
      : super([email, pass, locale]);

  List<Object> get props => throw UnimplementedError();
}

///-------------

class SubmitAnswerEvent extends HomeEvent {
  String? request;

  SubmitAnswerEvent({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class AssessmentReportEvent extends HomeEvent {
  int? assessmentId;
  AssessmentReportEvent({this.assessmentId}) : super([assessmentId]);

  List<Object> get props => throw UnimplementedError();
}

class AssessmentReportState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AssessmentReportResp? response;
  String? error;

  AssessmentReportState(this.state, {this.response, this.error});
}

class ReviewTestState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  TestReviewResponse? response;
  String? error;

  ReviewTestState(this.state, {this.response, this.error});
}

class UserJobListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  UserJobsListResponse? response;
  String? error;

  UserJobListState(this.state, {this.response, this.error});
}

class CompetitionDetailState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  TrainingModuleResponse? response;
  String? error;

  CompetitionDetailState(this.state, {this.response, this.error});
}

class TrainingDetailState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  TrainingDetailResponse? response;
  String? error;

  TrainingDetailState(this.state, {this.response, this.error});
}

class UserJobsListEvent extends HomeEvent {
  UserJobsListEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class CompetitionDetailEvent extends HomeEvent {
  int? moduleId;

  CompetitionDetailEvent({this.moduleId}) : super([moduleId]);

  List<Object> get props => throw UnimplementedError();
}

class TrainingDetailEvent extends HomeEvent {
  int? programId;

  TrainingDetailEvent({this.programId}) : super([programId]);

  List<Object> get props => throw UnimplementedError();
}

class NotificationsListEvent extends HomeEvent {
  int? fromValue;
  int? toValue;
  bool isInitial;
  NotificationsListEvent({this.fromValue, this.toValue, this.isInitial = false})
      : super();

  List<Object> get props => throw UnimplementedError();
}

class NotificationReadEvent extends HomeEvent {
  String? id;
  int? notiId;
  String? type;
  String? isRead;
  NotificationReadEvent({this.isRead, this.type, this.id, this.notiId})
      : super([]);

  List<Object> get props => throw UnimplementedError();
}

class NotificationsListState extends HomeState {
  NotificationStatus state;

  NotificationStatus get apiState => state;
  NotificationsListResp? response;
  String? error;

  NotificationsListState(this.state, {this.response, this.error});
}

class NotificationsReadState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  NotificationReadResp? response;
  String? error;

  NotificationsReadState(this.state, {this.response, this.error});
}

class ReviewTestEvent extends HomeEvent {
  String? request;

  ReviewTestEvent({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class FacultyBatchDetailsEvent extends HomeEvent {
  final int? courseId;
  FacultyBatchDetailsEvent({this.courseId}) : super();

  List<Object> get props => throw UnimplementedError();
}

class MatchingJobsEvent extends HomeEvent {
  MatchingJobsEvent() : super();

  List<Object> get props => throw UnimplementedError();
}

class MatchingJobsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  MatchingJobsResponse? response;
  String? error;

  MatchingJobsState(this.state, {this.response, this.error});
}

class FacultyModuleListEvent extends HomeEvent {
  final int? courseId;
  FacultyModuleListEvent({this.courseId}) : super();

  List<Object> get props => throw UnimplementedError();
}

class ModuleLeaderProgramListEvent extends HomeEvent {
  final int? courseId;
  ModuleLeaderProgramListEvent({this.courseId}) : super();

  List<Object> get props => throw UnimplementedError();
}

class HodProgramListEvent extends HomeEvent {
  HodProgramListEvent() : super();

  List<Object> get props => throw UnimplementedError();
}

class FacultyModuleListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  FacultyModuleListResponse? response;
  String? error;

  FacultyModuleListState(this.state, {this.response, this.error});
}

class ModuleLeaderProgramListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ModuleLeaderProgramListResponse? response;
  String? error;

  ModuleLeaderProgramListState(this.state, {this.response, this.error});
}

class HodProgramListtState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  HodProgramListtResponse? response;
  String? error;

  HodProgramListtState(this.state, {this.response, this.error});
}

class FacultyCourseDetailsEvent extends HomeEvent {
  int? programId;
  FacultyCourseDetailsEvent({this.programId}) : super();

  List<Object> get props => throw UnimplementedError();
}

class AssignLearnerEvent extends HomeEvent {
  int? isFaculty;
  int? programId;
  AssignLearnerEvent({this.isFaculty, this.programId})
      : super([isFaculty, programId]);

  List<Object> get props => throw UnimplementedError();
}

class AssignLearnerState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AssignLearnerResponse? response;
  String? error;

  AssignLearnerState(this.state, {this.response, this.error});
}

class FacultyBatchClassEvent extends HomeEvent {
  String? selectedDate;
  FacultyBatchClassEvent({this.selectedDate}) : super([selectedDate]);

  List<Object> get props => throw UnimplementedError();
}

class FacultyBatchAssignmentEvent extends HomeEvent {
  int? courseId;
  String? selectedDate;
  FacultyBatchAssignmentEvent({this.courseId, this.selectedDate})
      : super([courseId, selectedDate]);

  List<Object> get props => throw UnimplementedError();
}

class FacultyBatchAssessmentEvent extends HomeEvent {
  int? courseId;
  String? selectedDate;
  FacultyBatchAssessmentEvent({this.courseId, this.selectedDate})
      : super([courseId, selectedDate]);

  List<Object> get props => throw UnimplementedError();
}

class ProgramCompletionEvent extends HomeEvent {
  dynamic programCompletionId;
  ProgramCompletionEvent({this.programCompletionId})
      : super([programCompletionId]);

  List<Object> get props => throw UnimplementedError();
}

class AttendancePercentageEvent extends HomeEvent {
  int? programCompletionId;
  AttendancePercentageEvent({this.programCompletionId})
      : super([programCompletionId]);

  List<Object> get props => throw UnimplementedError();
}

class FacultyBatchDetailsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  FacultyBatchDetailsResponse? response;
  String? error;

  FacultyBatchDetailsState(this.state, {this.response, this.error});
}

class FacultyCourseDetailsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  FacultyBatchDetailsResponse? response;
  String? error;

  FacultyCourseDetailsState(this.state, {this.response, this.error});
}

class FacultyBatchClassState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  FacultyBatchClassResponse? response;
  String? error;

  FacultyBatchClassState(this.state, {this.response, this.error});
}

class FacultyBatchAssignmentState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  FacultyBatchAssignmentResponse? response;
  String? error;

  FacultyBatchAssignmentState(this.state, {this.response, this.error});
}

class FacultyBatchAssessmentState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  FacultyBatchAssessmentResponse? response;
  String? error;

  FacultyBatchAssessmentState(this.state, {this.response, this.error});
}

class AttendancePercentageState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AttendancePercentageResponse? response;
  String? error;

  AttendancePercentageState(this.state, {this.response, this.error});
}

class ProgramCompletionState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ProgramCompletionResponse? response;
  String? error;

  ProgramCompletionState(this.state, {this.response, this.error});
}

class MarkAttendanceEvent extends HomeEvent {
  int? batchId;
  int? classId;
  MarkAttendanceEvent({this.batchId, this.classId}) : super([batchId, classId]);

  List<Object> get props => throw UnimplementedError();
}

class MarkAttendanceState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  MarkAttendanceResponse? response;
  String? error;

  MarkAttendanceState(this.state, {this.response, this.error});
}

class UpdateAttendanceState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  UpdateAttendanceResp? response;
  String? error;

  UpdateAttendanceState(this.state, {this.response, this.error});
}

class UpdateAttendanceEvent extends HomeEvent {
  int? contentId;
  String? attendance;
  List<int>? users;
  UpdateAttendanceEvent({this.contentId, this.attendance, this.users})
      : super([contentId, attendance, users]);

  List<Object> get props => throw UnimplementedError();
}

class AssignmentSubmissionsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AssignmentSubmissionResponse? response;
  String? error;

  AssignmentSubmissionsState(this.state, {this.response, this.error});
}

class AssignmentSubmissionsEvent extends HomeEvent {
  int? request;

  AssignmentSubmissionsEvent({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class JoyCategoryEvent extends HomeEvent {
  JoyCategoryEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class GetCommentEvent extends HomeEvent {
  int? postId;

  GetCommentEvent({this.postId}) : super([postId]);

  List<Object> get props => throw UnimplementedError();
}

class PostCommentEvent extends HomeEvent {
  int? postId;
  int? parentId;
  String? comment;

  PostCommentEvent({this.postId, this.parentId, this.comment})
      : super([postId, parentId, comment]);

  List<Object> get props => throw UnimplementedError();
}

class getLiveClassEvent extends HomeEvent {
  getLiveClassEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class GetBottomNavigationBarEvent extends HomeEvent {
  GetBottomNavigationBarEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class PopularCoursesEvent extends HomeEvent {
  PopularCoursesEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class FilteredPopularCoursesEvent extends HomeEvent {
  FilteredPopularCoursesEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class GetUserProfileEvent extends HomeEvent {
  GetUserProfileEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class UpdateUserProfileImageEvent extends HomeEvent {
  String? filePath;
  String? name;
  String? email;

  UpdateUserProfileImageEvent({this.filePath, this.name, this.email})
      : super([filePath, name, email]);

  List<Object> get props => throw UnimplementedError();
}

class GetUserProfileState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  UserProfileResp? response;
  String? error;

  GetUserProfileState(this.state, {this.response, this.error});
}

class UpdateUserProfileImageState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  UpdateProfileImageResponse? response;
  String? error;

  UpdateUserProfileImageState(this.state, {this.response, this.error});
}

class PopularCoursesState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  popularCourses? response;
  String? error;

  PopularCoursesState(this.state, {this.response, this.error});
}

class GetBottomBarState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  BottomBarResponse? response;
  String? error;

  GetBottomBarState(this.state, {this.response, this.error});
}

class FilteredPopularCoursesState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  popularCourses? response;
  String? error;

  FilteredPopularCoursesState(this.state, {this.response, this.error});
}

class JoyCategoryState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  JoyCategoryResponse? response;
  String? error;

  JoyCategoryState(this.state, {this.response, this.error});
}

class GetCommentState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CommentListResponse? response;
  String? error;

  GetCommentState(this.state, {this.response, this.error});
}

class PostCommentState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  PostCommentResponse? response;
  String? error;

  PostCommentState(this.state, {this.response, this.error});
}

class getLiveClassState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  onBoardSessions? response;
  String? error;

  getLiveClassState(this.state, {this.response, this.error});
}

class JoyContentListEvent extends HomeEvent {
  bool fetchAll;
  JoyContentListEvent({this.fetchAll = false}) : super([fetchAll]);

  List<Object> get props => throw UnimplementedError();
}

class JoyContentListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  JoyConentListResponse? response;
  String? error;

  JoyContentListState(this.state, {this.response, this.error});
}

class JoyContentByPostIdEvent extends HomeEvent {
  int? postId;
  JoyContentByPostIdEvent({this.postId}) : super([postId]);

  List<Object> get props => throw UnimplementedError();
}

class JoyContentByPostIdState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  JoyConentListResponse? response;
  String? error;

  JoyContentByPostIdState(this.state, {this.response, this.error});
}

class DashboardIsVisibleEvent extends HomeEvent {
  DashboardIsVisibleEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class DashboardIsVisibleState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  DashboardViewResponse? response;
  String? error;

  DashboardIsVisibleState(this.state, {this.response, this.error});
}

// class DashboardContentEvent extends HomeEvent {
//   DashboardContentEvent() : super([]);

//   List<Object> get props => throw UnimplementedError();
// }

// class DashboardContentState extends HomeState {
//   ApiStatus state;

//   ApiStatus get apiState => state;
//   DashboardContentResponse? response;
//   String? error;

//   DashboardContentState(this.state, {this.response, this.error});
// }

class ProgramListEvent extends HomeEvent {
  ProgramListEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class OrganizationProgramListEvent extends HomeEvent {
  int? fetchGoalList;
  OrganizationProgramListEvent(this.fetchGoalList) : super([fetchGoalList]);

  List<Object> get props => throw UnimplementedError();
}

class OrganizationProgramListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  OrganizationProgramListResp? response;
  GoalInterestAreaResponse? responseSetGoal;
  String? error;

  OrganizationProgramListState(this.state,
      {this.response, this.responseSetGoal, this.error});
}

class ProgramListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ProgramListResponse? response;
  String? error;

  ProgramListState(this.state, {this.response, this.error});
}

class SemesterListEvent extends HomeEvent {
  SemesterListEvent() : super([]);
  List<Object> get props => throw UnimplementedError();
}

class CourseCategoryListIDEvent extends HomeEvent {
  int? categoryId;
  int? semesterID;

  CourseCategoryListIDEvent({this.categoryId, this.semesterID})
      : super([categoryId, semesterID]);

  List<Object> get props => throw UnimplementedError();
}

class CompetitionListEvent extends HomeEvent {
  bool? isPopular;
  bool? isFilter;
  String? ids;
  String? domainId;
  String? competitionType;
  bool? callMyActivity;

  CompetitionListEvent(
      {this.isPopular,
      this.isFilter = false,
      this.ids,
      this.domainId,
      this.competitionType,
      this.callMyActivity = false})
      : super([isPopular, isFilter, ids]);

  List<Object> get props => throw UnimplementedError();
}

class CompetitionListFilterEvent extends HomeEvent {
  bool? isPopular;
  bool? isFilter;
  String? ids;
  String? domainId;

  CompetitionListFilterEvent(
      {this.isPopular, this.isFilter = false, this.ids, this.domainId})
      : super([isPopular, isFilter, ids]);

  List<Object> get props => throw UnimplementedError();
}

class JobCompListEvent extends HomeEvent {
  bool? isPopular;
  bool? isFilter;
  String? ids;
  int? isJob;
  int? myJob;
  String? domainId;
  bool? jobTypeMyJob;

  JobCompListEvent(
      {this.isPopular,
      this.isFilter = false,
      this.ids,
      this.isJob,
      this.myJob,
      this.domainId,
      this.jobTypeMyJob = false})
      : super([isPopular, isFilter, ids, isJob, myJob, domainId, jobTypeMyJob]);

  List<Object> get props => throw UnimplementedError();
}

class JobCompListFilterEvent extends HomeEvent {
  bool? isPopular;
  bool? isFilter;
  String? ids;
  bool? jobTypeMyJob;
  String? domainId;
  String? widgetType;

  JobCompListFilterEvent(
      {this.isPopular,
      this.isFilter = false,
      this.ids,
      this.jobTypeMyJob = false,
      this.domainId,
      this.widgetType = 'filter'})
      : super([isPopular, isFilter, ids, jobTypeMyJob, domainId, widgetType]);

  List<Object> get props => throw UnimplementedError();
}

class DomainListEvent extends HomeEvent {
  DomainListEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class DomainListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  DomainListResponse? response;
  String? error;

  DomainListState(this.state, {this.response, this.error});
}

class JobDomainDetailEvent extends HomeEvent {
  int? domainId;

  JobDomainDetailEvent({this.domainId}) : super([domainId]);

  List<Object> get props => throw UnimplementedError();
}

class JobDomainDetailState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  JobDomainResponse? response;
  String? error;

  JobDomainDetailState(this.state, {this.response, this.error});
}

class DomainFilterListEvent extends HomeEvent {
  String? ids;

  DomainFilterListEvent({this.ids}) : super([ids]);

  List<Object> get props => throw UnimplementedError();
}

class DomainFilterListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  DomainFilterListResponse? response;
  String? error;

  DomainFilterListState(this.state, {this.response, this.error});
}

class LeaderboardEvent extends HomeEvent {
  String? type;
  int? id;
  int? skipotherUser;
  int? skipcurrentUser;

  LeaderboardEvent(
      {this.id, this.type, this.skipcurrentUser, this.skipotherUser})
      : super([id, type, skipotherUser, skipcurrentUser]);

  List<Object> get props => throw UnimplementedError();
}

class LeaderboardState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  LeaderboardResponse? response;

  LeaderboardState(
    this.state, {
    this.response,
  });
}

class CourseCategoryListIDState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CourseCategoryListIdResponse? response;
  CourseCategoryListIdResponse? error;

  CourseCategoryListIDState(this.state, {this.response, this.error});
}

class SemesterListState extends HomeState {
  ApiStatus state;
  ApiStatus get apiState => state;
  SemesterListResponse? response;
  String? error;

  SemesterListState(this.state, {this.response, this.error});
}

class CompetitionListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompetitionResponse? competitonResponse, popularCompetitionResponse;
  PortfolioCompetitionResponse? competedCompetition;
  CompetitionMyActivityResponse? myActivity;
  String? error;
  bool isEventType;

  CompetitionListState(this.state,
      {this.competitonResponse,
      this.popularCompetitionResponse,
      this.competedCompetition,
      this.myActivity,
      this.error,
      required this.isEventType});
}

class CompetitionListFilterState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompetitionResponse? competitionFilterResponse;
  String? error;

  CompetitionListFilterState(this.state,
      {this.competitionFilterResponse, this.error});
}

class JobCompListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompetitionResponse? jobListResponse,
      myJobListResponse,
      recommendedJobOpportunities;
  String? error;

  JobCompListState(this.state,
      {this.jobListResponse,
      this.myJobListResponse,
      this.recommendedJobOpportunities,
      this.error});
}

class JobCompListFilterState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompetitionResponse? jobListResponse;
  String? error;

  JobCompListFilterState(this.state, {this.jobListResponse, this.error});
}

class PopularCompetitionListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompetitionResponse? response;
  String? error;

  PopularCompetitionListState(this.state, {this.response, this.error});
}

class CourseCategoryList2IDEvent extends HomeEvent {
  int? categoryId;

  CourseCategoryList2IDEvent({this.categoryId}) : super([categoryId]);

  List<Object> get props => throw UnimplementedError();
}

class CompetitionContentListEvent extends HomeEvent {
  int? competitionId;
  int? isApplied;

  CompetitionContentListEvent({this.competitionId, this.isApplied})
      : super([competitionId, isApplied]);

  List<Object> get props => throw UnimplementedError();
}

class AssessmentDetailsEvent extends HomeEvent {
  int? contentId;

  AssessmentDetailsEvent({
    this.contentId,
  }) : super([contentId]);

  List<Object> get props => throw UnimplementedError();
}

class AssessmentCertificateEvent extends HomeEvent {
  int? contentId;
  int? certificateId;

  AssessmentCertificateEvent({this.contentId, this.certificateId})
      : super([contentId, certificateId]);

  List<Object> get props => throw UnimplementedError();
}

class AssessmentDetailsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AssessmentDetailsResponse? response;

  AssessmentDetailsState(this.state, {this.response});
}

class AssessmentCertificateState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AssessmentCertificateResponse? response;

  AssessmentCertificateState(this.state, {this.response});
}

class ExploreJobListEvent extends HomeEvent {
  int? indexNo;
  bool? doRefresh;
  String? resumeUrl;

  ExploreJobListEvent({this.indexNo, this.doRefresh = false, this.resumeUrl})
      : super([indexNo]);

  List<Object> get props => throw UnimplementedError();
}

class ExploreJobDetailsEvent extends HomeEvent {
  String? jobId;

  ExploreJobDetailsEvent({this.jobId}) : super([jobId]);

  List<Object> get props => throw UnimplementedError();
}

class CourseCategoryList2IDState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CourseCategoryListIdResponse? response;
  String? error;

  CourseCategoryList2IDState(this.state, {this.response, this.error});
}

class CompetitionContentListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompetitionContentListResponse? response;

  CompetitionContentListState(this.state, {this.response});
}

class ExploreJobListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ExploreJobListResponse? response;

  ExploreJobListState(this.state, {this.response});
}

class ExploreJobDetailsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ExploreJobDetailsResponse? response;

  ExploreJobDetailsState(this.state, {this.response});
}

class AppJobListCompeState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompetitionContentListResponse? response;

  AppJobListCompeState(this.state, {this.response});
}

class FeaturedVideoEvent extends HomeEvent {
  FeaturedVideoEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class CreatePortfolioEvent extends HomeEvent {
  String? title;
  String? description;
  String? type;
  String? filePath;

  CreatePortfolioEvent({this.title, this.description, this.type, this.filePath})
      : super([title, description, type, filePath]);

  List<Object> get props => throw UnimplementedError();
}

class MasterBrandCreateEvent extends HomeEvent {
  String? title;
  String? description;
  String? filePath;

  MasterBrandCreateEvent({this.title, this.description, this.filePath})
      : super([title, description, filePath]);

  List<Object> get props => throw UnimplementedError();
}

class UserBrandCreateEvent extends HomeEvent {
  String? endDate;
  String? startDate;
  int? typeId;
  String? filePath;

  UserBrandCreateEvent(
      {this.endDate, this.startDate, this.typeId, this.filePath})
      : super([endDate, startDate, typeId, filePath]);

  List<Object> get props => throw UnimplementedError();
}

class topScoringUsersEvent extends HomeEvent {
  int? userId;

  topScoringUsersEvent({this.userId}) : super([userId]);

  List<Object> get props => throw UnimplementedError();
}

class DeletePortfolioEvent extends HomeEvent {
  int? id;

  DeletePortfolioEvent({this.id}) : super([id]);

  List<Object> get props => throw UnimplementedError();
}

class ListPortfolioEvent extends HomeEvent {
  String? type;
  int? userId;

  ListPortfolioEvent({this.type, this.userId}) : super([type, userId]);

  List<Object> get props => throw UnimplementedError();
}

class DeletePortfolioState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  DeletePortfolioResponse? response;
  String? error;

  DeletePortfolioState(this.state, {this.response, this.error});
}

class SingularisDeletePortfolioEvent extends HomeEvent {
  int? portfolioId;

  SingularisDeletePortfolioEvent({this.portfolioId}) : super([portfolioId]);

  List<Object> get props => throw UnimplementedError();
}

class SingularisDeletePortfolioState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SingularisPortfolioDelete? response;
  String? error;

  SingularisDeletePortfolioState(this.state, {this.response, this.error});
}

class ListPortfolioState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ListPortfolioResponse? response;
  String? error;

  ListPortfolioState(this.state, {this.response, this.error});
}

class topScoringUsersState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  TopScoringUsersResponse? response;
  String? error;

  topScoringUsersState(this.state, {this.response, this.error});
}

class CreatePortfolioState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CreatePortfolioResponse? response;
  String? error;

  CreatePortfolioState(this.state, {this.response, this.error});
}

class MasterBrandCreateState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  MasterBrandResponse? response;
  String? error;

  MasterBrandCreateState(this.state, {this.response, this.error});
}

class UserBrandCreateState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  String? error;

  UserBrandCreateState(this.state, {this.error});
}

class FeaturedVideoState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  FeaturedVideoResponse? response;
  String? error;

  FeaturedVideoState(this.state, {this.response, this.error});
}

class InterestEvent extends HomeEvent {
  InterestEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class InterestState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  JoyCategoryResponse? response;
  String? error;

  InterestState(this.state, {this.response, this.error});
}

class SkillRatingEvent extends HomeEvent {
  SkillRatingEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class SkillRatingState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SkillRatingResponse? response;
  String? error;

  SkillRatingState(this.state, {this.response, this.error});
}

class UserAnalyticsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  UserAnalyticsResp? response;
  String? error;

  UserAnalyticsState(this.state, {this.response, this.error});
}

class MapInterestEvent extends HomeEvent {
  String? param;
  String? mapType;

  MapInterestEvent({this.param, this.mapType = ''}) : super([param, mapType]);

  List<Object> get props => throw UnimplementedError();
}

class MapInterestState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  MapInterestResponse? response;
  String? error;

  MapInterestState(this.state, {this.response, this.error});
}

class GCarvaanPostEvent extends HomeEvent {
  int? callCount;
  int? postId;
  bool userActivity;

  GCarvaanPostEvent({this.callCount, this.postId, this.userActivity = false})
      : super([callCount, postId]);

  List<Object> get props => throw UnimplementedError();
}

class SinlgePostGCarvaanEvent extends HomeEvent {
  int? postId;

  SinlgePostGCarvaanEvent({this.postId}) : super([postId]);

  List<Object> get props => throw UnimplementedError();
}

class GCarvaanPostState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GCarvaanPostResponse? response;
  String? error;

  GCarvaanPostState(this.state, {this.response, this.error});
}

class SinlgePostGCarvaanState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GCarvaanPostResponse? response;
  String? error;

  SinlgePostGCarvaanState(this.state, {this.response, this.error});
}

class GReelsPostEvent extends HomeEvent {
  bool userActivity;
  int from;
  int count;

  GReelsPostEvent({this.userActivity = false, this.from = 1, this.count = 10})
      : super([]);

  List<Object> get props => throw UnimplementedError();
}

class SingleGReelsPostEvent extends HomeEvent {
  int reelId;

  SingleGReelsPostEvent({required this.reelId}) : super([]);

  List<Object> get props => throw UnimplementedError();
}

class GReelsPostState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GReelsPostResponse? response;
  String? error;

  GReelsPostState(this.state, {this.response, this.error});
}

class AnnouncementContentState extends HomeState {
  ApiStatus state;
  int? contentType;

  ApiStatus get apiState => state;
  GetContentResp? response;
  String? error;

  AnnouncementContentState(this.state,
      {this.response, this.error, this.contentType});
}

class AnnouncementContentEvent extends HomeEvent {
  Box? box;
  int? contentType;

  AnnouncementContentEvent({this.contentType, this.box}) : super([contentType]);

  List<Object> get props => throw UnimplementedError();
}

class UpdateVideoCompletionEvent extends HomeEvent {
  int? bookmark;
  int? contentId;
  int? completionPercent;

  UpdateVideoCompletionEvent(
      {this.bookmark, this.contentId, this.completionPercent})
      : super([bookmark, contentId, completionPercent]);

  List<Object> get props => throw UnimplementedError();
}

class PiDetailEvent extends HomeEvent {
  int? userId;

  PiDetailEvent({this.userId}) : super([userId]);

  List<Object> get props => throw UnimplementedError();
}

class UpdateVideoCompletionState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  String? error;

  UpdateVideoCompletionState(this.state, {this.error});
}

class OpenToWorkEvent extends HomeEvent {
  int? openToWork;
  OpenToWorkEvent({this.openToWork})
      : super([
          openToWork,
        ]);
  List<Object> get props => throw UnimplementedError();
}

class OpenToWorkState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddOpenWorkResponse? response;
  String? error;

  OpenToWorkState(this.state, {this.response, this.error});
}

class PiDetailState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  PiDetailResponse? response;
  String? error;

  PiDetailState(this.state, {this.error, this.response});
}

class CreatePostEvent extends HomeEvent {
  int? contentType;
  String? title;
  String? description;
  String? postType;
  List<String?>? filePath;
  String? thumbnail;

  CreatePostEvent(
      {this.contentType,
      this.title,
      this.description,
      this.postType,
      this.filePath,
      this.thumbnail})
      : super([title, description, postType, filePath]);

  List<Object> get props => throw UnimplementedError();
}

class CreatePostState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CreatePostResponse? response;
  String? error;

  CreatePostState(this.state, {this.response, this.error});
}

class NotificationState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  NotificationResp? response;
  String? error;

  NotificationState(this.state, {this.response, this.error});
}

class NotificationListEvent extends HomeEvent {
  NotificationListEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class GetCoursesEvent extends HomeEvent {
  GetCoursesEvent({this.type = 0}) : super([]);
  int type;

  List<Object> get props => throw UnimplementedError();
}

class GetCoursesState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GetCoursesResp? response;
  String? error;

  GetCoursesState(this.state, {this.response, this.error});
}

class UserTrackingActivityEvent extends HomeEvent {
  UserTrackingActivity? trackReq;

  UserTrackingActivityEvent({this.trackReq}) : super([trackReq]);

  List<Object> get props => throw UnimplementedError();
}

class UserTrackingActivityState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GeneralResp? response;
  String? error;

  UserTrackingActivityState(this.state, {this.response, this.error});
}

class LikeContentEvent extends HomeEvent {
  int? contentId;
  String? type;
  int? like;

  LikeContentEvent({
    this.contentId,
    this.type,
    this.like,
  }) : super([contentId, type, like]);

  List<Object> get props => throw UnimplementedError();
}

class ReportEvent extends HomeEvent {
  String? status;

  int? postId;
  String? category;
  String? comment;

  ReportEvent({this.postId, this.category, this.comment, this.status})
      : super([status, postId, category, comment]);

  List<Object> get props => throw UnimplementedError();
}

class DeletePostEvent extends HomeEvent {
  int? postId;

  DeletePostEvent({this.postId})
      : super([
          postId,
        ]);

  List<Object> get props => throw UnimplementedError();
}

class DeleteSkillEvent extends HomeEvent {
  int? skillId;

  DeleteSkillEvent({this.skillId})
      : super([
          skillId,
        ]);

  List<Object> get props => throw UnimplementedError();
}

class LikeContentState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;

  LikeContentState(
    this.state,
  );
}

class ReportState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ReportContentResp? response;
  String? error;

  ReportState(this.state, {this.response, this.error});
}

class DeletePostState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  DeletePostResponse? response;
  String? error;

  DeletePostState(this.state, {this.response, this.error});
}

class DeleteSkillState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  DeleteSkillResponse? response;
  String? error;

  DeleteSkillState(this.state, {this.response, this.error});
}

class GetKPIAnalysisEvent extends HomeEvent {
  GetKPIAnalysisEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class GetKPIAnalysisState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GetKpiAnalysisResp? response;
  String? error;

  GetKPIAnalysisState(this.state, {this.response, this.error});
}

class GetCourseModulesState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GetCourseModulesResp? response;
  String? error;

  GetCourseModulesState(this.state, {this.response, this.error});
}

class GetCourseLeaderboardState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GetCourseLeaderboardResp? response;
  String? error;

  GetCourseLeaderboardState(this.state, {this.response, this.error});
}

class GetCourseLeaderboardEvent extends HomeEvent {
  GetCourseModulesRequest? getCourseModulesReq;
  int type;

  GetCourseLeaderboardEvent({this.getCourseModulesReq, this.type = 0})
      : super([getCourseModulesReq, type]);

  List<Object> get props => throw UnimplementedError();
}

class GetCourseModulesEvent extends HomeEvent {
  GetCourseModulesRequest? getCourseModulesReq;
  int? type;

  GetCourseModulesEvent({this.getCourseModulesReq, this.type = 0})
      : super([getCourseModulesReq, type]);

  List<Object> get props => throw UnimplementedError();
}

class GetCertificatesEvent extends HomeEvent {
  GetCertificatesEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class GetCertificatesState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GetCertificatesResp? response;
  String? error;

  GetCertificatesState(this.state, {this.response, this.error});
}

class GetModuleLeaderboardState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GetModuleLeaderboardResp? response;
  String? error;

  GetModuleLeaderboardState(this.state, {this.response, this.error});
}

class GetModuleLeaderboardEvent extends HomeEvent {
  GetCourseModulesRequest? getCourseModulesReq;
  int type;

  GetModuleLeaderboardEvent({this.getCourseModulesReq, this.type = 0})
      : super([getCourseModulesReq, type]);

  List<Object> get props => throw UnimplementedError();
}

class SubmitFeedbackState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SubmitFeedbackResp? response;
  String? error;

  SubmitFeedbackState(this.state, {this.response, this.error});
}

class SubmitFeedbackEvent extends HomeEvent {
  FeedbackReq? feedbackReq;

  SubmitFeedbackEvent({this.feedbackReq}) : super([feedbackReq]);

  List<Object> get props => throw UnimplementedError();
}

class TopicsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  TopicsResp? response;
  String? error;

  TopicsState(this.state, {this.response, this.error});
}

class TopicsEvent extends HomeEvent {
  TopicsEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class FeedbackState extends HomeState {
  ApiStatus state;
  int? contentType;

  ApiStatus get apiState => state;
  FeedbackResp? response;
  String? error;

  FeedbackState(this.state, {this.response, this.error, this.contentType});
}

class FeedbackEvent extends HomeEvent {
  int? categoryType;

  FeedbackEvent({this.categoryType}) : super([categoryType]);

  List<Object> get props => throw UnimplementedError();
}

class ContentTagsState extends HomeState {
  ApiStatus state;
  int? contentType;

  ApiStatus get apiState => state;
  ContentTagsResp? response;
  String? error;

  ContentTagsState(this.state, {this.response, this.error, this.contentType});
}

class ContentTagsEvent extends HomeEvent {
  int? categoryType;

  ContentTagsEvent({this.categoryType}) : super([categoryType]);

  List<Object> get props => throw UnimplementedError();
}

class LibraryContentState extends HomeState {
  ApiStatus state;
  int? contentType;

  ApiStatus get apiState => state;
  GetContentResp? response;
  String? error;

  LibraryContentState(this.state,
      {this.response, this.error, this.contentType});
}

class LibraryContentEvent extends HomeEvent {
  int? contentType;

  LibraryContentEvent({this.contentType}) : super([contentType]);

  List<Object> get props => throw UnimplementedError();
}

class TrackAnnouncementEvent extends HomeEvent {
  TrackAnnouncementReq? rewardReq;

  TrackAnnouncementEvent({this.rewardReq}) : super([rewardReq]);

  List<Object> get props => throw UnimplementedError();
}

class ActivityAttemptEvent extends HomeEvent {
  String? filePath;
  int? contentType;
  int? contentId;

  ActivityAttemptEvent({this.filePath, this.contentType, this.contentId})
      : super([filePath, contentType, contentId]);

  List<Object> get props => throw UnimplementedError();
}

class ActivityAttemptState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GeneralResp? response;
  String? error;

  ActivityAttemptState(this.state, {this.response, this.error});
}

class TrackAnnouncementState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GeneralResp? response;
  String? error;

  TrackAnnouncementState(this.state, {this.response, this.error});
}

class SurveyDataState extends HomeState {
  ApiStatus state;
  int? contentId;

  ApiStatus get apiState => state;
  SurveyDataResp? response;
  String? error;

  SurveyDataState(this.state, {this.response, this.error, this.contentId});
}

class SurveyDataEvent extends HomeEvent {
  int? contentId;
  int? type;

  SurveyDataEvent({this.contentId, this.type}) : super([contentId, type]);

  List<Object> get props => throw UnimplementedError();
}

class RemoveAccountEvent extends HomeEvent {
  String? type;

  RemoveAccountEvent({this.type}) : super([type]);

  List<Object> get props => throw UnimplementedError();
}

class RemoveAccountState extends HomeState {
  ApiStatus state;
  String? type;

  ApiStatus get apiState => state;
  RemoveAccountResponse? response;

  RemoveAccountState(this.state, {this.response, this.type});
}

class SurveySubmitState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GeneralResp? response;
  String? error;

  SurveySubmitState(this.state, {this.response, this.error});
}

class SubmitSurveyEvent extends HomeEvent {
  SubmitSurveyReq? submitSurveyReq;

  SubmitSurveyEvent({this.submitSurveyReq}) : super([submitSurveyReq]);
}

class PollSubmitState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GeneralResp? response;
  String? error;

  PollSubmitState(this.state, {this.response, this.error});
}

class SubmitPollEvent extends HomeEvent {
  PollSubmitRequest? submitPollReq;

  SubmitPollEvent({this.submitPollReq}) : super([submitPollReq]);
}

class PortfolioEvent extends HomeEvent {
  int? userId;
  PortfolioEvent({this.userId}) : super([userId]);

  List<Object> get props => throw UnimplementedError();
}

class AddPortfolioEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddPortfolioEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class AddResumeEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddResumeEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class UploadProfileEvent extends HomeEvent {
  Map<String, dynamic>? data;

  UploadProfileEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class AddSocialEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddSocialEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class PortfolioCompetitoinEvent extends HomeEvent {
  int? userId;

  PortfolioCompetitoinEvent({this.userId}) : super([userId]);

  List<Object> get props => throw UnimplementedError();
}

class PortfoilioCompetitionState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  PortfolioCompetitionResponse? response;
  String? error;

  PortfoilioCompetitionState(this.state, {this.response, this.error});
}

class AddSocialState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddSocialState(this.state, {this.response, this.error});
}

class TopScoringUserState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  TopScoringResponse? response;
  String? error;

  TopScoringUserState(this.state, {this.response, this.error});
}

class ZoomOpenUrlState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ZoomOpenUrlResponse? response;

  ZoomOpenUrlState(this.state, {this.response});
}

class PortfolioState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  PortfolioResponse? response;
  String? error;

  PortfolioState(this.state, {this.response, this.error});
}

class AddResumeState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddResumeState(this.state, {this.response, this.error});
}

class UploadProfileState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  UploadProfileState(this.state, {this.response, this.error});
}

class AddPortfolioState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddPortfolioState(this.state, {this.response, this.error});
}

class AddExperienceEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddExperienceEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class TopScoringUserEvent extends HomeEvent {
  int? userId;
  bool? skipCurrentUser;

  TopScoringUserEvent({this.userId, this.skipCurrentUser = true})
      : super([userId]);

  List<Object> get props => throw UnimplementedError();
}

class ZoomOpenUrlEvent extends HomeEvent {
  int? contentId;

  ZoomOpenUrlEvent({this.contentId}) : super([contentId]);

  List<Object> get props => throw UnimplementedError();
}

class AddExperienceState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddExperienceState(this.state, {this.response, this.error});
}

class AddEducationEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddEducationEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class AddPortfolioProfileEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddPortfolioProfileEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class AddProfolioProfileState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddProfolioProfileState(this.state, {this.response, this.error});
}

class AddEducationState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddEducationState(this.state, {this.response, this.error});
}

class AddActivitiesEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddActivitiesEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class AddActivitiesState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddActivitiesState(this.state, {this.response, this.error});
}

class AddCertificateEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddCertificateEvent({this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class AddCertificateState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddPortfolioResp? response;
  String? error;

  AddCertificateState(this.state, {this.response, this.error});
}

class GenerateCertificateEvent extends HomeEvent {
  int? certificateId;
  String? programName;
  int? contentId;
  GenerateCertificateEvent(
      {this.certificateId, this.programName, this.contentId})
      : super([certificateId, programName, contentId]);

  List<Object> get props => throw UnimplementedError();
}

class GenerateCertificateState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  GenerateCertificateResponse? response;
  String? error;

  GenerateCertificateState(this.state, {this.response, this.error});
}

class PopularJobIntershipEvent extends HomeEvent {
  PopularJobIntershipEvent() : super([]);
  List<Object> get props => throw UnimplementedError();
}

class PopularIntershipEvent extends HomeEvent {
  PopularIntershipEvent() : super([]);
  List<Object> get props => throw UnimplementedError();
}

class TopCompaniesEvent extends HomeEvent {
  TopCompaniesEvent() : super([]);
  List<Object> get props => throw UnimplementedError();
}

class CompanyJobsEvent extends HomeEvent {
  CompanyJobsEvent() : super([]);
  List<Object> get props => throw UnimplementedError();
}

class SkillSuggestionEvent extends HomeEvent {
  SkillSuggestionEvent() : super([]);
  List<Object> get props => throw UnimplementedError();
}

class AddSkillEvent extends HomeEvent {
  Map<String, dynamic>? data;

  AddSkillEvent({this.data}) : super([data]);
  List<Object> get props => throw UnimplementedError();
}

class SkillSuggestionState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SkillSuggestionResponse? response;
  String? error;

  SkillSuggestionState(this.state, {this.response, this.error});
}

class AddSkillState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AddSkillResponse? response;
  String? error;

  AddSkillState(this.state, {this.response, this.error});
}

class CompanyJobsListEvent extends HomeEvent {
  String? name;
  CompanyJobsListEvent({this.name}) : super([name]);

  List<Object> get props => throw UnimplementedError();
}

class PopularJobIntershipState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompanyJobListResponse? response;
  String? error;

  PopularJobIntershipState(this.state, {this.response, this.error});
}

class PopularIntershipState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompanyJobListResponse? response;
  String? error;

  PopularIntershipState(this.state, {this.response, this.error});
}

class CompanyJobsState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompanyJobListResponse? response;
  String? error;

  CompanyJobsState(this.state, {this.response, this.error});
}

class CompanyJobsListState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompanyJobListResponse? response;
  String? error;

  CompanyJobsListState(this.state, {this.response, this.error});
}

class TopCompaniesState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CompanyListResponse? response;
  String? error;

  TopCompaniesState(this.state, {this.response, this.error});
}

class WowDashboardEvent extends HomeEvent {
  WowDashboardEvent() : super();
  List<Object> get props => throw UnimplementedError();
}

class WowDashboardState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  WowDashboardResponse? response;
  String? error;

  WowDashboardState(this.state, {this.response, this.error});
}

class ListVideoResumeState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ListVideoResumeResponse? response;
  String? error;
  bool? isRefresh;
  int? index;

  ListVideoResumeState(this.state,
      {this.response, this.error, this.isRefresh, this.index});
}

class DeleteVideoResumeEvent extends HomeEvent {
  int videoIndex;
  bool isSetPrimary;

  DeleteVideoResumeEvent({required this.videoIndex, required this.isSetPrimary})
      : super([videoIndex, isSetPrimary]);
  List<Object> get props => throw UnimplementedError();
}

class DeleteVideoResumeState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  DeleteVideoResumeResponse? response;
  bool? isSetPrimary;

  String? error;

  DeleteVideoResumeState(this.state,
      {this.response, this.error, this.isSetPrimary});
}

class AnalyseVideoResumeEvent extends HomeEvent {
  String videoText;
  int index;

  AnalyseVideoResumeEvent({required this.videoText, required this.index})
      : super([videoText]);
  List<Object> get props => throw UnimplementedError();
}

class AnalyseVideoResumeState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AnalyseVideoResumeResponse? response;
  bool? isSetPrimary;

  String? error;

  AnalyseVideoResumeState(this.state,
      {this.response, this.error, this.isSetPrimary});
}

class GenerateSimilarityState extends HomeState {
  ApiStatus state;

  ApiStatus get apiState => state;
  String? error;

  GenerateSimilarityState(this.state, {this.error});
}

class GenerateSimilarityEvent extends HomeEvent {
  int? submissionId;

  GenerateSimilarityEvent({required this.submissionId}) : super([]);

  List<Object> get props => throw UnimplementedError();
}

class ListVideoResumeEvent extends HomeEvent {
  int? index;
  bool? isRefresh;
  ListVideoResumeEvent(this.index, {this.isRefresh = false})
      : super([index, isRefresh]);
  List<Object> get props => throw UnimplementedError();
}


class DownloadAssessmentReportState extends HomeState {
  ApiStatus state;
  ApiStatus get apiState => state;
  DownloadAssessmentReportResponse? response;
  String? error;

  DownloadAssessmentReportState(this.state, {this.response, this.error});
}

class DownloadAssessmentReportEvent extends HomeEvent {
  int? programId;
  int? contentId;
  int? userId;

  DownloadAssessmentReportEvent({this.programId, this.contentId, this.userId})
      : super([programId, contentId, userId]);
  List<Object> get props => throw UnimplementedError();
}


//add New
class FeeAgreementState extends HomeState {
  ApiStatus state;
  ApiStatus get apiState => state;
  FeeAgreementResponse? response;
  String? error;

  FeeAgreementState(this.state, {this.response, this.error});
}

class FeeAgreementEvent extends HomeEvent {
  FeeAgreementEvent(): super([]);
  List<Object> get props => throw UnimplementedError();
}

class AcceptFeeAgreementState extends HomeState {
  ApiStatus state;
  ApiStatus get apiState => state;
  AcceptFeeAgreementResponse? response;
  String? error;

  AcceptFeeAgreementState(this.state, {this.response, this.error});
}

class AcceptFeeAgreementEvent extends HomeEvent {
  int? isAccepted;

  AcceptFeeAgreementEvent({this.isAccepted}): super([isAccepted]);
  List<Object> get props => throw UnimplementedError();
}



class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final homeRepository = Injector.appInstance.get<HomeRepository>();

  HomeBloc(HomeState initialState) : super(initialState) {
    on<PiDetailEvent>((event, emit) async {
      try {
        emit(PiDetailState(ApiStatus.LOADING));
        final response = await homeRepository.piDetail(event.userId!);
        if (response.status == 1) {
          emit(PiDetailState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(PiDetailState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(PiDetailState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<ListVideoResumeEvent>((event, emit) async {
      try {
        emit(ListVideoResumeState(ApiStatus.LOADING,
            isRefresh: event.isRefresh));
        final response = await homeRepository.listVideoResume(event.index);
        log("rep ge ${response.data} and ${event.index}");

        if (response.status == 1) {
          log("rep ge");
          emit(ListVideoResumeState(ApiStatus.SUCCESS,
              response: response,
              isRefresh: event.isRefresh,
              index: event.index));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(ListVideoResumeState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong,
              isRefresh: event.isRefresh,
              index: event.index));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(ListVideoResumeState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong,
            isRefresh: event.isRefresh,
            index: event.index));
      }
    }, transformer: concurrent());

    on<AnalyseVideoResumeEvent>((event, emit) async {
      try {
        emit(AnalyseVideoResumeState(ApiStatus.LOADING));
        final response = await homeRepository.anlyseVideoResume(event);
        if (response.status == 1) {
          emit(AnalyseVideoResumeState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(AnalyseVideoResumeState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AnalyseVideoResumeState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<DeleteVideoResumeEvent>((event, emit) async {
      try {
        emit(DeleteVideoResumeState(ApiStatus.LOADING));
        final response = await homeRepository.deleteVideoResume(event);
        if (response.status == 1) {
          emit(DeleteVideoResumeState(ApiStatus.SUCCESS,
              response: response, isSetPrimary: event.isSetPrimary));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(DeleteVideoResumeState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong,
              isSetPrimary: event.isSetPrimary));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(DeleteVideoResumeState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong,
            isSetPrimary: event.isSetPrimary));
      }
    }, transformer: concurrent());

    on<SkillSuggestionEvent>((event, emit) async {
      try {
        emit(SkillSuggestionState(ApiStatus.LOADING));
        final response = await homeRepository.getSkillSuggestion();
        if (response.status == 1) {
          emit(SkillSuggestionState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(SkillSuggestionState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(SkillSuggestionState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<SkillRatingEvent>((event, emit) async {
      try {
        emit(SkillRatingState(ApiStatus.LOADING));
        final response = await homeRepository.getSkillRating();
        if (response.status == 1) {
          emit(SkillRatingState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(SkillRatingState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(SkillRatingState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<AddSkillEvent>((event, emit) async {
      try {
        emit(AddSkillState(ApiStatus.LOADING));
        final response = await homeRepository.addSkill(event.data!);
        if (response.status == 1) {
          emit(AddSkillState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(AddSkillState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AddSkillState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<PopularJobIntershipEvent>((event, emit) async {
      try {
        emit(PopularJobIntershipState(ApiStatus.LOADING));
        final response = await homeRepository.popularjobInternship();
        if (response.status == 1) {
          emit(PopularJobIntershipState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(PopularJobIntershipState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(PopularJobIntershipState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<PopularIntershipEvent>((event, emit) async {
      try {
        emit(PopularIntershipState(ApiStatus.LOADING));
        final response = await homeRepository.popularInternship();
        if (response.status == 1) {
          emit(PopularIntershipState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(PopularIntershipState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(PopularIntershipState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<CompanyJobsListEvent>((event, emit) async {
      try {
        emit(CompanyJobsListState(ApiStatus.LOADING));
        final response = await homeRepository.companyJobs(event.name!);
        if (response.status == 1) {
          emit(CompanyJobsListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(CompanyJobsListState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(CompanyJobsListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<AssessmentDetailsEvent>((event, emit) async {
      try {
        emit(AssessmentDetailsState(ApiStatus.LOADING));
        final response = await homeRepository.getAssessmentDetails(
            contentId: event.contentId);
        if (response.status == 1) {
          emit(AssessmentDetailsState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(AssessmentDetailsState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AssessmentDetailsState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());

    on<AssessmentCertificateEvent>((event, emit) async {
      try {
        emit(AssessmentCertificateState(ApiStatus.LOADING));
        final response = await homeRepository.getAssessmentCertificate(
            certificateId: event.certificateId, contentId: event.contentId);
        if (response.status == 1) {
          emit(AssessmentCertificateState(ApiStatus.SUCCESS,
              response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(AssessmentCertificateState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AssessmentCertificateState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());

    on<TopCompaniesEvent>((event, emit) async {
      try {
        emit(TopCompaniesState(ApiStatus.LOADING));
        final response = await homeRepository.topCompanies();
        if (response.status == 1) {
          emit(TopCompaniesState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(TopCompaniesState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(TopCompaniesState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<FacultyBatchDetailsEvent>((event, emit) async {
      Log.v("status DATA now check ::: 1");

      try {
        emit(FacultyBatchDetailsState(ApiStatus.LOADING));
        Log.v("status DATA now check ::: 2");

        try {
          final response =
              await homeRepository.getFacultyBatchDetails(event.courseId);
          Log.v("status DATA now check ::: valiue ${response?.toJson()}");
          Log.v("status DATA now check ::: valiue  now ${response?.status}");
          emit(FacultyBatchDetailsState(ApiStatus.SUCCESS, response: response));

          // if (response?.status == 1) {

          //   emit(FacultyBatchDetailsState(ApiStatus.SUCCESS, response: response));
          // } else {
          //   Log.v("ERRyyyOR DATA ::: $response");
          //   emit(FacultyBatchDetailsState(ApiStatus.ERROR,
          //       error: Strings.somethingWentWrong));
          // }
        } catch (e, stackTrace) {
          print("status DATA now check $e");
          print("status DATA now check $stackTrace");
        }
      } catch (e, Stacktrace) {
        Log.v("ERROR DATA : $e ,$Stacktrace");
        emit(FacultyBatchDetailsState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<FacultyCourseDetailsEvent>((event, emit) async {
      Log.v("status DATA now check ::: 1");

      try {
        emit(FacultyCourseDetailsState(ApiStatus.LOADING));
        Log.v("status DATA now check ::: 2");

        try {
          final response = await homeRepository.getFacultyCourseDetails(
              programId: event.programId);
          Log.v("status DATA now check ::: valiue ${response?.toJson()}");
          Log.v("status DATA now check ::: valiue  now ${response?.status}");
          // emit(FacultyCourseDetailsState(ApiStatus.SUCCESS, response: response));

          if (response?.status == 1) {
            emit(FacultyCourseDetailsState(ApiStatus.SUCCESS,
                response: response));
          } else {
            Log.v("ERRyyyOR DATA ::: $response");
            emit(FacultyCourseDetailsState(ApiStatus.ERROR,
                error: Strings.somethingWentWrong));
          }
        } catch (e, stackTrace) {
          print("status DATA now check $e");
          print("status DATA now check $stackTrace");
        }
      } catch (e, Stacktrace) {
        Log.v("ERROR DATA : $e ,$Stacktrace");
        emit(FacultyCourseDetailsState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<MatchingJobsEvent>((event, emit) async {
      Log.v("status DATA now check ::: 1");

      try {
        emit(MatchingJobsState(ApiStatus.LOADING));
        Log.v("status DATA now check ::: 2");

        try {
          final response = await homeRepository.getMatchingJobs();
          Log.v("status DATA now check ::: valiue ${response?.toJson()}");
          Log.v("status DATA now check ::: valiue  now ${response?.status}");
          // emit(MatchingJobsState(ApiStatus.SUCCESS, response: response));

          if (response?.status == 1) {
            emit(MatchingJobsState(ApiStatus.SUCCESS, response: response));
          } else {
            Log.v("ERRyyyOR DATA ::: $response");
            emit(MatchingJobsState(ApiStatus.ERROR,
                error: Strings.somethingWentWrong));
          }
        } catch (e, stackTrace) {
          print("status DATA now check $e");
          print("status DATA now check $stackTrace");
        }
      } catch (e, Stacktrace) {
        Log.v("ERROR DATA : $e ,$Stacktrace");
        emit(MatchingJobsState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<FacultyBatchClassEvent>((event, emit) async {
      try {
        emit((FacultyBatchClassState(ApiStatus.LOADING)));
        final response = await homeRepository.getFacultyBatchClass(
            selectedDate: event.selectedDate);
        if (response.status == 1) {
          emit(FacultyBatchClassState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(FacultyBatchClassState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(FacultyBatchClassState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<WowDashboardEvent>((event, emit) async {
      try {
        emit((WowDashboardState(ApiStatus.LOADING)));
        final response = await homeRepository.getWowDashboard();
        if (response.status == 1) {
          emit(WowDashboardState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(WowDashboardState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(WowDashboardState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<FacultyBatchAssignmentEvent>((event, emit) async {
      try {
        emit((FacultyBatchAssignmentState(ApiStatus.LOADING)));
        final response = await homeRepository.getFacultyBatchAssignment(
            courseId: event.courseId, selectedDate: event.selectedDate);
        if (response.status == 1) {
          emit(FacultyBatchAssignmentState(ApiStatus.SUCCESS,
              response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(FacultyBatchAssignmentState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(FacultyBatchAssignmentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<FacultyModuleListEvent>((event, emit) async {
      try {
        emit((FacultyModuleListState(ApiStatus.LOADING)));
        final response =
            await homeRepository.getFacultyModuleList(courseId: event.courseId);
        if (response.status == 1) {
          emit(FacultyModuleListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(FacultyModuleListState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(FacultyModuleListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<ModuleLeaderProgramListEvent>((event, emit) async {
      try {
        emit((ModuleLeaderProgramListState(ApiStatus.LOADING)));
        final response = await homeRepository.getModuleLeaderProgramList(
            courseId: event.courseId);
        if (response.status == 1) {
          emit(ModuleLeaderProgramListState(ApiStatus.SUCCESS,
              response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(ModuleLeaderProgramListState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(ModuleLeaderProgramListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<HodProgramListEvent>((event, emit) async {
      try {
        emit((HodProgramListtState(ApiStatus.LOADING)));
        final response = await homeRepository.getHodModuleList();
        if (response.status == 1) {
          emit(HodProgramListtState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(HodProgramListtState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(HodProgramListtState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<FacultyBatchAssessmentEvent>((event, emit) async {
      try {
        emit((FacultyBatchAssessmentState(ApiStatus.LOADING)));
        final response = await homeRepository.getFacultyBatchAssessment(
            courseId: event.courseId, selectedDate: event.selectedDate);
        if (response.status == 1) {
          emit(FacultyBatchAssessmentState(ApiStatus.SUCCESS,
              response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(FacultyBatchAssessmentState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(FacultyBatchAssessmentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<AttendancePercentageEvent>((event, emit) async {
      try {
        emit((AttendancePercentageState(ApiStatus.LOADING)));
        final response = await homeRepository.getAttendancePercentage(
            programCompletionId: event.programCompletionId);
        if (response.status == 1) {
          emit(
              AttendancePercentageState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(AttendancePercentageState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AttendancePercentageState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<ProgramCompletionEvent>((event, emit) async {
      try {
        emit((ProgramCompletionState(ApiStatus.LOADING)));
        final response = await homeRepository.getProgramCompletion(
            programCompId: event.programCompletionId);
        if (response?.status == 1) {
          emit(ProgramCompletionState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(ProgramCompletionState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(ProgramCompletionState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<MarkAttendanceEvent>((event, emit) async {
      try {
        emit((MarkAttendanceState(ApiStatus.LOADING)));
        final response = await homeRepository.getMarkAttendance(
            batchId: event.batchId, classId: event.classId);
        if (response.status == 1) {
          emit(MarkAttendanceState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(MarkAttendanceState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(MarkAttendanceState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<UpdateAttendanceEvent>((event, emit) async {
      try {
        emit((UpdateAttendanceState(ApiStatus.LOADING)));
        final response = await homeRepository.getUpdateAttendance(
            contentId: event.contentId,
            attendance: event.attendance,
            users: event.users);
        if (response.status == 1) {
          emit(UpdateAttendanceState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(UpdateAttendanceState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(UpdateAttendanceState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<DomainFilterListEvent>((event, emit) async {
      try {
        emit(DomainFilterListState(ApiStatus.LOADING));
        final response = await homeRepository.getFilterDomainList(event.ids!);

        if (response.data != null) {
          emit(DomainFilterListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v(" domain filter   ERROR DATA ::: $response");
          emit(DomainFilterListState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());

    on<getLiveClassEvent>((event, emit) async {
      try {
        emit(getLiveClassState(ApiStatus.LOADING));
        final response = await homeRepository.getLiveClasses();
        if (response.data != null) {
          emit(getLiveClassState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(getLiveClassState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(getLiveClassState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GenerateCertificateEvent>((event, emit) async {
      try {
        emit(GenerateCertificateState(ApiStatus.LOADING));
        final response = await homeRepository.generateCertificate(
            event.certificateId!, event.contentId!, event.programName!);
        if (response != null) {
          emit(GenerateCertificateState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GenerateCertificateState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GenerateCertificateState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<JobDomainDetailEvent>((event, emit) async {
      try {
        emit(JobDomainDetailState(ApiStatus.LOADING));
        final response = await homeRepository.jobDomainDetail(event.domainId!);
        Log.v("top scoring resume DATA ::: ${response.data}");

        if (response.data != null) {
          emit(JobDomainDetailState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("top scoring   ERROR DATA ::: $response");
          emit(JobDomainDetailState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<DomainListEvent>((event, emit) async {
      try {
        emit(DomainListState(ApiStatus.LOADING));
        final response = await homeRepository.getDomainList();
        Log.v("top scoring resume DATA ::: ${response.data}");

        if (response.data != null) {
          emit(DomainListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("top scoring   ERROR DATA ::: $response");
          emit(DomainListState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<TopScoringUserEvent>((event, emit) async {
      try {
        emit(TopScoringUserState(ApiStatus.LOADING));
        final response = await homeRepository.topScoringUser(
            userId: event.userId, skipCurrentUser: event.skipCurrentUser);
        Log.v("top scoring resume DATA ::: ${response?.data?.length}");

        if (response?.data != null) {
          emit(TopScoringUserState(ApiStatus.SUCCESS, response: response!));
        } else {
          Log.v("top scoring   ERROR DATA ::: $response");
          emit(TopScoringUserState(ApiStatus.ERROR, response: response!));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<PortfolioCompetitoinEvent>((event, emit) async {
      try {
        emit(PortfoilioCompetitionState(ApiStatus.LOADING));
        final response =
            await homeRepository.getPortfolioCompetition(event.userId);
        Log.v("Add Social resume DATA ::: ${response?.data}");

        if (response?.data != null) {
          emit(PortfoilioCompetitionState(ApiStatus.SUCCESS,
              response: response!));
        } else {
          Log.v("Add social   ERROR DATA ::: $response");
          emit(
              PortfoilioCompetitionState(ApiStatus.ERROR, response: response!));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<AddSocialEvent>((event, emit) async {
      try {
        emit(AddSocialState(ApiStatus.LOADING));
        final response = await homeRepository.addSocial(data: event.data);
        Log.v("Add Social resume DATA ::: ${response?.data}");

        if (response?.data != null) {
          emit(AddSocialState(ApiStatus.SUCCESS, response: response!));
        } else {
          Log.v("Add social   ERROR DATA ::: $response");
          emit(AddSocialState(ApiStatus.ERROR, response: response!));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<UploadProfileEvent>((event, emit) async {
      try {
        emit(UploadProfileState(ApiStatus.LOADING));
        final response = await homeRepository.uploadProfile(data: event.data);
        Log.v("Add PORTFOLIO resume DATA ::: ${response?.data}");

        if (response?.data != null) {
          emit(UploadProfileState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("PORTFOLIO resume  ERROR DATA ::: $response");
          emit(UploadProfileState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<AddResumeEvent>((event, emit) async {
      try {
        emit(AddResumeState(ApiStatus.LOADING));
        final response = await homeRepository.addResume(data: event.data);
        Log.v("Add PORTFOLIO resume DATA ::: ${response.data}");

        if (response.data != null) {
          emit(AddResumeState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("PORTFOLIO resume  ERROR DATA ::: $response");
          emit(AddResumeState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());

    on<AddPortfolioProfileEvent>((event, emit) async {
      try {
        emit(AddProfolioProfileState(ApiStatus.LOADING));
        final response =
            await homeRepository.addPortfolioProfile(data: event.data);
        Log.v("Add PORTFOLIO Profile DATA ::: ${response.data}");

        if (response.data != null) {
          emit(AddProfolioProfileState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("PORTFOLIO Profile  ERROR DATA ::: $response");
          emit(AddProfolioProfileState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<AddPortfolioEvent>((event, emit) async {
      try {
        emit(AddPortfolioState(ApiStatus.LOADING));
        final response = await homeRepository.addPortfolio(data: event.data);
        Log.v("Add PORTFOLIO DATA ::: ${response.data}");

        if (response.data != null) {
          emit(AddPortfolioState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("Add ERROR DATA ::: $response");
          emit(AddPortfolioState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<PortfolioEvent>((event, emit) async {
      print('new portfolio data event is ');
      try {
        emit(PortfolioState(ApiStatus.LOADING));
        final response = await homeRepository.getPortfolio(event.userId);
        Log.v("PORTFOLIO DATA home ::: ${response?.data.toJson()}");

        if (response?.data != null) {
          emit(PortfolioState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(PortfolioState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        print('the exception is $e');
      }
    }, transformer: concurrent());

    on<SingularisDeletePortfolioEvent>((event, emit) async {
      try {
        emit(SingularisDeletePortfolioState(ApiStatus.LOADING));
        final response =
            await homeRepository.singularisPortfolioDelete(event.portfolioId!);
        Log.v(" Delete PORTFOLIO DATA ::: ${response.data}");

        emit(SingularisDeletePortfolioState(ApiStatus.SUCCESS,
            response: response));
      } catch (e) {}
    }, transformer: concurrent());

    on<AddActivitiesEvent>((event, emit) async {
      try {
        emit(AddActivitiesState(ApiStatus.LOADING));
        final response = await homeRepository.addProfessional(data: event.data);
        Log.v("ACTIVITIES DATA ::: ${response.data}");

        if (response.data != null) {
          emit(AddActivitiesState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(AddActivitiesState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {}
    }, transformer: concurrent());
    on<ZoomOpenUrlEvent>((event, emit) async {
      try {
        emit(ZoomOpenUrlState(ApiStatus.LOADING));
        final response = await homeRepository.getZoomOpenUrl(
          event.contentId!,
        );

        emit(ZoomOpenUrlState(ApiStatus.SUCCESS, response: response));
      } catch (e, stacktrace) {
        Log.v(stacktrace);
      }
    }, transformer: concurrent());

    on<CompetitionContentListEvent>((event, emit) async {
      try {
        log('enter in loading state');
        emit(AppJobListCompeState(ApiStatus.LOADING));
        final response = await homeRepository.getCompetitionContentListNew(
            event.competitionId, event.isApplied);
        Log.v("MY DA DATA hkjsdhfjksdf ::: ${response.data}");
        int notCheck = 1;
        //if (response.data != null) {
        if (notCheck == 1) {
          emit(AppJobListCompeState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(AppJobListCompeState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        Log.v("Expection DATA  : $e");
        emit(AppJobListCompeState(ApiStatus.ERROR));
      }
    }, transformer: concurrent());

    //for Explore Job List
    on<ExploreJobListEvent>((event, emit) async {
      try {
        emit(ExploreJobListState(ApiStatus.LOADING));
        final response = await homeRepository.getExploreJobList(
            indexNo: event.indexNo,
            doRefresh: event.doRefresh,
            resumeUrl: event.resumeUrl);
        Log.v("MY DA DATA ::: ${response.data}");
        int notCheck = 1;
        if (notCheck == 1) {
          emit(ExploreJobListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(ExploreJobListState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        Log.v("Expection DATA  : $e");
        emit(ExploreJobListState(ApiStatus.ERROR));
      }
    }, transformer: concurrent());
    on<AssessmentReportEvent>((event, emit) async {
      try {
        emit(AssessmentReportState(ApiStatus.LOADING));
        final response = await homeRepository.getAssessmentReports(
            assessmentId: event.assessmentId);
        if (response?.data != null) {
          emit(AssessmentReportState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(AssessmentReportState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AssessmentReportState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<AssignLearnerEvent>((event, emit) async {
      try {
        emit(AssignLearnerState(ApiStatus.LOADING));
        final response = await homeRepository.getAssignLearner(
            isFaculty: event.isFaculty, programId: event.programId);
        if (response?.data != null) {
          emit(AssignLearnerState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(AssignLearnerState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AssignLearnerState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<ExploreJobDetailsEvent>((event, emit) async {
      try {
        emit(ExploreJobDetailsState(ApiStatus.LOADING));
        final response = await homeRepository.getExploreJobDetails(event.jobId);
        if (response!.data != null) {
          emit(ExploreJobDetailsState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(ExploreJobDetailsState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        Log.v("Expection DATA  : $e");
        emit(ExploreJobDetailsState(ApiStatus.ERROR));
      }
    }, transformer: concurrent());

    on<LeaderboardEvent>((event, emit) async {
      try {
        emit(LeaderboardState(ApiStatus.LOADING));
        final response = await homeRepository.getLeaderboard(
            event.id, event.type, event.skipotherUser, event.skipotherUser);
        Log.v("MY DA DATA ::: ${response.data}");

        emit(LeaderboardState(ApiStatus.SUCCESS, response: response));
      } catch (e) {
        Log.v("Expection DATA  : $e");
        emit(LeaderboardState(ApiStatus.ERROR));
      }
    }, transformer: concurrent());
    on<AnnouncementContentEvent>((event, emit) async {
      try {
        emit(AnnouncementContentState(ApiStatus.LOADING));
        final response = await homeRepository.getContentList(
          contentType: event.contentType,
        );
        if (response.data != null) {
          emit(AnnouncementContentState(ApiStatus.SUCCESS,
              response: response, contentType: event.contentType));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(AnnouncementContentState(ApiStatus.ERROR,
              error: response.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(AnnouncementContentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<SubmitPollEvent>((event, emit) async {
      try {
        emit(PollSubmitState(ApiStatus.LOADING));
        final response = await homeRepository.submitPoll(
            submitSurveyReq: event.submitPollReq);
        if (response != null) {
          emit(PollSubmitState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(PollSubmitState(ApiStatus.ERROR, error: response?.message));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(PollSubmitState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<SubmitSurveyEvent>((event, emit) async {
      try {
        emit(SurveySubmitState(ApiStatus.LOADING));
        final response = await homeRepository.submitSurvey(
            submitSurveyReq: event.submitSurveyReq);
        emit(SurveySubmitState(ApiStatus.SUCCESS, response: response));
      } catch (e, s) {
        Log.v("ERROR DATA : $s");
        emit(SurveySubmitState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<RemoveAccountEvent>((event, emit) async {
      try {
        emit(RemoveAccountState(ApiStatus.LOADING));
        final response = await homeRepository.removeAccount(type: event.type);
        emit(RemoveAccountState(ApiStatus.SUCCESS, response: response));
      } catch (e, s) {
        Log.v("ERROR DATA : $s");
        emit(RemoveAccountState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<SurveyDataEvent>((event, emit) async {
      try {
        emit(SurveyDataState(ApiStatus.LOADING));
        final response = await homeRepository.getSurveyDataList(
            contentId: event.contentId, type: event.type);
        if (response != null) {
          emit(SurveyDataState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(SurveyDataState(ApiStatus.ERROR, error: response?.error![0]));
        }
      } catch (e, s) {
        Log.v("ERROR DATA : $s");
        emit(SurveyDataState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<NotificationListEvent>((event, emit) async {
      try {
        emit(NotificationState(ApiStatus.LOADING));
        final response = await homeRepository.getNotifications();
        if (response?.data != null) {
          emit(NotificationState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(NotificationState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(NotificationState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<NotificationsListEvent>((event, emit) async {
      try {
        event.isInitial
            ? emit(NotificationsListState(NotificationStatus.loading))
            : emit(NotificationsListState(NotificationStatus.loadingMore));
        final response = await homeRepository.getNotificationsList(
            fromVal: event.fromValue, toVal: event.toValue);
        if (response?.data != null) {
          emit(NotificationsListState(NotificationStatus.success,
              response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(NotificationsListState(NotificationStatus.error,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(NotificationsListState(NotificationStatus.error,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<NotificationReadEvent>((event, emit) async {
      try {
        emit(NotificationsReadState(ApiStatus.LOADING));
        final response = await homeRepository.getNotificationsRead(
            id: event.id,
            notiId: event.notiId,
            type: event.type,
            isRead: event.isRead);
        if (response?.data != null) {
          emit(NotificationsReadState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(NotificationsReadState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(NotificationsReadState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<ActivityAttemptEvent>((event, emit) async {
      try {
        emit(ActivityAttemptState(ApiStatus.LOADING));
        final response = await homeRepository.activityAttempt(
            filePath: event.filePath,
            contentType: event.contentType,
            contentId: event.contentId);
        if (response?.status == 1) {
          emit(ActivityAttemptState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(ActivityAttemptState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(ActivityAttemptState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<TrackAnnouncementEvent>((event, emit) async {
      try {
        emit(TrackAnnouncementState(ApiStatus.LOADING));
        final response = await homeRepository.trackAnnouncment(
            trackAnnouncementReq: event.rewardReq);
        if (response?.status == 1) {
          emit(TrackAnnouncementState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(TrackAnnouncementState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(TrackAnnouncementState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<LibraryContentEvent>((event, emit) async {
      try {
        emit(LibraryContentState(ApiStatus.LOADING));
        final response =
            await homeRepository.getContentList(contentType: event.contentType);
        if (response.data != null) {
          emit(LibraryContentState(ApiStatus.SUCCESS,
              response: response, contentType: event.contentType));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(LibraryContentState(ApiStatus.ERROR, error: response.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(LibraryContentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<ContentTagsEvent>((event, emit) async {
      try {
        emit(ContentTagsState(ApiStatus.LOADING));
        final response = await homeRepository.getContentTagsList(
            categoryType: event.categoryType);
        if (response?.data != null) {
          emit(ContentTagsState(ApiStatus.SUCCESS,
              response: response, contentType: event.categoryType));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(ContentTagsState(ApiStatus.ERROR, error: response?.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(ContentTagsState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<FeedbackEvent>((event, emit) async {
      try {
        emit(FeedbackState(ApiStatus.LOADING));
        final response = await homeRepository.getFeedbackList();
        if (response!.data != null) {
          emit(FeedbackState(ApiStatus.SUCCESS,
              response: response, contentType: event.categoryType));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(FeedbackState(ApiStatus.ERROR, error: response.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(FeedbackState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<TopicsEvent>((event, emit) async {
      try {
        emit(TopicsState(ApiStatus.LOADING));
        final response = await homeRepository.getTopicsList();
        if (response.data != null) {
          emit(TopicsState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(TopicsState(ApiStatus.ERROR, error: response.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(TopicsState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<SubmitFeedbackEvent>((event, emit) async {
      try {
        emit(SubmitFeedbackState(ApiStatus.LOADING));
        final response =
            await homeRepository.submitFeedback(feedbackReq: event.feedbackReq);
        emit(SubmitFeedbackState(ApiStatus.SUCCESS, response: response));
      } catch (e, stacktrace) {
        Log.v(stacktrace);
        Log.v("ERROR DATA3 : $e");
        emit(SubmitFeedbackState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetModuleLeaderboardEvent>((event, emit) async {
      try {
        emit(GetModuleLeaderboardState(ApiStatus.LOADING));
        final response = await homeRepository.getModuleLeaderboardList(
            '${event.getCourseModulesReq?.courseId}',
            type: event.type);
        if (response?.data != null) {
          emit(
              GetModuleLeaderboardState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GetModuleLeaderboardState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GetModuleLeaderboardState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetCertificatesEvent>((event, emit) async {
      try {
        emit(GetCertificatesState(ApiStatus.LOADING));
        final response = await homeRepository.getCertificatesList();
        if (response?.data != null) {
          emit(GetCertificatesState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GetCertificatesState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GetCertificatesState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetCourseLeaderboardEvent>((event, emit) async {
      try {
        emit(GetCourseLeaderboardState(ApiStatus.LOADING));
        final response = await homeRepository.getCourseLeaderboardList(
            '${event.getCourseModulesReq?.courseId}',
            type: event.type);
        if (response!.data != null) {
          emit(
              GetCourseLeaderboardState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GetCourseLeaderboardState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GetCourseLeaderboardState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetCourseModulesEvent>((event, emit) async {
      try {
        emit(GetCourseModulesState(ApiStatus.LOADING));
        final response = await homeRepository.getCourseModulesList(
            '${event.getCourseModulesReq?.courseId}',
            type: event.type!);
        if (response?.data != null) {
          emit(GetCourseModulesState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GetCourseModulesState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e, stacktrace) {
        Log.v(stacktrace);
        Log.v("ERROR DATA : $e");
        emit(GetCourseModulesState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetKPIAnalysisEvent>((event, emit) async {
      try {
        emit(GetKPIAnalysisState(ApiStatus.LOADING));
        final response = await homeRepository.getKPIAnalysisList();
        if (response?.data != null) {
          emit(GetKPIAnalysisState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GetKPIAnalysisState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GetKPIAnalysisState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<MyAssessmentEvent>((event, emit) async {
      try {
        emit(MyAssessmentState(ApiStatus.LOADING));
        final response = await homeRepository.getMyAssessmentList(
            interestID: event.interestID,
            jobRoleID: event.jobRoleID,
            skillID: event.skillID);
        if (response.data != null) {
          emit(MyAssessmentState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(MyAssessmentState(ApiStatus.ERROR, error: response.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(MyAssessmentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetCoursesEvent>((event, emit) async {
      try {
        emit(GetCoursesState(ApiStatus.LOADING));
        final response = await homeRepository.getCoursesList(type: event.type);
        if (response?.data != null) {
          emit(GetCoursesState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GetCoursesState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GetCoursesState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<MyAssignmentEvent>((event, emit) async {
      try {
        emit(MyAssignmentState(ApiStatus.LOADING));
        final response = await homeRepository.getMyAssignmentList();
        if (response.data != null) {
          emit(MyAssignmentState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(MyAssignmentState(ApiStatus.ERROR, error: response.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(MyAssignmentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<UserProgramSubscribeEvent>((event, emit) async {
      try {
        emit(UserProgramSubscribeState(ApiStatus.LOADING));
        final response = await homeRepository.subscribeProgram(event.subrReq!);
        if (response.data != null) {
          emit(
              UserProgramSubscribeState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(UserProgramSubscribeState(ApiStatus.ERROR,
              error: response.error![0]));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(UserProgramSubscribeState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<AttemptTestEvent>((event, emit) async {
      try {
        emit(AttemptTestState(ApiStatus.LOADING));
        final response =
            await homeRepository.attemptTest(request: event.request!);
        if (response!.status == 1) {
          emit(AttemptTestState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(AttemptTestState(ApiStatus.ERROR, error: response.error?.first));
        }
      } catch (e) {
        emit(AttemptTestState(ApiStatus.ERROR, error: "Something went wrong"));
      }
    }, transformer: concurrent());
    on<SaveAnswerEvent>((event, emit) async {
      try {
        emit(SaveAnswerState(ApiStatus.LOADING));
        final response =
            await homeRepository.saveAnswer(request: event.request!);
        if (response!.status == 1) {
          emit(SaveAnswerState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(SaveAnswerState(ApiStatus.ERROR, error: "SOMETHING WENT WRONG"));
        }
      } catch (e) {
        emit(SaveAnswerState(ApiStatus.ERROR, error: "Something went wrong"));
      }
    }, transformer: concurrent());
    on<SubmitAnswerEvent>((event, emit) async {
      try {
        emit(SubmitAnswerState(ApiStatus.LOADING));
        final response =
            await homeRepository.submitAnswer(request: event.request);
        if (response!.status == 1) {
          emit(SubmitAnswerState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(SubmitAnswerState(ApiStatus.ERROR,
              error: "Something went wrong"));
        }
      } catch (e) {
        emit(SubmitAnswerState(ApiStatus.ERROR, error: "Something went wrong"));
      }
    }, transformer: concurrent());
    on<EmailCodeSendEvent>((event, emit) async {
      try {
        emit(EmailCodeSendState(ApiStatus.LOADING));
        final response = await homeRepository.emailCodeSend(
            email: event.email,
            isSignup: event.isSignup,
            forgotPass: event.forgotPass);
        if (response == 1) {
          emit(EmailCodeSendState(ApiStatus.SUCCESS));
        } else {
          emit(EmailCodeSendState(ApiStatus.ERROR, error: response));
        }
      } catch (e) {
        emit(EmailCodeSendState(ApiStatus.ERROR,
            error: "Something went wrong "));
      }
    }, transformer: concurrent());

    on<ParticipateEvent>((event, emit) async {
      try {
        emit(ParticipateState(ApiStatus.LOADING));
        final response = await homeRepository.getParticipate(
            name: event.name,
            email: event.email,
            mobileNo: event.mobileNo,
            programId: event.programId,
            isMobile: event.isMobile,
            countryCode: event.countryCode);
        if (response == 1) {
          emit(ParticipateState(ApiStatus.SUCCESS));
        } else {
          emit(
              ParticipateState(ApiStatus.ERROR, error: 'something went wrong'));
        }
      } catch (e) {
        emit(ParticipateState(ApiStatus.ERROR, error: "Something went wrong "));
      }
    }, transformer: concurrent());

    on<VerifyEmailCodeEvent>((event, emit) async {
      try {
        emit(VerifyEmailCodeState(ApiStatus.LOADING));
        final response = await homeRepository.verifyEmailCodeAnswer(
            email: event.email, eCode: event.code);

        if (response == 1) {
          emit(VerifyEmailCodeState(ApiStatus.SUCCESS));
        } else if (response == 2) {
          emit(VerifyEmailCodeState(ApiStatus.ERROR, error: 'sis_redirection'));
        } else {
          emit(VerifyEmailCodeState(ApiStatus.ERROR, error: "Invalid Code"));
        }
      } catch (e) {
        emit(VerifyEmailCodeState(ApiStatus.ERROR,
            error: "Something went wrong"));
      }
    }, transformer: concurrent());
    on<PasswordUpdateEvent>((event, emit) async {
      try {
        emit(PasswordUpdateState(ApiStatus.LOADING));
        // final response =
        await homeRepository.passwordUpdate(
            email: event.email, pass: event.pass, locale: event.locale);

        emit(PasswordUpdateState(ApiStatus.SUCCESS));
      } catch (e) {
        emit(PasswordUpdateState(ApiStatus.ERROR,
            error: "Something went wrong"));
      }
    }

        ///
        , transformer: concurrent());
    on<ReviewTestEvent>((event, emit) async {
      try {
        emit(ReviewTestState(ApiStatus.LOADING));
        final response =
            await homeRepository.reviewTest(request: event.request!);
        if (response!.status == 1) {
          emit(ReviewTestState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(ReviewTestState(ApiStatus.ERROR, error: response.error?.first));
        }
      } catch (e) {
        emit(ReviewTestState(ApiStatus.ERROR, error: "Something went wrong"));
      }
    }, transformer: concurrent());
    on<AssignmentSubmissionsEvent>((event, emit) async {
      try {
        emit(AssignmentSubmissionsState(ApiStatus.LOADING));
        final response =
            await homeRepository.getSubmissions(request: event.request);
        if (response!.status == 1) {
          emit(AssignmentSubmissionsState(ApiStatus.SUCCESS,
              response: response));
        } else {
          emit(AssignmentSubmissionsState(ApiStatus.ERROR,
              error: response.error?.first));
        }
      } catch (e) {
        emit(ReviewTestState(ApiStatus.ERROR, error: "Something went wrong"));
      }
    }, transformer: concurrent());
    on<LanguageEvent>((event, emit) async {
      try {
        emit(LanguageState(ApiStatus.LOADING));
        final response = await homeRepository.getLanguage(event.languageType);
        if (response.data != null) {
          emit(LanguageState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(LanguageState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(LanguageState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<MasterLanguageEvent>((event, emit) async {
      try {
        emit(MasterLanguageState(ApiStatus.LOADING));
        final response = await homeRepository.getMasterLanguage();
        if (response.data != null) {
          emit(MasterLanguageState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA TTT ::: ${response.toJson()}");
          emit(MasterLanguageState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA FFF : $e");
        emit(MasterLanguageState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<JoyCategoryEvent>((event, emit) async {
      try {
        emit(JoyCategoryState(ApiStatus.LOADING));
        final response = await homeRepository.getjoyCategory();
        if (response.data != null) {
          emit(JoyCategoryState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(JoyCategoryState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(JoyCategoryState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<UserJobsListEvent>((event, emit) async {
      try {
        emit(UserJobListState(ApiStatus.LOADING));
        final response = await homeRepository.getUserJobList();
        if (response.list != null) {
          emit(UserJobListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(UserJobListState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(UserJobListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<CompetitionDetailEvent>((event, emit) async {
      try {
        emit(CompetitionDetailState(ApiStatus.LOADING));
        final response =
            await homeRepository.getCompetitionDetail(event.moduleId);
        if (response.status == 1) {
          emit(CompetitionDetailState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(CompetitionDetailState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(CompetitionDetailState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<TrainingDetailEvent>((event, emit) async {
      try {
        emit(TrainingDetailState(ApiStatus.LOADING));
        final response =
            await homeRepository.getTrainingDetail(event.programId);
        if (response.status == 1) {
          emit(TrainingDetailState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(TrainingDetailState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(TrainingDetailState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetCommentEvent>((event, emit) async {
      try {
        emit(GetCommentState(ApiStatus.LOADING));
        final response = await homeRepository.getComment(event.postId);
        if (response.data != null) {
          emit(GetCommentState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GetCommentState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GetCommentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<PostCommentEvent>((event, emit) async {
      try {
        emit(PostCommentState(ApiStatus.LOADING));
        final response = await homeRepository.postComment(
            event.postId, event.parentId, event.comment);
        emit(PostCommentState(ApiStatus.SUCCESS, response: response));
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(PostCommentState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<DashboardIsVisibleEvent>((event, emit) async {
      try {
        emit(DashboardIsVisibleState(ApiStatus.LOADING));
        final response = await homeRepository.getDashboardIsVisible();
        if (response.data != null) {
          emit(DashboardIsVisibleState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(DashboardIsVisibleState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(DashboardIsVisibleState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    // on<DashboardContentEvent>((event, emit) async {
    //   try {
    //     emit(DashboardContentState(ApiStatus.LOADING));
    //     final response = await homeRepository.getDasboardList();
    //     Log.v("my result is f ${response.data?.jobDashboard?.first.companies} ",
    //         name: "DashboardContentEvent");

    //     if (response.data != null) {
    //       emit(DashboardContentState(ApiStatus.SUCCESS, response: response));
    //     } else {
    //       Log.v(
    //           "ERROR DATA ::: ${response.data?.jobDashboard?.first.companies}");
    //       emit(DashboardContentState(ApiStatus.ERROR,
    //           error: Strings.somethingWentWrong));
    //     }
    //   } catch (e) {
    //     Log.v("ERROR DATA : $e");
    //     emit(DashboardContentState(ApiStatus.ERROR,
    //         error: Strings.somethingWentWrong));
    //   }
    // }, transformer: concurrent());
    on<OrganizationProgramListEvent>((event, emit) async {
      try {
        emit(OrganizationProgramListState(ApiStatus.LOADING));

        if (event.fetchGoalList == 1) {
          final responseSetGoal =
              await homeRepository.getGoalInterestArea(event.fetchGoalList);
          emit(OrganizationProgramListState(ApiStatus.SUCCESS,
              responseSetGoal: responseSetGoal));
        } else {
          final response =
              await homeRepository.getOrganizationProgram(event.fetchGoalList);
          emit(OrganizationProgramListState(ApiStatus.SUCCESS,
              response: response));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(OrganizationProgramListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<JoyContentListEvent>((event, emit) async {
      try {
        emit(JoyContentListState(ApiStatus.LOADING));
        final response = await homeRepository.getjoyContentList(event);
        if (response.data != null) {
          emit(JoyContentListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(JoyContentListState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(JoyContentListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<JoyContentByPostIdEvent>((event, emit) async {
      try {
        emit(JoyContentByPostIdState(ApiStatus.LOADING));
        final response =
            await homeRepository.getjoyContentByPostId(postId: event.postId);
        if (response.data != null) {
          emit(JoyContentByPostIdState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(JoyContentByPostIdState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(JoyContentByPostIdState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<ProgramListEvent>((event, emit) async {
      try {
        emit(ProgramListState(ApiStatus.LOADING));
        final response = await homeRepository.getPrograms();
        if (response.data != null) {
          emit(ProgramListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(ProgramListState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(ProgramListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<SemesterListEvent>((event, emit) async {
      try {
        emit(SemesterListState(ApiStatus.LOADING));
        final response = await homeRepository.getSemesterList();
        if (response.data != null) {
          emit(SemesterListState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(SemesterListState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(SemesterListState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<CourseCategoryListIDEvent>((event, emit) async {
      try {
        emit(CourseCategoryListIDState(ApiStatus.LOADING));

        final response = await homeRepository.getCourseWithId(
            id: event.categoryId, semesterID: event.semesterID);

        if (response.data != null) {
          emit(
              CourseCategoryListIDState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: ${response.error?.first}");
          emit(CourseCategoryListIDState(ApiStatus.ERROR, error: response));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(CourseCategoryListIDState(ApiStatus.ERROR,
            error: CourseCategoryListIdResponse()));
      }
    }, transformer: concurrent());
    on<CompetitionListEvent>((event, emit) async {
      if (event.isPopular == false) {
        try {
          emit(CompetitionListState(ApiStatus.LOADING,
              isEventType: event.callMyActivity == false));

          if (event.callMyActivity == true) {
            List<dynamic> response = await Future.wait([
              homeRepository.getCompetitionMyActivity(
                  competitionType: event.competitionType)
            ]);
            emit(CompetitionListState(ApiStatus.SUCCESS,
                myActivity: response[0],
                isEventType: event.callMyActivity == false));
          } else {
            List<dynamic> response = await Future.wait([
              homeRepository.getCompetitionList(
                  false, event.isFilter!, event.ids, event.domainId),
              homeRepository.getCompetitionList(
                  true, event.isFilter!, event.ids, event.domainId),
              homeRepository.getPortfolioCompetition(null),
              homeRepository.getCompetitionMyActivity(
                  competitionType: event.competitionType)
            ]);

            // final response = await homeRepository.getCompetitionList(false);
            emit(CompetitionListState(ApiStatus.SUCCESS,
                competitonResponse: response[0],
                popularCompetitionResponse: response[1],
                competedCompetition: response[2],
                myActivity: response[3],
                isEventType: event.callMyActivity == false));
          }
        } catch (e) {
          Log.v("Exception : $e");
          emit(CompetitionListState(ApiStatus.ERROR,
              isEventType: event.callMyActivity == false,
              error: 'Something went wrong'));
        }
      } else {
        try {
          emit(PopularCompetitionListState(ApiStatus.LOADING));

          final response = await homeRepository.getCompetitionList(
              true, event.isFilter!, event.ids, event.domainId);

          emit(PopularCompetitionListState(ApiStatus.SUCCESS,
              response: response));
        } catch (e) {
          Log.v("Exception : $e");
          emit(PopularCompetitionListState(ApiStatus.ERROR,
              error: 'Something went wrong'));
        }
      }
    }, transformer: concurrent());
    on<CompetitionListFilterEvent>((event, emit) async {
      //if (event.isPopular == false) {
      try {
        emit(CompetitionListFilterState(ApiStatus.LOADING));
        List<dynamic> response = await Future.wait([
          homeRepository.getCompetitionList(
              event.isPopular, event.isFilter!, event.ids, event.domainId)
        ]);
        emit(CompetitionListFilterState(ApiStatus.SUCCESS,
            competitionFilterResponse: response[0]));
      } catch (e) {
        Log.v("Exception : $e");
        emit(CompetitionListFilterState(ApiStatus.ERROR,
            error: 'Something went wrong'));
      }
    }, transformer: concurrent());

    on<JobCompListEvent>((event, emit) async {
      if (event.isPopular == false) {
        try {
          emit(JobCompListState(ApiStatus.LOADING));

          if (event.jobTypeMyJob == false) {
            List<dynamic> response = await Future.wait([
              homeRepository.getJobCompApiList(
                  false,
                  event.isFilter!,
                  event.ids,
                  event.isJob,
                  event.myJob,
                  'allJob',
                  event.domainId),
              homeRepository.getJobCompApiList(false, event.isFilter!,
                  event.ids, event.isJob, event.myJob, 'myJob', event.domainId),
              homeRepository.getJobCompApiList(
                  false,
                  event.isFilter!,
                  event.ids,
                  event.isJob,
                  event.myJob,
                  'recomJob',
                  event.domainId),
            ]);

            emit(JobCompListState(ApiStatus.SUCCESS,
                jobListResponse: response[0],
                myJobListResponse: response[1],
                recommendedJobOpportunities: response[2]));
          } else {
            List<dynamic> response = await Future.wait([
              homeRepository.getJobCompApiList(false, event.isFilter!,
                  event.ids, event.isJob, event.myJob, 'myJob', event.domainId),
            ]);

            emit(JobCompListState(
              ApiStatus.SUCCESS,
              myJobListResponse: response[0],
            ));
          }
        } catch (e) {
          Log.v("Exception : $e");
          emit(
              JobCompListState(ApiStatus.ERROR, error: 'Something went wrong'));
        }
      } else {
        try {
          emit(JobCompListState(ApiStatus.LOADING));
          List<dynamic> response = await Future.wait([
            homeRepository.getJobCompApiList(true, event.isFilter!, event.ids,
                event.isJob, event.myJob, 'dashboard', event.domainId)
          ]);

          emit(JobCompListState(
            ApiStatus.SUCCESS,
            myJobListResponse: response[0],
          ));
        } catch (e) {
          Log.v("Exception : $e");
          emit(
              JobCompListState(ApiStatus.ERROR, error: 'Something went wrong'));
        }
      }
    }, transformer: concurrent());
    on<JobCompListFilterEvent>((event, emit) async {
      try {
        emit(JobCompListFilterState(ApiStatus.LOADING));
        List<dynamic> response = await Future.wait([
          //homeRepository.getJobCompApiList(false, true, event.ids, 0, 0, 'filter', event.domainId)
          homeRepository.getJobCompApiList(
              false, true, event.ids, 0, 0, event.widgetType, event.domainId)
        ]);

        emit(JobCompListFilterState(
          ApiStatus.SUCCESS,
          jobListResponse: response[0],
        ));
      } catch (e) {
        Log.v("Exception : $e");
        emit(JobCompListFilterState(ApiStatus.ERROR,
            error: 'Something went wrong'));
      }
    }, transformer: concurrent());
    on<CourseCategoryList2IDEvent>((event, emit) async {
      try {
        emit(CourseCategoryList2IDState(ApiStatus.LOADING));

        final response =
            await homeRepository.getCourseWithId(id: event.categoryId);

        if (response.data != null) {
          emit(CourseCategoryList2IDState(ApiStatus.SUCCESS,
              response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(CourseCategoryList2IDState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(CourseCategoryList2IDState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<FeaturedVideoEvent>((event, emit) async {
      try {
        emit(FeaturedVideoState(ApiStatus.LOADING));

        final response = await homeRepository.getFeaturedVideo();

        if (response.data != null) {
          emit(FeaturedVideoState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(FeaturedVideoState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(FeaturedVideoState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<InterestEvent>((event, emit) async {
      try {
        emit(InterestState(ApiStatus.LOADING));

        final response = await homeRepository.getInterestPrograms();

        if (response.data != null) {
          emit(InterestState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(InterestState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(InterestState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<MapInterestEvent>((event, emit) async {
      try {
        emit(MapInterestState(ApiStatus.LOADING));

        final response =
            await homeRepository.mapInterest(event.param, event.mapType);

        if (response.data != null) {
          emit(MapInterestState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(MapInterestState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(MapInterestState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<PopularCoursesEvent>((event, emit) async {
      try {
        emit(PopularCoursesState(ApiStatus.LOADING));

        final response = await homeRepository.getPopularCourses();

        if (response.data != null) {
          emit(PopularCoursesState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(PopularCoursesState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(PopularCoursesState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<FilteredPopularCoursesEvent>((event, emit) async {
      try {
        emit(FilteredPopularCoursesState(ApiStatus.LOADING));

        final response = await homeRepository.getFilteredPopularCourses();

        if (response.data != null) {
          emit(FilteredPopularCoursesState(ApiStatus.SUCCESS,
              response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(FilteredPopularCoursesState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e, stackTrace) {
        Log.v("ERROR DATA getFilteredPopularCourses : $stackTrace");
        emit(FilteredPopularCoursesState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetBottomNavigationBarEvent>((event, emit) async {
      try {
        emit(GetBottomBarState(ApiStatus.LOADING));
        final response = await homeRepository.bottombarResponse();
        if (response.status == 1) {
          emit(GetBottomBarState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(GetBottomBarState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GetBottomBarState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GCarvaanPostEvent>((event, emit) async {
      try {
        emit(GCarvaanPostState(ApiStatus.LOADING));

        final response = await homeRepository.GCarvaanPost(
            event.callCount!, event.postId, event.userActivity);

        if (response.data != null) {
          emit(GCarvaanPostState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA  error ::: $response");
          emit(GCarvaanPostState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA exception : $e");
        emit(GCarvaanPostState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<SinlgePostGCarvaanEvent>((event, emit) async {
      try {
        emit(SinlgePostGCarvaanState(ApiStatus.LOADING));

        final response = await homeRepository.getCarvaanPost(event.postId);

        if (response.data != null) {
          emit(SinlgePostGCarvaanState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA  error ::: $response");
          emit(SinlgePostGCarvaanState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA exception : $e");
        emit(SinlgePostGCarvaanState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<GReelsPostEvent>((event, emit) async {
      try {
        emit(GReelsPostState(ApiStatus.LOADING));

        final response = await homeRepository.GReelsPost(event);

        if (response.data != null) {
          emit(GReelsPostState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GReelsPostState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e, stackTrace) {
        Log.v("now the stacktrace : $e, $stackTrace");
        emit(GReelsPostState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<SingleGReelsPostEvent>((event, emit) async {
      try {
        emit(GReelsPostState(ApiStatus.LOADING));

        final response = await homeRepository.getSingleReel(event.reelId);

        if (response.data != null) {
          emit(GReelsPostState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(GReelsPostState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(GReelsPostState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<CreatePostEvent>((event, emit) async {
      try {
        emit(CreatePostState(ApiStatus.LOADING));

        final response = await homeRepository.CreatePost(
            event.thumbnail,
            event.contentType,
            event.postType,
            event.title,
            event.description,
            event.filePath);

        emit(CreatePostState(ApiStatus.SUCCESS, response: response));
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(CreatePostState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<GetUserProfileEvent>((event, emit) async {
      try {
        emit(GetUserProfileState(ApiStatus.LOADING));

        final response = await homeRepository.getUserProfile();

        emit(GetUserProfileState(ApiStatus.SUCCESS, response: response));
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(GetUserProfileState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<UpdateUserProfileImageEvent>((event, emit) async {
      try {
        emit(UpdateUserProfileImageState(ApiStatus.LOADING));

        final response = await homeRepository.updateUserProfileImage(
            event.filePath, event.name, event.email);

        emit(
            UpdateUserProfileImageState(ApiStatus.SUCCESS, response: response));
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(UpdateUserProfileImageState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<LikeContentEvent>((event, emit) async {
      Log.v('calling api with ${event.contentId}');
      try {
        emit(LikeContentState(ApiStatus.LOADING));

        final response = await homeRepository.likeContent(
            event.contentId, event.type, event.like);

        if (response != null) {
          emit(LikeContentState(
            ApiStatus.SUCCESS,
          ));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(LikeContentState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(LikeContentState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<ReportEvent>((event, emit) async {
      try {
        emit(ReportState(ApiStatus.LOADING));

        final response = await homeRepository.reportContent(
            event.status, event.postId, event.category, event.comment);

        emit(ReportState(ApiStatus.SUCCESS, response: response));
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(ReportState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<DeletePostEvent>((event, emit) async {
      try {
        emit(DeletePostState(ApiStatus.LOADING));

        final response = await homeRepository.deletePost(event.postId);

        if (response != null) {
          emit(DeletePostState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(DeletePostState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(DeletePostState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());

    on<OpenToWorkEvent>((event, emit) async {
      try {
        emit(OpenToWorkState(ApiStatus.LOADING));

        final response = await homeRepository.openToWork(event.openToWork);

        if (response != null) {
          emit(OpenToWorkState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(OpenToWorkState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(OpenToWorkState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<DeleteSkillEvent>((event, emit) async {
      print('event call');
      try {
        emit(DeleteSkillState(ApiStatus.LOADING));

        final response = await homeRepository.getDeleteSkill(event.skillId);

        if (response != null) {
          emit(DeleteSkillState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(DeleteSkillState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(DeleteSkillState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<UserAnalyticsEvent>((event, emit) async {
      try {
        emit(UserAnalyticsState(ApiStatus.LOADING));
        final response = await homeRepository.UserAnalytics();
        if (response.status == 1) {
          emit(UserAnalyticsState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(UserAnalyticsState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(UserAnalyticsState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<LearningSpaceEvent>((event, emit) async {
      try {
        emit(LearningSpaceState(ApiStatus.LOADING));
        final response = await homeRepository.learningSpace();
        if (response.status == 1) {
          emit(LearningSpaceState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRyyyOR DATA ::: $response");
          emit(LearningSpaceState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA this : $e");
        emit(LearningSpaceState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<CreatePortfolioEvent>((event, emit) async {
      try {
        Map<String, dynamic> data = Map();
        var filePath = event.filePath;
        String fileName = filePath!.split('/').last;
        data['file'] =
            await Dio.MultipartFile.fromFile(filePath, filename: fileName);
        data['title'] = event.title;
        data['description'] = event.description;
        data['type'] = event.type;

        emit(CreatePortfolioState(ApiStatus.LOADING));
        final response = await homeRepository.createPortfolio(data);
        if (response != null) {
          emit(CreatePortfolioState(
            ApiStatus.SUCCESS,
          ));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(CreatePortfolioState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(CreatePortfolioState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<MasterBrandCreateEvent>((event, emit) async {
      try {
        Map<String, dynamic> data = Map();
        var filePath = event.filePath;
        String fileName = filePath!.split('/').last;
        data['file'] =
            await Dio.MultipartFile.fromFile(filePath, filename: fileName);
        data['title'] = event.title;
        data['description'] = event.description;

        emit(MasterBrandCreateState(ApiStatus.LOADING));
        final response = await homeRepository.masterBrandCreate(data);
        if (response != null) {
          emit(MasterBrandCreateState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(MasterBrandCreateState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(MasterBrandCreateState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<UserBrandCreateEvent>((event, emit) async {
      try {
        Map<String, dynamic> data = Map();
        var filePath = event.filePath;
        String fileName = filePath!.split('/').last;
        data['file'] =
            await Dio.MultipartFile.fromFile(filePath, filename: fileName);
        data['type_id'] = event.typeId;
        data['start_date'] = event.startDate;
        data['end_date'] = event.endDate;

        emit(UserBrandCreateState(ApiStatus.LOADING));
        final response = await homeRepository.userBrandCreate(data);
        if (response != null) {
          emit(UserBrandCreateState(
            ApiStatus.SUCCESS,
          ));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(UserBrandCreateState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(UserBrandCreateState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<DeletePortfolioEvent>((event, emit) async {
      try {
        emit(DeletePortfolioState(ApiStatus.LOADING));

        final response = await homeRepository.deletePortfolio(event.id);

        if (response != null) {
          emit(DeletePortfolioState(
            ApiStatus.SUCCESS,
          ));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(DeletePortfolioState(
            ApiStatus.ERROR,
          ));
        }
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(DeletePortfolioState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());
    on<ListPortfolioEvent>((event, emit) async {
      try {
        emit(ListPortfolioState(ApiStatus.LOADING));
        final response =
            await homeRepository.listPortfolio(event.type, event.userId);

        emit(ListPortfolioState(ApiStatus.SUCCESS, response: response));
      } catch (e) {
        Log.v("ERROR DATA is : $e");
        emit(ListPortfolioState(
          ApiStatus.ERROR,
        ));
      }
    }, transformer: concurrent());

    on<UpdateVideoCompletionEvent>((event, emit) async {
      try {
        emit(UpdateVideoCompletionState(ApiStatus.LOADING));
        final response = await homeRepository.updateVideoCompletion(
            event.bookmark!, event.contentId!, event.completionPercent!);
        if (response.status == 1) {
          emit(UpdateVideoCompletionState(
            ApiStatus.SUCCESS,
          ));
        } else {
          emit(UpdateVideoCompletionState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("Exception DATA : $e");
        emit(UpdateVideoCompletionState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<ExploreApplyJobEvent>((event, emit) async {
      try {
        emit(ExploreApplyJobState(ApiStatus.LOADING));
        final response = await homeRepository.exploreApplyJob(
          jobId: event.jobId,
          jobType: event.jobType,
        );
        if (response == 1) {
          emit(ExploreApplyJobState(ApiStatus.SUCCESS));
        } else {
          emit(ExploreApplyJobState(ApiStatus.ERROR, error: response));
        }
      } catch (e) {
        emit(ExploreApplyJobState(ApiStatus.ERROR,
            error: "Something went wrong "));
      }
    }, transformer: concurrent());

    on<GenerateSimilarityEvent>((event, emit) async {
      try {
        emit(GenerateSimilarityState(ApiStatus.LOADING));
        final response = await homeRepository.generateSimilarity(
          submissionId: event.submissionId,
        );

        if (response == 1) {
          emit(GenerateSimilarityState(ApiStatus.SUCCESS));
        } else {
          emit(GenerateSimilarityState(ApiStatus.ERROR, error: response));
        }
      } catch (e) {
        emit(GenerateSimilarityState(ApiStatus.ERROR,
            error: "Something went wrong "));
      }
    }, transformer: concurrent());

    on<DownloadAssessmentReportEvent>((event, emit) async {
      try {
        emit(DownloadAssessmentReportState(ApiStatus.LOADING));
        final response = await homeRepository.downloadAssessmentReport(
          programId: event.programId,
          contentId: event.contentId,
          userId: event.userId,
        );
        if (response!.data != null) {
          emit(DownloadAssessmentReportState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(DownloadAssessmentReportState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        emit(DownloadAssessmentReportState(ApiStatus.ERROR));
      }
    }, transformer: concurrent());

    //add new
    on<FeeAgreementEvent>((event, emit) async {
      try {
        emit(FeeAgreementState(ApiStatus.LOADING));
        final response = await homeRepository.feeAgreement();
        if (response!.data != null) {
          emit(FeeAgreementState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(FeeAgreementState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        emit(FeeAgreementState(ApiStatus.ERROR));
      }
    }, transformer: concurrent());

    on<AcceptFeeAgreementEvent>((event, emit) async {
      try {
        emit(AcceptFeeAgreementState(ApiStatus.LOADING));
        final response = await homeRepository.AcceptFeeAgreement(isAccepted: event.isAccepted);
        if (response!.data != null) {
          emit(AcceptFeeAgreementState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(AcceptFeeAgreementState(ApiStatus.ERROR, response: response));
        }
      } catch (e) {
        emit(AcceptFeeAgreementState(ApiStatus.ERROR));
      }
    }, transformer: concurrent());
  }
}
//AcceptFeeAgreementEvent
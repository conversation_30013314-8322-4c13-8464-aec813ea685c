import 'dart:async';
import 'dart:developer';
import 'package:app_links/app_links.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:install_referrer/install_referrer.dart';
import 'package:masterg/blocs/dashboard_content/dashboard_content_cubit.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/data/models/response/home_response/greels_response.dart';
import 'package:masterg/data/providers/video_player_provider.dart';
import 'package:masterg/firebase_options.dart';
import 'package:masterg/pages/walk_through_page/splash_screen.dart';
import 'package:masterg/routes/local_notification.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/logger.dart';
import 'package:masterg/utils/notification_helper.dart';
import 'package:masterg/utils/offline_data/url_model_adapter.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'blocs/auth_bloc.dart';
import 'data/api/api_service.dart';
import 'data/models/response/auth_response/user_session.dart';
import 'data/providers/menu_provider.dart';
import 'dependency_injections.dart';
import 'local/pref/Preference.dart';
import 'pages/explore_job/explore_job_proivder.dart';
import 'routes/app_link_route.dart';
//import 'package:uni_links/uni_links.dart';
import 'package:android_play_install_referrer/android_play_install_referrer.dart';

DateTime? currentIndiaTime;
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
StreamController<double> uploadProgressController =
    StreamController<double>.broadcast();

void main() async {
  runZoned(() {
    runZonedGuarded(() async {
      WidgetsFlutterBinding.ensureInitialized();
      NotificationService().initNotification();

      if (!kIsWeb) {
        final appDocumentDir = await getApplicationDocumentsDirectory();
        Hive.init(appDocumentDir.path);
        Hive.registerAdapter(URLModelAdapter());
      }

      if (await AppTrackingTransparency.trackingAuthorizationStatus ==
          TrackingStatus.notDetermined) {
        //await Future.delayed(const Duration(milliseconds: 200));
        await AppTrackingTransparency.requestTrackingAuthorization();
      }

      if (!kIsWeb)
        await FlutterDownloader.initialize(debug: true, ignoreSsl: true);

      await EasyLocalization.ensureInitialized();

      if (kIsWeb) {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
      } else
        await Firebase.initializeApp();

      setupDependencyInjections();
      await Hive.initFlutter();

      Hive.openBox(DB.CONTENT);
      Hive.openBox(DB.ANALYTICS);
      Hive.openBox(DB.TRAININGS);
      Hive.openBox('theme');
      Preference.getInstance();

      // Initialize logger
      Logger.init();

      runApp(MultiBlocProvider(
        providers: [
          BlocProvider<AuthBloc>(
              create: (context) => AuthBloc(LoginState(ApiStatus.INITIAL))),
          BlocProvider<HomeBloc>(
              create: (context) =>
                  HomeBloc(AnnouncementContentState(ApiStatus.INITIAL))),
          BlocProvider<AuthBloc>(
              create: (context) => AuthBloc(LoginState(ApiStatus.INITIAL))),
          BlocProvider<HomeBloc>(
              create: (context) =>
                  HomeBloc(AnnouncementContentState(ApiStatus.INITIAL))),
          BlocProvider<HomeBloc>(
              create: (context) =>
                  HomeBloc(ContentTagsState(ApiStatus.INITIAL))),
          BlocProvider<DashboardContentCubit>(
              create: (context) => DashboardContentCubit()),
        ],
        child: MultiProvider(
          providers: [
            ChangeNotifierProvider<ExploreJobProvider>(
              create: (context) => ExploreJobProvider(),
            ),
            ChangeNotifierProvider<MenuListProvider>(
              create: (context) => MenuListProvider([]),
            ),
            ChangeNotifierProvider<GCarvaanListModel>(
              create: (context) => GCarvaanListModel([]),
            ),
            ChangeNotifierProvider<GReelsModel>(
              create: (context) => GReelsModel([]),
            ),
            ChangeNotifierProvider<VideoPlayerProvider>(
              create: (context) => VideoPlayerProvider(false),
            ),
            ChangeNotifierProvider<MenuProvider>(
              create: (context) => MenuProvider(),
            ),
          ],
          child: GetMaterialApp(
            navigatorKey: navigatorKey,
            theme: ThemeData(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                bottomAppBarTheme: BottomAppBarTheme(
                  elevation: 0.0,
                ),
                primarySwatch: ColorConstants.PRIMARY_COLOR_LIGHT,
                primaryColorDark: ColorConstants.ORANGE),
            debugShowCheckedModeBanner: false,
            onGenerateRoute: (settings) {
              return null;
            },
            home: EasyLocalization(
                supportedLocales: [
                  Locale('en'),
                  Locale('ar'),
                  Locale('hi'),
                  Locale('mr'),
                  Locale('ta'),
                  Locale('or'),
                ],
                path: 'assets/translations',
                fallbackLocale: Locale('en'),
                child: MyApp()
                //  FeatureDiscovery.withProvider(
                //     persistenceProvider: NoPersistenceProvider(), child:),
                // MyApp(),
                ),
          ),
        ),
      ));

      Preference.load().then((value) {
        UserSession();
        setRegion();
      });
    }, (error, stackTrace) {});
  }, zoneSpecification: ZoneSpecification(
      print: (Zone self, ZoneDelegate parent, Zone zone, String line) {
    if (kDebugMode) parent.print(zone, "$line");
  }));
}

Future<void> setRegion() async {
  final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
  Preference.setString('region', currentTimeZone);
}

class MyApp extends StatefulWidget with PortraitModeMixin {
  final String? intialRoute;
  final Widget? routeWidget;

  const MyApp({super.key, this.intialRoute, this.routeWidget});

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late StreamSubscription _sub;
  bool initialLinkRequested = false;

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initReferrerDetails() async {
    // late ReferrerDetails referrerDetails;

    try {
      // referrerDetails =
      await AndroidPlayInstallReferrer.installReferrer;
    } catch (e) {}

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    if (Preference.getBool('utm_handled') == null ||
        Preference.getBool('utm_handled') == false) {
      //AppLinkRoute.handlePlayStoreUtm(referrerDetails);
    }
  }

  ///For IOS Referrer Details
  Future<void> iosReferrer() async {
    InstallReferrerDetectorBuilder(
      builder: (BuildContext context, InstallationApp? app) {
        if (app == null) {
          return const CircularProgressIndicator.adaptive();
        } else {
          return Text(
            'Package name:\n${app.packageName ?? 'Unknown'}\n'
            'Referrer:\n${referrerToReadableString(app.referrer)}',
            textAlign: TextAlign.center,
          );
        }
      },
    );
  }

  String referrerToReadableString(InstallationAppReferrer referrer) {
    AppLinkRoute.handleAppStoreUtm('utm_source=p_id');
    Utility.showSnackBar(
        scaffoldContext: context, message: 'Referrer IOS Massage==$referrer');
    switch (referrer) {
      case InstallationAppReferrer.iosAppStore:
        return "Apple - App Store";
      case InstallationAppReferrer.iosTestFlight:
        return "Apple - Test Flight";
      case InstallationAppReferrer.iosDebug:
        return "Apple - Debug";
      case InstallationAppReferrer.androidGooglePlay:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidAmazonAppStore:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidHuaweiAppGallery:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidOppoAppMarket:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidSamsungAppShop:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidVivoAppStore:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidXiaomiAppStore:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidManually:
        // TODO: Handle this case.
        break;
      case InstallationAppReferrer.androidDebug:
        // TODO: Handle this case.
        break;
    }
    return '';
  }

  @override
  void dispose() {
    _sub.cancel();
    log("your app link is dispose");
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    initReferrerDetails();
    iosReferrer();
    initUniLinks();

    Preference.getInstance();
    Preference.load().then((value) {
      setState(() {
        UserSession();
      });
    });
  }

  void initHive() async {
    print('start hive ');
    try {
      await getApplicationDocumentsDirectory().then((value) {
        Hive.init(value.path);
        Hive.openBox(DB.CONTENT);
        Hive.openBox(DB.ANALYTICS);
        Hive.openBox(DB.TRAININGS);
        Hive.openBox('theme');
      });
    } catch (e) {
      print('soemthign$e');
    }
    print('start hive done');
  }

  Future<void> initUniLinks() async {
    //final initialLink = await getInitialLink();
    final initialLink = await AppLinks();

    // final sub =
    initialLink.uriLinkStream.listen((link) {
      log("your app link is 1s $link");
      AppLinkRoute.handleRoute(route: initialLink.toString());
    }, onError: (err) {
      log("your app link is error $err");
    });

    // _sub = initialLink.uriLinkStream((String? link) {
    //   // Parse the link and warn the user, if it is not correct
    //   log("your app link is 1s $link");
    //   if (initialLink != null) {
    //     AppLinkRoute.handleRoute(route: initialLink);
    //   } else if (link != null) {
    //     AppLinkRoute.handleRoute(route: link);
    //   }
    // }, onError: (err) {
    //   log("your app link is error $err");
    //   // Handle exception by warning the user their action did not succeed
    // });

    //stop listening to _sub

    // Platform messages may fail, so we use a try/catch PlatformException.
    // try {
    //   final initialLink = await getInitialLink();
    //   if (initialLink != null) {
    //     AppLinkRoute.handleRoute(route: initialLink);
    //   }
    //   // Parse the link and warn the user, if it is not correct,
    //   // but keep in mind it could be `null`.
    // } on PlatformException {
    //   // Handle exception by warning the user their action did not succeed
    //   // return?
    //   log("your app link is error PlatformException ");
    // }`
  }

  @override
  Widget build(BuildContext context) {
    Application(context);

    NotificationHelper.getInstance(context).setFcm();

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      home: EntryAnimationPage(
        intialRoute: widget.intialRoute,
        routeWidget: widget.routeWidget,
      ),
    );
  }
}

mixin PortraitModeMixin on StatefulWidget {
  Widget? build(BuildContext context) {
    _portraitModeOnly();
    return null;
  }
}

void initHive() async {
  try {
    log("error while opening ");

    await getApplicationDocumentsDirectory().then((value) {
      Hive.init(value.path);
      Hive.openBox(DB.CONTENT);
      Hive.openBox(DB.ANALYTICS);
      Hive.openBox(DB.TRAININGS);
      Hive.openBox('theme');
    });
  } catch (error) {
    log("error while opening for hive $error");
  }
}

void _portraitModeOnly() {
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

class Application {
  static BuildContext? _context;

  Application(BuildContext context) {
    _context = context;
  }

  static BuildContext? getContext() {
    return _context;
  }
}

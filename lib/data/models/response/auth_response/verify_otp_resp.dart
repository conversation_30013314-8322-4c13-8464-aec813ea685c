class VerifyOtpResp {
  VerifyOtpResp({
    this.isCompleted,
    this.status,
    this.data,
    this.error,
    this.name,
    this.founded,
    this.members,
  });

  int? isCompleted;
  int? status;
  Data? data;
  List<dynamic>? error;
  String? name;
  int? founded;
  List<String>? members;

  factory VerifyOtpResp.fromJson(Map<String, dynamic> json) => VerifyOtpResp(
        isCompleted: json["is_completed"] == null ? null : json["is_completed"],
        status: json["status"] == null ? null : json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null
            ? null
            : List<dynamic>.from(json["error"].map((x) => x)),
        name: json["name"] == null ? null : json["name"],
        founded: json["founded"] == null ? null : json["founded"],
        members: json["members"] == null
            ? null
            : List<String>.from(json["members"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "is_completed": isCompleted == null ? null : isCompleted,
        "status": status == null ? null : status,
        "data": data == null ? null : data!.toJson(),
        "error":
            error == null ? null : List<dynamic>.from(error!.map((x) => x)),
        "name": name == null ? null : name,
        "founded": founded == null ? null : founded,
        "members":
            members == null ? null : List<dynamic>.from(members!.map((x) => x)),
      };
}

class Data {
  Data({
    this.token,
    this.user,
  });

  String? token;
  User? user;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        token: json["token"],
        user: User.fromJson(json["user"]),
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "user": user!.toJson(),
      };
}

class User {
  int? id;
  String? name;
  String? email;
  String? department;
  String? designation;
  String? mobileNo;
  String? profileImage;
  int? isTrainer;
  String? categoryIds;
  String? defaultVideoUrlOnCategory;
  String? orgLogo;
  int? orgId;
  String? mecRegdId;
  String? ssoToken; //sso_token
  String? role;
  String? locale;
  int? languageId;
  String? englishName;
  int? isPrimaryLanguage;
  int? currentSemesterID;

  User(
      {this.id,
      this.name,
      this.isTrainer,
      this.email,
      this.department,
      this.designation,
      this.mobileNo,
      this.profileImage,
      this.categoryIds,
      this.defaultVideoUrlOnCategory,
      this.orgLogo,
      this.orgId,
      this.mecRegdId,
      this.ssoToken,
      this.role,
      this.locale,
      this.languageId,
      this.englishName,
      this.isPrimaryLanguage,
      this.currentSemesterID});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    department = json['department'];
    designation = json['designation'];
    mobileNo = json['mobile_no'] is String
        ? json['mobile_no']
        : json['mobile_no'].toString();
    profileImage = json['profile_image'];
    isTrainer = json['is_trainer'];
    categoryIds = json['category_ids'];
    defaultVideoUrlOnCategory = json['default_video_url_on_category'];
    orgLogo = json['org_logo'];
    orgId = json['organization_id'];
    mecRegdId = json['mec_regd_id'];
    ssoToken = json['sso_token'];
    role = json['role'];
    locale = json['locale'];
    languageId = json['language_id'];
    englishName = json['english_name'];
    isPrimaryLanguage = json['is_primary_language'];
    currentSemesterID = json['current_semester_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['email'] = this.email;
    data['department'] = this.department;
    data['designation'] = this.designation;
    data['mobile_no'] = this.mobileNo;
    data['profile_image'] = this.profileImage;
    data['is_trainer'] = this.isTrainer;
    data['category_ids'] = this.categoryIds;
    data['default_video_url_on_category'] = this.defaultVideoUrlOnCategory;
    data['org_logo'] = this.orgLogo;
    data['organization_id'] = this.orgId;
    data['mec_regd_id'] = this.mecRegdId;
    data['sso_token'] = this.ssoToken;
    data['role'] = this.role;
    data['locale'] = this.locale;
    data['language_id'] = this.languageId;
    data['english_name'] = this.englishName;
    data['is_primary_language'] = this.isPrimaryLanguage;
    data['current_semester_id'] = this.currentSemesterID;

    return data;
  }
}

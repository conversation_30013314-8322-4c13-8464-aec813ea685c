// To parse this JSON data, do
//
//     final assessmentReportResp = assessmentReportRespFromJson(jsonString);

import 'dart:convert';

AssessmentReportResp assessmentReportRespFromJson(String str) =>
    AssessmentReportResp.fromJson(json.decode(str));

String assessmentReportRespToJson(AssessmentReportResp data) =>
    json.encode(data.toJson());

class AssessmentReportResp {
  int? status;
  Data? data;
  List<dynamic>? error;

  AssessmentReportResp({
    this.status,
    this.data,
    this.error,
  });

  factory AssessmentReportResp.fromJson(Map<String, dynamic> json) =>
      AssessmentReportResp(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null
            ? []
            : List<dynamic>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  List<SkillCategory>? skillCategories;
  AllignmentPer? allignmentPer;
  Summary? summary;

  Data({
    this.skillCategories,
    this.allignmentPer,
    this.summary,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        skillCategories: json["skill_categories"] == null
            ? []
            : List<SkillCategory>.from(json["skill_categories"]!
                .map((x) => SkillCategory.fromJson(x))),
        allignmentPer: json["allignment_per"] == null
            ? null
            : AllignmentPer.fromJson(json["allignment_per"]),
        summary:
            json["summary"] == null ? null : Summary.fromJson(json["summary"]),
      );

  Map<String, dynamic> toJson() => {
        "skill_categories": skillCategories == null
            ? []
            : List<dynamic>.from(skillCategories!.map((x) => x.toJson())),
        "allignment_per": allignmentPer?.toJson(),
        "summary": summary?.toJson(),
      };
}

class AllignmentPer {
  double? percentage;

  AllignmentPer({
    this.percentage,
  });

  factory AllignmentPer.fromJson(Map<String, dynamic> json) => AllignmentPer(
        percentage: json["percentage"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "percentage": percentage,
      };
}

class SkillCategory {
  int? scId;
  String? scName;
  int? scFitmentScore;
  List<Skill>? skills;

  SkillCategory({
    this.scId,
    this.scName,
    this.skills,
    this.scFitmentScore,
  });

  factory SkillCategory.fromJson(Map<String, dynamic> json) => SkillCategory(
        scId: json["sc_id"],
        scName: json["sc_name"],
        scFitmentScore: json['sc_fitment_score'],
        skills: json["skills"] == null
            ? []
            : List<Skill>.from(json["skills"]!.map((x) => Skill.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "sc_id": scId,
        "sc_name": scName,
        "sc_fitment_score": scFitmentScore,
        "skills": skills == null
            ? []
            : List<dynamic>.from(skills!.map((x) => x.toJson())),
      };
}

class Skill {
  int? userId;
  int? assessmentId;
  String? subSkill;
  int? totCorrect;
  int? totCorrectScore;
  int? totScore;
  String? title;
  int? scId;
  String? scName;
  double? fitmentScore;
  int? rating;

  Skill({
    this.userId,
    this.assessmentId,
    this.subSkill,
    this.totCorrect,
    this.totCorrectScore,
    this.totScore,
    this.title,
    this.scId,
    this.scName,
    this.fitmentScore,
    this.rating,
  });

  factory Skill.fromJson(Map<String, dynamic> json) => Skill(
        userId: json["user_id"],
        assessmentId: json["assessment_id"],
        subSkill: json["sub_skill"],
        totCorrect: json["tot_correct"],
        totCorrectScore: json["tot_correct_score"],
        totScore: json["tot_score"],
        title: json["title"],
        scId: json["sc_id"],
        scName: json["sc_name"],
        fitmentScore: json["fitment_score"]?.toDouble(),
        rating: json["rating"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "assessment_id": assessmentId,
        "sub_skill": subSkill,
        "tot_correct": totCorrect,
        "tot_correct_score": totCorrectScore,
        "tot_score": totScore,
        "title": title,
        "sc_id": scId,
        "sc_name": scName,
        "fitment_score": fitmentScore,
        "rating": rating,
      };
}

class Summary {
  String? strengths;
  String? areaOfImprovement;
  List<String>? remarks;

  Summary({
    this.strengths,
    this.areaOfImprovement,
    this.remarks,
  });

  factory Summary.fromJson(Map<String, dynamic> json) => Summary(
        strengths: json["strengths"],
        areaOfImprovement: json["area_of_improvement"],
        remarks: json["remarks"] == null
            ? []
            : List<String>.from(json["remarks"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "strengths": strengths,
        "area_of_improvement": areaOfImprovement,
        "remarks":
            remarks == null ? [] : List<dynamic>.from(remarks!.map((x) => x)),
      };
}

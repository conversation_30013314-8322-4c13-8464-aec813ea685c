import 'dart:convert';

DashboardViewResponse dashboardViewResponseFromJson(String str) =>
    DashboardViewResponse.fromJson(json.decode(str));

String dashboardViewResponseToJson(DashboardViewResponse data) =>
    json.encode(data.toJson());

class DashboardViewResponse {
  DashboardViewResponse({
    this.status,
    this.data,
    this.error,
  });

  int? status;
  Data? data;
  List<dynamic>? error;

  factory DashboardViewResponse.fromJson(Map<String, dynamic> json) =>
      DashboardViewResponse(
        status: json["status"],
        data: Data.fromJson(json["data"]),
        error: List<dynamic>.from(json["error"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  String? learnerDashboardBanner;
  String? learnerDashboardJobPortfolio;
  String? dashboardFeaturedContentLimit;
  String? learnerDashboardInterestArea;
  String? learnerDashboardJobDomains;
  String? learnerDashboardInternship;
  String? learnerDashboardCompetition;
  String? learnerDashboardPortfolio;
  String? learnerDashboardFutureTrends;
  String? toDoActivitiesLimit;
  String? dashboardSessionsLimit;
  String? dashboardMyCoursesLimit;
  String? dashboardReelsLimit;
  String? dashboardRecommendedCoursesLimit;
  String? dashboardCarvanLimit;
  int? enableSkill;

  Messages? messages;
  dynamic whoAmI;

  Data({
    this.learnerDashboardBanner,
    this.learnerDashboardJobPortfolio,
    this.learnerDashboardFutureTrends,
    this.learnerDashboardInterestArea,
    this.learnerDashboardJobDomains,
    this.learnerDashboardInternship,
    this.learnerDashboardCompetition,
    this.learnerDashboardPortfolio,
    this.dashboardFeaturedContentLimit,
    this.toDoActivitiesLimit,
    this.dashboardSessionsLimit,
    this.dashboardMyCoursesLimit,
    this.dashboardReelsLimit,
    this.dashboardRecommendedCoursesLimit,
    this.dashboardCarvanLimit,
    this.enableSkill,
    this.messages,
    this.whoAmI,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
      learnerDashboardBanner: json["learner_dashboard_banner"] == ""
          ? "0"
          : json["learner_dashboard_banner"],
      learnerDashboardJobPortfolio: json["learner_dashboard_jobportfolio"] == ""
          ? "0"
          : json["learner_dashboard_jobportfolio"],
      learnerDashboardFutureTrends: json["learner_dashboard_futuretrends"] == ""
          ? "0"
          : json["learner_dashboard_futuretrends"],
      learnerDashboardInterestArea: json["learner_dashboard_interestarea"] == ""
          ? "0"
          : json["learner_dashboard_interestarea"],
      learnerDashboardJobDomains: json["learner_dashboard_jobdomains"] == ""
          ? "0"
          : json["learner_dashboard_jobdomains"],
      learnerDashboardInternship: json["learner_dashboard_internship"] == ""
          ? "0"
          : json["learner_dashboard_internship"],
      learnerDashboardCompetition: json["learner_dashboard_competetion"] == ""
          ? "0"
          : json["learner_dashboard_competetion"],
      learnerDashboardPortfolio: json["learner_dashboard_portfolio"] == ""
          ? "0"
          : json["learner_dashboard_portfolio"],
      dashboardFeaturedContentLimit: json["dashboard_featured_content_limit"] == ""
          ? "0"
          : json["dashboard_featured_content_limit"],
      toDoActivitiesLimit:
          json["to_do_activities"] == "" ? "0" : json["to_do_activities"],
      dashboardSessionsLimit: json["dashboard_sessions_limit"] == ""
          ? "0"
          : json["dashboard_sessions_limit"],
      dashboardMyCoursesLimit: json["dashboard_my_courses_limit"] == ""
          ? "0"
          : json["dashboard_my_courses_limit"],
      dashboardReelsLimit: json["dashboard_reels_limit"] == ""
          ? "0"
          : json["dashboard_reels_limit"],
      dashboardRecommendedCoursesLimit:
          json["dashboard_recommended_courses_limit"] == ""
              ? "0"
              : json["dashboard_recommended_courses_limit"],
      dashboardCarvanLimit:
          json["dashboard_carvan_limit"] == "" ? "0" : json["dashboard_carvan_limit"],
      whoAmI: json["who_am_i"],
      messages: json["messages"] == null ? null : Messages.fromJson(json["messages"]),
      enableSkill: int.tryParse('${json['enable_skill']}'));

  Map<String, dynamic> toJson() => {
        "learner_dashboard_banner": learnerDashboardBanner,
        "learner_dashboard_jobportfolio": learnerDashboardJobPortfolio,
        "learner_dashboard_futuretrends": learnerDashboardFutureTrends,
        "learner_dashboard_interestarea": learnerDashboardInterestArea,
        "learner_dashboard_jobdomains": learnerDashboardJobDomains,
        "learner_dashboard_internship": learnerDashboardInternship,
        "learner_dashboard_competetion": learnerDashboardCompetition,
        "learner_dashboard_portfolio": learnerDashboardPortfolio,
        "dashboard_featured_content_limit": dashboardFeaturedContentLimit,
        "to_do_activities": toDoActivitiesLimit,
        "dashboard_sessions_limit": dashboardSessionsLimit,
        "dashboard_my_courses_limit": dashboardMyCoursesLimit,
        "dashboard_reels_limit": dashboardReelsLimit,
        "dashboard_recommended_courses_limit": dashboardRecommendedCoursesLimit,
        "dashboard_carvan_limit": dashboardCarvanLimit,
        "enable_skill": enableSkill,
        "messages": messages?.toJson(),
        "who_am_i": whoAmI,
      };

  map(Function(dynamic e) param0) {}
}

class WhoAmI {
  List<String>? en;
  List<String>? ar;

  WhoAmI({
    this.en,
    this.ar,
  });

  factory WhoAmI.fromJson(Map<String, dynamic> json) => WhoAmI(
        en: json["en"] == null
            ? []
            : List<String>.from(json["en"]!.map((x) => x)),
        ar: json["ar"] == null
            ? []
            : List<String>.from(json["ar"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "en": en == null ? [] : List<dynamic>.from(en!.map((x) => x)),
        "ar": ar == null ? [] : List<dynamic>.from(ar!.map((x) => x)),
      };
}

class Ar {
  String? mecStudentLabel;
  String? mecStudent;
  String? mecFacultyStaffLabel;
  String? mecFacultyStaff;
  String? mecAlumniLabel;
  String? mecAlumni;
  String? prospectiveStudentLabel;
  String? prospectiveStudent;
  String? corporatesLabel;
  String? corporates;
  String? governmentLabel;
  String? government;
  String? corporate;

  Ar({
    this.mecStudentLabel,
    this.mecStudent,
    this.mecFacultyStaffLabel,
    this.mecFacultyStaff,
    this.mecAlumniLabel,
    this.mecAlumni,
    this.prospectiveStudentLabel,
    this.prospectiveStudent,
    this.corporatesLabel,
    this.corporates,
    this.governmentLabel,
    this.government,
    this.corporate,
  });

  factory Ar.fromJson(Map<String, dynamic> json) => Ar(
        mecStudentLabel: json["Mec Student-label"],
        mecStudent: json["Mec Student"],
        mecFacultyStaffLabel: json["Mec Faculty/Staff-label"],
        mecFacultyStaff: json["Mec Faculty/Staff"],
        mecAlumniLabel: json["Mec Alumni-label"],
        mecAlumni: json["Mec Alumni"],
        prospectiveStudentLabel: json["Prospective Student-label"],
        prospectiveStudent: json["Prospective Student"],
        corporatesLabel: json["Corporates-label"],
        corporates: json["Corporates"],
        governmentLabel: json["Government-label"],
        government: json["Government"],
        corporate: json["Corporate"],
      );

  Map<String, dynamic> toJson() => {
        "Mec Student-label": mecStudentLabel,
        "Mec Student": mecStudent,
        "Mec Faculty/Staff-label": mecFacultyStaffLabel,
        "Mec Faculty/Staff": mecFacultyStaff,
        "Mec Alumni-label": mecAlumniLabel,
        "Mec Alumni": mecAlumni,
        "Prospective Student-label": prospectiveStudentLabel,
        "Prospective Student": prospectiveStudent,
        "Corporates-label": corporatesLabel,
        "Corporates": corporates,
        "Government-label": governmentLabel,
        "Government": government,
        "Corporate": corporate,
      };
}

class Messages {
  Ar? en;
  Ar? ar;

  Messages({
    this.en,
    this.ar,
  });

  factory Messages.fromJson(Map<String, dynamic> json) => Messages(
        en: json["en"] == null ? null : Ar.fromJson(json["en"]),
        ar: json["ar"] == null ? null : Ar.fromJson(json["ar"]),
      );

  Map<String, dynamic> toJson() => {
        "en": en?.toJson(),
        "ar": ar?.toJson(),
      };
}

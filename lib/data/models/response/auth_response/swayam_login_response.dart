class SwayamLoginResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  SwayamLoginResponse({this.status, this.data, this.error});

  SwayamLoginResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data?.toJson();
    }
    data['error'] = this.error;
    return data;
  }
}

class Data {
  String? token;
  User? user;
  String? url;

  Data({this.token, this.user, this.url});

  Data.fromJson(Map<String, dynamic> json) {
    token = json['token'];
    url = json['url'] != null ? json['url'] : null;
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['token'] = this.token;
    final user = this.user;
    if (user != null) {
      data['user'] = user.toJson();
    }
    data['url'] = this.url;
    return data;
  }
}

class User {
  int? id;
  String? name;
  String? email;
  String? department;
  String? designation;
  String? mobileNo;
  String? profileImage;
  int? isTrainer;
  String? categoryIds;
  String? defaultVideoUrlOnCategory;
  String? orgLogo;
  int? orgId;
  String? mecRegdId;
  String? ssoToken;
  String? role;
  String? locale;
  int? languageId;
  String? englishName;
  int? isPrimaryLanguage;
  String? secondaryRole;
  int? currentSemesterID;

  User(
      {this.id,
      this.name,
      this.isTrainer,
      this.email,
      this.department,
      this.designation,
      this.mobileNo,
      this.profileImage,
      this.categoryIds,
      this.defaultVideoUrlOnCategory,
      this.orgLogo,
      this.orgId,
      this.mecRegdId,
      this.ssoToken,
      this.role,
      this.locale,
      this.languageId,
      this.englishName,
      this.isPrimaryLanguage,
      this.secondaryRole,
        this.currentSemesterID,
      });

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    department = json['department'];
    designation = json['designation'];
    mobileNo = json['mobile_no'] is String
        ? json['mobile_no']
        : json['mobile_no'].toString();
    profileImage = json['profile_image'];
    isTrainer = json['is_trainer'];
    categoryIds = json['category_ids'];
    defaultVideoUrlOnCategory = json['default_video_url_on_category'];
    orgLogo = json['org_logo'];
    orgId = json['organization_id'];
    mecRegdId = json['mec_regd_id'];
    ssoToken = json['sso_token'];
    role = json['role'];
    locale = json['locale'];
    languageId = json['language_id'];
    englishName = json['english_name'];
    isPrimaryLanguage = json['is_primary_language'];
    secondaryRole = json['secondary_role'];
    currentSemesterID = json['current_semester_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['email'] = this.email;
    data['department'] = this.department;
    data['designation'] = this.designation;
    data['mobile_no'] = this.mobileNo;
    data['profile_image'] = this.profileImage;
    data['is_trainer'] = this.isTrainer;
    data['category_ids'] = this.categoryIds;
    data['default_video_url_on_category'] = this.defaultVideoUrlOnCategory;
    data['org_logo'] = this.orgLogo;
    data['organization_id'] = this.orgId;
    data['mec_regd_id'] = this.mecRegdId;
    data['sso_token'] = this.ssoToken;
    data['role'] = this.role;
    data['locale'] = this.locale;
    data['language_id'] = this.languageId;
    data['english_name'] = this.englishName;
    data['is_primary_language'] = this.isPrimaryLanguage;
    data['secondary_role'] = this.secondaryRole;
    data['current_semester_id'] = this.currentSemesterID;

    return data;
  }
}

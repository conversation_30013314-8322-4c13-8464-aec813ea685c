// class ExploreJobListResponse {
//   int? status;
//   Data? data;
//   List<dynamic>? error;

//   ExploreJobListResponse({this.status, this.data, this.error});

//   ExploreJobListResponse.fromJson(Map<String, dynamic> json) {
//     status = json['status'];
//     data = json['data'] != null ? new Data.fromJson(json['data']) : null;
//     error = List<dynamic>.from(json["error"].map((x) => x));
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['status'] = this.status;
//     if (this.data != null) {
//       data['data'] = this.data!.toJson();
//     }
//     if (this.error != null) {
//       data['error'] = this.error!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class Data {
//   int? maxDate;
//   String? totalJobs;
//   List<ExploreJob>? exploreJob;

//   Data({this.maxDate, this.totalJobs, this.exploreJob});

//   Data.fromJson(Map<String, dynamic> json) {
//     maxDate = json['max_date'];
//     totalJobs = json['total_jobs'].toString();
//     if (json['explore_job'] != null) {
//       exploreJob = <ExploreJob>[];
//       json['explore_job'].forEach((v) {
//         exploreJob!.add(new ExploreJob.fromJson(v));
//       });
//     }
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['max_date'] = this.maxDate;
//     data['total_jobs'] = this.totalJobs;
//     if (this.exploreJob != null) {
//       data['explore_job'] = this.exploreJob!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class ExploreJob {
//   String? company;
//   String? experience;
//   String? jD;
//   String? location;
//   String? post;
//   int? resumeRating;
//   String? jobId;
//   String? salary;
//   String? resumeType;
//   String? logo;
//   List<Skills>? skills;
//   String? updateDate;
//   int? isVerified;
//   int? jobPostDateTime;

//   ExploreJob(
//       {this.company,
//       this.experience,
//       this.jD,
//       this.location,
//       this.post,
//       this.resumeRating,
//       this.jobId,
//       this.salary,
//       this.resumeType,
//       this.logo,
//       this.skills,
//       this.updateDate,
//       this.isVerified,
//       this.jobPostDateTime});

//   ExploreJob.fromJson(Map<String, dynamic> json) {
//     company = json['Company'];
//     experience = json['Experience'];
//     jD = json['JD'];
//     location = json['Location'];
//     post = json['Post'];
//     resumeRating = json['Resume Rating'];
//     jobId = json['job_id'];
//     salary = json['salary'];
//     resumeType = json['resume_type'];
//     logo = json['logo'];
//     if (json['skills'] != null) {
//       skills = <Skills>[];
//       json['skills'].forEach((v) {
//         skills!.add(new Skills.fromJson(v));
//       });
//     }
//     updateDate = json['update_date'];
//     isVerified = json['isVerified'];
//     jobPostDateTime = json['job_posted_date_time'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['Company'] = this.company;
//     data['Experience'] = this.experience;
//     data['JD'] = this.jD;
//     data['Location'] = this.location;
//     data['Post'] = this.post;
//     data['Resume Rating'] = this.resumeRating;
//     data['job_id'] = this.jobId;
//     data['salary'] = this.salary;
//     data['resume_type'] = this.resumeType;
//     data['logo'] = this.logo;
//     data['isVerified'] = this.isVerified;
//     if (this.skills != null) {
//       data['skills'] = this.skills!.map((v) => v.toJson()).toList();
//     }
//     data['update_date'] = this.updateDate;
//     data['job_posted_date_time'] = this.jobPostDateTime;
//     return data;
//   }
// }

// class Skills {
//   int? id;
//   String? interestarea;
//   String? subAreaOfInterest;
//   String? jobRoles;
//   String? skills;
//   int? wtgPercent;
//   String? skillDescription;
//   String? keyword;
//   String? functionalDomains;
//   Null? addedBy;
//   String? addedDate;
//   Null? updatedBy;
//   Null? lastupdated;
//   String? status;
//   String? jobRoleDescription;
//   int? interestAreaId;
//   int? subinterestAreaId;
//   String? createdAt;
//   String? updatedAt;

//   Skills(
//       {this.id,
//       this.interestarea,
//       this.subAreaOfInterest,
//       this.jobRoles,
//       this.skills,
//       this.wtgPercent,
//       this.skillDescription,
//       this.keyword,
//       this.functionalDomains,
//       this.addedBy,
//       this.addedDate,
//       this.updatedBy,
//       this.lastupdated,
//       this.status,
//       this.jobRoleDescription,
//       this.interestAreaId,
//       this.subinterestAreaId,
//       this.createdAt,
//       this.updatedAt});

//   Skills.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     interestarea = json['interestarea'];
//     subAreaOfInterest = json['sub_area_of_interest'];
//     jobRoles = json['job_roles'];
//     skills = json['skills'];
//     wtgPercent = json['wtg_percent'];
//     skillDescription = json['skill_description'];
//     keyword = json['keyword'];
//     functionalDomains = json['functional_domains'];
//     addedBy = json['added_by'];
//     addedDate = json['added_date'];
//     updatedBy = json['updated_by'];
//     lastupdated = json['lastupdated'];
//     status = json['status'];
//     jobRoleDescription = json['job_role_description'];
//     interestAreaId = json['interest_area_id'];
//     subinterestAreaId = json['subinterest_area_id'];
//     createdAt = json['created_at'];
//     updatedAt = json['updated_at'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['interestarea'] = this.interestarea;
//     data['sub_area_of_interest'] = this.subAreaOfInterest;
//     data['job_roles'] = this.jobRoles;
//     data['skills'] = this.skills;
//     data['wtg_percent'] = this.wtgPercent;
//     data['skill_description'] = this.skillDescription;
//     data['keyword'] = this.keyword;
//     data['functional_domains'] = this.functionalDomains;
//     data['added_by'] = this.addedBy;
//     data['added_date'] = this.addedDate;
//     data['updated_by'] = this.updatedBy;
//     data['lastupdated'] = this.lastupdated;
//     data['status'] = this.status;
//     data['job_role_description'] = this.jobRoleDescription;
//     data['interest_area_id'] = this.interestAreaId;
//     data['subinterest_area_id'] = this.subinterestAreaId;
//     data['created_at'] = this.createdAt;
//     data['updated_at'] = this.updatedAt;
//     return data;
//   }
// }

// To parse this JSON data, do
//
//     final exploreJobListResponse = exploreJobListResponseFromJson(jsonString);

import 'dart:convert';

ExploreJobListResponse exploreJobListResponseFromJson(String str) =>
    ExploreJobListResponse.fromJson(json.decode(str));

String exploreJobListResponseToJson(ExploreJobListResponse data) =>
    json.encode(data.toJson());

class ExploreJobListResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  ExploreJobListResponse({
    this.status,
    this.data,
    this.error,
  });

  factory ExploreJobListResponse.fromJson(Map<String, dynamic> json) =>
      ExploreJobListResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null
            ? []
            : List<dynamic>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  int? maxDate;
  int? totalJobs;
  List<ExploreJob>? exploreJob;

  Data({
    this.maxDate,
    this.totalJobs,
    this.exploreJob,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        maxDate: json["max_date"],
        totalJobs: json["total_jobs"],
        exploreJob: json["explore_job"] == null
            ? []
            : List<ExploreJob>.from(
                json["explore_job"]!.map((x) => ExploreJob.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "max_date": maxDate,
        "total_jobs": totalJobs,
        "explore_job": exploreJob == null
            ? []
            : List<dynamic>.from(exploreJob!.map((x) => x.toJson())),
      };
}

class ExploreJob {
  String? company;
  String? experience;
  String? location;
  String? post;
  //int? resumeRating;
  double? resumeRating;
  int? jobId;
  String? resumeType;
  int? isVerified;
  String? jobPostedDate;
  dynamic jobPostedDateTime;
  String? logo;
  List<Skill>? skills;
  dynamic ratingDetail;
  dynamic updateDate;
  dynamic programId;

  ExploreJob({
    this.company,
    this.experience,
    this.location,
    this.post,
    this.resumeRating,
    this.jobId,
    this.resumeType,
    this.isVerified,
    this.jobPostedDate,
    this.jobPostedDateTime,
    this.logo,
    this.skills,
    this.ratingDetail,
    this.updateDate,
    this.programId,
  });

  factory ExploreJob.fromJson(Map<String, dynamic> json) => ExploreJob(
      company: json["Company"],
      experience: json["Experience"],
      location: json["Location"],
      post: json["Post"],
      resumeRating: json["Resume Rating"],
      jobId: json["job_id"],
      resumeType: json["resume_type"],
      isVerified: json["isVerified"],
      jobPostedDate: json["job_posted_date"],
      jobPostedDateTime: json["job_posted_date_time"],
      logo: json["logo"],
      skills: json["skills"] == null
          ? []
          : List<Skill>.from(json["skills"]!.map((x) => Skill.fromJson(x))),
      ratingDetail: json["rating_detail"],
      //  == null
      // ? null
      // : RatingDetail.fromJson(json["rating_detail"]),
      updateDate: json["update_date"] == null
          ? null
          : DateTime.parse(json["update_date"]),
      programId: json["program_id"]);

  Map<String, dynamic> toJson() => {
        "Company": company,
        "Experience": experience,
        "Location": location,
        "Post": post,
        "Resume Rating": resumeRating,
        "job_id": jobId,
        "resume_type": resumeType,
        "isVerified": isVerified,
        "job_posted_date": jobPostedDate,
        "job_posted_date_time": jobPostedDateTime,
        "logo": logo,
        "skills": skills == null
            ? []
            : List<dynamic>.from(skills!.map((x) => x.toJson())),
        "rating_detail": ratingDetail,
        "update_date": updateDate,
        "program_id": programId
      };
}

class RatingDetail {
  int? stars;
  String? ratingLabel;
  String? colorClass;

  RatingDetail({
    this.stars,
    this.ratingLabel,
    this.colorClass,
  });

  factory RatingDetail.fromJson(Map<String, dynamic> json) => RatingDetail(
        stars: json["stars"],
        ratingLabel: json["rating_label"],
        colorClass: json["color_class"],
      );

  Map<String, dynamic> toJson() => {
        "stars": stars,
        "rating_label": ratingLabel,
        "color_class": colorClass,
      };
}

class Skill {
  int? id;
  String? skills;
  int? userMatch;

  Skill({this.id, this.skills, this.userMatch});

  factory Skill.fromJson(Map<String, dynamic> json) => Skill(
      id: json["id"], skills: json["skills"], userMatch: json["user_matched"]);

  Map<String, dynamic> toJson() =>
      {"id": id, "skills": skills, "user_matched": userMatch};
}

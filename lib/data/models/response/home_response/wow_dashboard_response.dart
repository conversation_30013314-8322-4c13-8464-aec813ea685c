// To parse this JSON data, do
//
//     final wowDashboardResponse = wowDashboardResponseFromJson(jsonString);

import 'dart:convert';

WowDashboardResponse wowDashboardResponseFromJson(String str) => WowDashboardResponse.fromJson(json.decode(str));

String wowDashboardResponseToJson(WowDashboardResponse data) => json.encode(data.toJson());

class WowDashboardResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    WowDashboardResponse({
        this.status,
        this.data,
        this.error,
    });

    factory WowDashboardResponse.fromJson(Map<String, dynamic> json) => WowDashboardResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    Map<String, List<dynamic>>? jobVacanciesGraph;
    WowDetails? wowDetails;
    List<JobOpportunity>? jobOpportunities;
    MatchingProfile? matchingProfile;
    Map<String, UserSkillAssessmentWOW>? userSkillAssessment;

    Data({
        this.jobVacanciesGraph,
        this.wowDetails,
        this.jobOpportunities,
        this.matchingProfile,
        this.userSkillAssessment,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        jobVacanciesGraph: Map.from(json["job_vacancies_graph"]!).map((k, v) => MapEntry<String, List<dynamic>>(k, List<dynamic>.from(v.map((x) => x)))),
        wowDetails: json["wow_details"] == null ? null : WowDetails.fromJson(json["wow_details"]),
        jobOpportunities: json["job_opportunities"] == null ? [] : List<JobOpportunity>.from(json["job_opportunities"]!.map((x) => JobOpportunity.fromJson(x))),
        matchingProfile: json["matching_profile"] == null ? null : MatchingProfile.fromJson(json["matching_profile"]),

        //userSkillAssessment: json['user_skill_assessment'] == null ? null : (json['user_skill_assessment'] as Map<String, dynamic>)


        // userSkillAssessment: (json['user_skill_assessment'] as List?)?.isEmpty ?? true
        //     ? null : (json['user_skill_assessment'] as Map<String, dynamic>)
        //     .map((key, value) => MapEntry(key, UserSkillAssessmentWOW.fromJson(value as Map<String, dynamic>))),

        userSkillAssessment: (json['user_skill_assessment'] == null ||
            (json['user_skill_assessment'] is List &&
                (json['user_skill_assessment'] as List).isEmpty) ||
            (json['user_skill_assessment'] is Map &&
                (json['user_skill_assessment'] as Map).isEmpty))
            ? null : (json['user_skill_assessment'] as Map<String, dynamic>)
            .map((key, value) => MapEntry(key, UserSkillAssessmentWOW.fromJson(value as Map<String, dynamic>))),

    );

    Map<String, dynamic> toJson() => {
        "job_vacancies_graph": Map.from(jobVacanciesGraph!).map((k, v) => MapEntry<String, dynamic>(k, List<dynamic>.from(v.map((x) => x)))),
        "wow_details": wowDetails?.toJson(),
        "job_opportunities": jobOpportunities == null ? [] : List<dynamic>.from(jobOpportunities!.map((x) => x.toJson())),
        "matching_profile": matchingProfile?.toJson(),
        "user_skill_assessment": userSkillAssessment,
    };
}

class JobOpportunity {
    int? id;
    String? name;
    String? image;

    JobOpportunity({
        this.id,
        this.name,
        this.image,
    });

    factory JobOpportunity.fromJson(Map<String, dynamic> json) => JobOpportunity(
        id: json["id"],
        name: json["name"],
        image: json["image"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
    };
}

class MatchingProfile {
    int? profileCompletion;
    int? matchingJobs;

    MatchingProfile({
        this.profileCompletion,
        this.matchingJobs,
    });

    factory MatchingProfile.fromJson(Map<String, dynamic> json) => MatchingProfile(
        profileCompletion: json["profile_completion"],
        matchingJobs: json["matching_jobs"],
    );

    Map<String, dynamic> toJson() => {
        "profile_completion": profileCompletion,
        "matching_jobs": matchingJobs,
    };
}

class WowDetails {
    String? jobPosting;
    String? jobVacancies;

    WowDetails({
        this.jobPosting,
        this.jobVacancies,
    });

    factory WowDetails.fromJson(Map<String, dynamic> json) => WowDetails(
        jobPosting: json["job_posting"],
        jobVacancies: json["job_vacancies"],
    );

    Map<String, dynamic> toJson() => {
        "job_posting": jobPosting,
        "job_vacancies": jobVacancies,
    };
}

class UserSkillAssessmentWOW {
    final int categoryId;
    final String categoryName;
    final List<Skill> skills;

    UserSkillAssessmentWOW({
        required this.categoryId,
        required this.categoryName,
        required this.skills,
    });

    factory UserSkillAssessmentWOW.fromJson(Map<String, dynamic> json) {
        var skillsList = (json['skills'] as List)
            .map((skillJson) => Skill.fromJson(skillJson))
            .toList();

        return UserSkillAssessmentWOW(
            categoryId: json['c_id'],
            categoryName: json['c_name'],
            skills: skillsList,
        );
    }
}

class Skill {
    final int id;
    final int? skillId;
    final String name;
    final String description;
    final int skillWeightage;
    final int? userWeightage;
    final double userWeightagePer;
    final int? userWeightagePerNo;
    final String userWeightageLabel;

    Skill({
        required this.id,
        this.skillId,
        required this.name,
        required this.description,
        required this.skillWeightage,
        this.userWeightage,
        required this.userWeightagePer,
        this.userWeightagePerNo,
        required this.userWeightageLabel,
    });

    factory Skill.fromJson(Map<String, dynamic> json) {
        return Skill(
            id: json['id'],
            skillId: json['skill_id'],
            name: json['name'],
            description: json['description'],
            skillWeightage: json['skill_weightage'],
            userWeightage: json['user_weightage'],
            userWeightagePer: double.parse(json['user_weightage_per'].toString()),
            userWeightageLabel: json['user_weightage_per_label'],
            userWeightagePerNo: json['user_weightage_per_no'],
        );
    }
}
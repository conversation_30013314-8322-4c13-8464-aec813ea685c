import 'dart:convert';

PortfolioResponse portfolioResponseFromJson(String str) =>
    PortfolioResponse.fromJson(json.decode(str));

String portfolioResponseToJson(PortfolioResponse data) =>
    json.encode(data.toJson());

class PortfolioResponse {
  PortfolioResponse({
    required this.status,
    required this.data,
    required this.error,
  });

  int status;
  Data data;
  List<dynamic> error;

  factory PortfolioResponse.fromJson(Map<String, dynamic> json) =>
      PortfolioResponse(
        status: json["status"],
        data: Data.fromJson(json["data"]),
        error: List<dynamic>.from(json["error"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data.toJson(),
        "error": List<dynamic>.from(error.map((x) => x)),
      };
}

class Data {
  Data({
    required this.image,
    required this.name,
    required this.openToWork,
    required this.qrCode,
    required this.resume,
    required this.profileVideo,
    required this.education,
    required this.experience,
    required this.certificate,
    required this.extraActivities,
    required this.portfolioSocial,
    required this.portfolioProfile,
    required this.portfolio,
    required this.baseFileUrl,
    required this.recentActivity,
    required this.profileCompletion,
    this.skill,
    this.resumeParserDataCount,
    this.sampleResumes,
  });

  String image;
  String name;
  String? openToWork;
  String? qrCode;
  List<Resume> resume;
  String profileVideo;
  List<CommonProfession> education;
  List<CommonProfession> experience;
  List<CommonProfession> certificate;
  List<CommonProfession> extraActivities;
  //dynamic portfolioSocial;
  List portfolioSocial;
  List<PortfolioProfile> portfolioProfile;
  List<Portfolio> portfolio;
  String baseFileUrl;
  List<RecentActivity> recentActivity;
  int profileCompletion;
  List<CommonProfession>? skill;
  int? resumeParserDataCount;
  List<SampleResume>? sampleResumes;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        image: json["image"],
        name: json["name"],
        openToWork: json["open_to_work"],
        qrCode: json["qr_code"],
        resume:
            List<Resume>.from(json["resume"].map((x) => Resume.fromJson(x))),
        profileVideo: json["profile_video"],
        profileCompletion: int.parse('${json["profile_completion"] ?? 0}'),
        education: List<CommonProfession>.from(
            json["Education"].map((x) => CommonProfession.fromJson(x))),
        experience: List<CommonProfession>.from(json["Experience"]
            .reversed
            .map((x) => CommonProfession.fromJson(x))),
        // certificate: List<CommonProfession>.from(json["Certificate"].reversed.map((x) => CommonProfession.fromJson(x))),
        certificate: List<CommonProfession>.from(
            json["Certificate"].map((x) => CommonProfession.fromJson(x))),
        extraActivities: List<CommonProfession>.from(
            json["extra_activities"].map((x) => CommonProfession.fromJson(x))),
        portfolioSocial: json["portfolio_social"],
        portfolioProfile: List<PortfolioProfile>.from(
            json["portfolio_profile"].map((x) => PortfolioProfile.fromJson(x))),
        portfolio: json.containsKey("portfolio") == true
            ? List<Portfolio>.from(
                json["portfolio"].reversed.map((x) => Portfolio.fromJson(x)))
            : [],
        baseFileUrl: json.containsKey("base_file_url") == true
            ? json["base_file_url"]
            : "",
        recentActivity: List<RecentActivity>.from(
            json["recent_activity"].map((x) => RecentActivity.fromJson(x))),
        skill: List<CommonProfession>.from(
            json["skill"].map((x) => CommonProfession.fromJson(x))),

        resumeParserDataCount: json["resume_parser_data_count"],
        sampleResumes: json["sampleResumes"] == null
            ? []
            : List<SampleResume>.from(
                json["sampleResumes"]!.map((x) => SampleResume.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "image": image,
        "name": name,
        "open_to_work": openToWork,
        "qr_code": qrCode,
        "resume": List<dynamic>.from(resume.map((x) => x.toJson())),
        "profile_video": profileVideo,
        "Education": List<dynamic>.from(education.map((x) => x.toJson())),
        "Experience":
            List<dynamic>.from(experience.map((x) => x.toJson())).reversed,
        "Certificate":
            List<dynamic>.from(certificate.map((x) => x.toJson())).reversed,
        "extra_activities":
            List<dynamic>.from(extraActivities.map((x) => x.toJson())),
        "portfolio_social": portfolioSocial,
        "portfolio_profile":
            List<dynamic>.from(portfolioProfile.map((x) => x.toJson())),
        "portfolio":
            List<dynamic>.from(portfolio.map((x) => x.toJson())).reversed,
        "base_file_url": baseFileUrl,
        "profile_completion": profileCompletion,
        "recent_activity":
            List<dynamic>.from(recentActivity.map((x) => x.toJson())),
        "skill": List<dynamic>.from(skill!.map((x) => x.toJson())),
        "resume_parser_data_count": resumeParserDataCount,
        "sampleResumes": sampleResumes == null
            ? []
            : List<dynamic>.from(sampleResumes!.map((x) => x.toJson())),
      };
}

class SampleResume {
  String? image;
  String? url;

  SampleResume({
    this.image,
    this.url,
  });

  factory SampleResume.fromJson(Map<String, dynamic> json) => SampleResume(
        image: json["image"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "image": image,
        "url": url,
      };
}

class CommonProfession {
  CommonProfession(
      {required this.activityType,
      required this.title,
      required this.description,
      required this.startDate,
      required this.endDate,
      required this.institute,
      required this.certificate,
      required this.action,
      required this.professionalKey,
      required this.editUrlProfessional,
      required this.employmentType,
      required this.currentlyWorkHere,
      required this.curricularType,
      required this.imageName,
      required this.id,
      this.percent});

  String activityType;
  String title;
  String description;
  String startDate;
  String endDate;
  String institute;
  String certificate;
  String action;
  String professionalKey;
  String editUrlProfessional;
  String employmentType;
  String currentlyWorkHere;
  String curricularType;
  String imageName;
  int id;
  String? percent;

  factory CommonProfession.fromJson(Map<String, dynamic> json) =>
      CommonProfession(
          activityType: json["activity_type"],
          title: json["title"],
          description: json["description"],
          startDate: json["start_date"],
          endDate: json["end_date"],
          institute: json["institute"],
          certificate: json["certificate"],
          action: json["action"],
          professionalKey: json["professional_key"],
          editUrlProfessional: json["edit_url_professional"],
          employmentType: json["employment_type"],
          currentlyWorkHere: json["currently_work_here"],
          curricularType: json["curricular_type"],
          imageName: json["image_name"],
          id: json["id"],
          percent: json["percentage"]);

  Map<String, dynamic> toJson() => {
        "activity_type": activityType,
        "title": title,
        "description": description,
        "start_date": startDate,
        "end_date": endDate,
        "institute": institute,
        "certificate": certificate,
        "action": action,
        "professional_key": professionalKey,
        "edit_url_professional": editUrlProfessional,
        "employment_type": employmentType,
        "currently_work_here": currentlyWorkHere,
        "curricular_type": curricularType,
        "image_name": imageName,
        "id": id,
        "percentage": percent
      };
}

class Portfolio {
  Portfolio({
    required this.portfolioTitle,
    required this.portfolioLink,
    required this.action,
    required this.portfolioKey,
    required this.editUrlPortfolio,
    required this.editImageType,
    required this.desc,
    required this.imageName,
    required this.portfolioFile,
    required this.id,
  });

  String portfolioTitle;
  String portfolioLink;
  String action;
  String portfolioKey;
  String editUrlPortfolio;
  String editImageType;
  String desc;
  String imageName;
  String portfolioFile;
  int id;

  factory Portfolio.fromJson(Map<String, dynamic> json) => Portfolio(
        portfolioTitle: json["portfolio_title"],
        portfolioLink: json["portfolio_link"],
        action: json["action"],
        portfolioKey: json["portfolio_key"],
        editUrlPortfolio: json["edit_url_portfolio"],
        editImageType: json["edit_image_type"],
        desc: json["desc"],
        imageName: json["image_name"],
        portfolioFile: json["portfolio_file"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "portfolio_title": portfolioTitle,
        "portfolio_link": portfolioLink,
        "action": action,
        "portfolio_key": portfolioKey,
        "edit_url_portfolio": editUrlPortfolio,
        "edit_image_type": editImageType,
        "desc": desc,
        "image_name": imageName,
        "portfolio_file": portfolioFile,
        "id": id,
      };
}

class PortfolioProfile {
  PortfolioProfile({
    required this.headline,
    required this.country,
    required this.city,
    required this.aboutMe,
    required this.id,
  });

  String headline;
  String country;
  String city;
  String? aboutMe;
  int id;

  factory PortfolioProfile.fromJson(Map<String, dynamic> json) =>
      PortfolioProfile(
        headline: json["headline"],
        country: json["country"],
        city: json["city"],
        aboutMe: json["about_me"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "headline": headline,
        "country": country,
        "city": city,
        "about_me": aboutMe,
        "id": id,
      };
}


class Resume {
  Resume({
    required this.url,
    required this.id,
  });

  String url;
  int id;

  factory Resume.fromJson(Map<String, dynamic> json) => Resume(
        url: json["url"] != null ? json["url"] : '',
        id: json["id"] != null ? json["id"] : 0,
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "id": id,
      };
}

class RecentActivity {
  RecentActivity(
      {this.id,
      this.viewCount,
      this.title,
      this.description,
      this.resourcePath,
      this.resourcePathThumbnail,
      this.contentType,
      this.categoryId,
      this.profileImage,
      this.name,
      this.createdAt,
      this.createdAtTs,
      this.reserved});

  int? id;
  int? viewCount;
  String? title;
  String? description;
  String? resourcePath;
  String? resourcePathThumbnail;
  String? contentType;
  int? categoryId;
  String? profileImage;
  String? name;
  String? createdAt;
  int? createdAtTs;
  String? reserved;

  factory RecentActivity.fromJson(Map<String, dynamic> json) => RecentActivity(
        id: json["id"],
        viewCount: json["view_count"],
        title: json["title"] ?? "",
        description: json["description"],
        resourcePath: json["resource_path"],
        resourcePathThumbnail: json["resource_path_thumbnail"] ?? "",
        contentType: json["content_type"],
        categoryId: json["category_id"],
        profileImage: json["profile_image"],
        name: json["name"],
        createdAt: json['created_at'],
        // createdAtTs: json['created_at_ts'],
        reserved: json['reserved'],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "view_count": viewCount,
        "title": title,
        "description": description,
        "resource_path": resourcePath,
        "resource_path_thumbnail": resourcePathThumbnail,
        "content_type": contentType,
        "category_id": categoryId,
        "profile_image": profileImage,
        "name": name,
        "created_at": createdAt,
        // "created_at_ts": createdAtTs,
        "reserved": reserved
      };
}

class ExploreJobDetailsResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  ExploreJobDetailsResponse({this.status, this.data, this.error});

  ExploreJobDetailsResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    error = List<dynamic>.from(json["error"].map((x) => x));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (this.error != null) {
      data['error'] = this.error!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  ResArr? resArr;
  List<String>? skills;
  List<Program>? program;

  Data({this.resArr, this.skills, this.program});

  Data.fromJson(Map<String, dynamic> json) {
    resArr =
        json['resArr'] != null ? new ResArr.fromJson(json['resArr']) : null;
    skills = json['skills'].cast<String>();
    if (json['program'] != null) {
      program = <Program>[];
      json['program'].forEach((v) {
        program!.add(new Program.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resArr != null) {
      data['resArr'] = this.resArr!.toJson();
    }
    data['skills'] = this.skills;
    if (this.program != null) {
      data['program'] = this.program!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResArr {
  int? id;
  String? designation;
  String? location;
  String? jobinfo;
  String? description;
  String? minExperience;
  String? maxExperience;
  String? company;
  String? jdUrl;
  String? vacancies;
  String? logoUrl;
  String? teLogoUrl;
  String? whiteListedKeywords;
  String? keywords;
  String? keywordsAr;
  String? email;
  String? isEasyApply;
  String? jobId;
  String? jobPostedDateTime;
  String? minSalary;
  String? maxSalary;
  String? country;
  String? state;
  String? city;
  String? functionalDomain;
  String? sectorIndustryDomain;
  String? icon;
  String? skills;
  String? programIds;
  int? gulfId;
  String? education;
  String? nationality;
  String? gender;
  String? chartKey;
  String? jobRole;
  String? jobDesignation;
  String? status;
  String? createdAt;
  String? updatedAt;
  int? jobPostedDateTimestamp;
  int? isApplied;

  ResArr(
      {this.id,
      this.designation,
      this.location,
      this.jobinfo,
      this.description,
      this.minExperience,
      this.maxExperience,
      this.company,
      this.jdUrl,
      this.vacancies,
      this.logoUrl,
      this.teLogoUrl,
      this.whiteListedKeywords,
      this.keywords,
      this.keywordsAr,
      this.email,
      this.isEasyApply,
      this.jobId,
      this.jobPostedDateTime,
      this.minSalary,
      this.maxSalary,
      this.country,
      this.state,
      this.city,
      this.functionalDomain,
      this.sectorIndustryDomain,
      this.icon,
      this.skills,
      this.programIds,
      this.gulfId,
      this.education,
      this.nationality,
      this.gender,
      this.chartKey,
      this.jobRole,
      this.jobDesignation,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.jobPostedDateTimestamp,
      this.isApplied});

  ResArr.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    designation = json['designation'];
    location = json['location'];
    jobinfo = json['jobinfo'];
    description = json['description'];
    minExperience = json['min_experience'];
    maxExperience = json['max_experience'];
    company = json['company'];
    jdUrl = json['jd_url'];
    vacancies = json['vacancies'];
    logoUrl = json['logo_url'];
    teLogoUrl = json['te_logo_url'];
    whiteListedKeywords = json['white_listed_keywords'];
    keywords = json['keywords'];
    keywordsAr = json['keywords_ar'];
    email = json['email'];
    isEasyApply = json['is_easy_apply'];
    jobId = json['job_id'];
    jobPostedDateTime = json['job_posted_date_time'];
    minSalary = json['min_Salary'];
    maxSalary = json['max_Salary'];
    country = json['Country'];
    state = json['State'];
    city = json['City'];
    functionalDomain = json['functional_Domain'];
    sectorIndustryDomain = json['sector_industry_Domain'];
    icon = json['icon'];
    skills = json['skills'];
    programIds = json['program_ids'];
    gulfId = json['gulf_id'];
    education = json['education'];
    nationality = json['nationality'];
    gender = json['gender'];
    chartKey = json['chart_key'];
    jobRole = json['job_role'];
    jobDesignation = json['job_designation'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    isApplied = json['is_applied'];
    jobPostedDateTimestamp =
        int.tryParse('${json['job_posted_date_timestamp']}') ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['designation'] = this.designation;
    data['location'] = this.location;
    data['jobinfo'] = this.jobinfo;
    data['description'] = this.description;
    data['min_experience'] = this.minExperience;
    data['max_experience'] = this.maxExperience;
    data['company'] = this.company;
    data['jd_url'] = this.jdUrl;
    data['vacancies'] = this.vacancies;
    data['logo_url'] = this.logoUrl;
    data['te_logo_url'] = this.teLogoUrl;
    data['white_listed_keywords'] = this.whiteListedKeywords;
    data['keywords'] = this.keywords;
    data['keywords_ar'] = this.keywordsAr;
    data['email'] = this.email;
    data['is_easy_apply'] = this.isEasyApply;
    data['job_id'] = this.jobId;
    data['job_posted_date_time'] = this.jobPostedDateTime;
    data['min_Salary'] = this.minSalary;
    data['max_Salary'] = this.maxSalary;
    data['Country'] = this.country;
    data['State'] = this.state;
    data['City'] = this.city;
    data['functional_Domain'] = this.functionalDomain;
    data['sector_industry_Domain'] = this.sectorIndustryDomain;
    data['icon'] = this.icon;
    data['skills'] = this.skills;
    data['program_ids'] = this.programIds;
    data['gulf_id'] = this.gulfId;
    data['education'] = this.education;
    data['nationality'] = this.nationality;
    data['gender'] = this.gender;
    data['chart_key'] = this.chartKey;
    data['job_role'] = this.jobRole;
    data['job_designation'] = this.jobDesignation;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['is_applied'] = this.isApplied;
    data['job_posted_date_timestamp'] = this.jobPostedDateTimestamp;
    return data;
  }
}

class Program {
  String? programName;

  Program({this.programName});

  Program.fromJson(Map<String, dynamic> json) {
    programName = json['program_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['program_name'] = this.programName;
    return data;
  }
}

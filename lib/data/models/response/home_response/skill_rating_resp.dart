class SkillRatingResponse {
  int? status;
  List<Skill>? data;
  List<dynamic>? error;

  SkillRatingResponse({this.status, this.data, this.error});

  SkillRatingResponse.fromJson(Map<String, dynamic> json) {
    print('from json called');
    status = json['status'];
    if (json['data'] != null) {
      data = <Skill>[];
      json['data'].forEach((v) {
        data?.add(new Skill.fromJson(v));
      });
      if (data?.length != 0)
        data?.sort((a, b) => b.selfProficiency!.compareTo(a.selfProficiency!));
    }
    if (json['error'] != null) {
      error = <dynamic>[];
      json['error'].forEach((v) {
        error?.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    print('to json called');

    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data?.map((v) => v.toJson()).toList();
    }
    if (this.error != null) {
      data['error'] = this.error?.map((v) => v).toList();
    }
    return data;
  }
}

class Skill {
  int? id;
  int? organizationId;
  int? userId;
  int? skillId;
  int? selfProficiency;
  dynamic assesedProficiency;
  dynamic assessmentContentId;
  dynamic assessmentScore;
  dynamic assesedDate;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? name;
  String? description;

  Skill(
      {this.id,
      this.organizationId,
      this.userId,
      this.skillId,
      this.selfProficiency,
      this.assesedProficiency,
      this.assessmentContentId,
      this.assessmentScore,
      this.assesedDate,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.name,
      this.description});

  Skill.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    organizationId = json['organization_id'];
    userId = json['user_id'];
    skillId = json['skill_id'];
    selfProficiency = json['self_proficiency'];
    assesedProficiency = json['assesed_proficiency'];
    assessmentContentId = json['assessment_content_id'];
    assessmentScore = json['assessment_score'];
    assesedDate = json['assesed_date'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    name = json['name'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['organization_id'] = this.organizationId;
    data['user_id'] = this.userId;
    data['skill_id'] = this.skillId;
    data['self_proficiency'] = this.selfProficiency;
    data['assesed_proficiency'] = this.assesedProficiency;
    data['assessment_content_id'] = this.assessmentContentId;
    data['assessment_score'] = this.assessmentScore;
    data['assesed_date'] = this.assesedDate;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['name'] = this.name;
    data['description'] = this.description;
    return data;
  }
}

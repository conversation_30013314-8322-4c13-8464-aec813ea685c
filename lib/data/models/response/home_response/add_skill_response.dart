class AddSkillResponse {
  int? status;
  String? data;
  List<String>? error;

  AddSkillResponse({this.status, this.data, this.error});

  AddSkillResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'];
    if (json['error'] != null) {
      error = <String>[];
      json['error'].forEach((v) {
        error?.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['data'] = this.data;
    if (this.error != null) {
      data['error'] = this.error?.map((v) => v).toList();
    }
    return data;
  }
}

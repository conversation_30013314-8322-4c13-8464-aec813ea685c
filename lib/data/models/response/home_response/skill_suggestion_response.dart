
class SkillSuggestionResponse {
  SkillSuggestionResponse({
    this.status,
    this.data,
    this.error,
  });
  int? status;
  List<Data>? data;
  List<dynamic>? error;
  
  SkillSuggestionResponse.fromJson(Map<String, dynamic> json){
    status = json['status'];
    data = List.from(json['data']).map((e)=>Data.fromJson(e)).toList();
    error = List.castFrom<dynamic, dynamic>(json['error']);
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['status'] = status;
    _data['data'] = data?.map((e)=>e.toJson()).toList();
    _data['error'] = error;
    return _data;
  }
}

class Data {
  Data({
    this.id,
    this.name,
     this.description,
    this.parentId,
    this.status,
    this.organizationId,
    this.createdAt,
    this.updatedAt,
     this.salary,
     this.growth,
     this.growthType,
  });
  int? id;
  String? name;
  String? description;
  int? parentId;
  String? status;
  int? organizationId;
  String? createdAt;
  String? updatedAt;
  String? salary;
  String? growth;
  String? growthType;
  
  Data.fromJson(Map<String, dynamic> json){
    id = json['id'];
    name = json['name'];
    description = null;
    parentId = json['parent_id'];
    status = json['status'];
    organizationId = json['organization_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    salary = null;
    growth = null;
    growthType = null;
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['id'] = id;
    _data['name'] = name;
    _data['description'] = description;
    _data['parent_id'] = parentId;
    _data['status'] = status;
    _data['organization_id'] = organizationId;
    _data['created_at'] = createdAt;
    _data['updated_at'] = updatedAt;
    _data['salary'] = salary;
    _data['growth'] = growth;
    _data['growth_type'] = growthType;
    return _data;
  }
}
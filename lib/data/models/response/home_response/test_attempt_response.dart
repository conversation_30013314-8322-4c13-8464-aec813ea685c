import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/resource/colors.dart';

class AttemptTestResponse {
  int? status;
  Data? data;
  List<String?>? error;

  AttemptTestResponse({this.status, this.data, this.error});

  AttemptTestResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = this.error;
    return data;
  }
}

class Data {
  AssessmentDetails? assessmentDetails;

  Data({this.assessmentDetails});

  Data.fromJson(Map<String, dynamic> json) {
    assessmentDetails = json['assessment_details'] != null
        ? new AssessmentDetails.fromJson(json['assessment_details'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.assessmentDetails != null) {
      data['assessment_details'] = this.assessmentDetails!.toJson();
    }
    return data;
  }
}

class AssessmentDetails {
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  dynamic maximumMarks;
  int? passingMarks;
  int? questionCount;
  int? negativeMarking;
  int? negativeMarks;
  int? totalAttempts;
  int? attemptCount;
  int? durationInMinutes;
  int? disableBackTracking;
  String? questionSequence;
  List<Questions>? questions;

  AssessmentDetails(
      {this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.maximumMarks,
      this.passingMarks,
      this.questionCount,
      this.negativeMarking,
      this.negativeMarks,
      this.totalAttempts,
      this.attemptCount,
      this.durationInMinutes,
      this.disableBackTracking,
      this.questionSequence,
      this.questions});

  AssessmentDetails.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    maximumMarks = json['maximum_marks'];
    passingMarks = json['passing_marks'];
    questionCount = json['question_count'];
    negativeMarking = json['negative_marking'];
    negativeMarks = json['negative_marks'];
    totalAttempts = json['total_attempts'];
    attemptCount = json['attempt_count'];
    durationInMinutes = json['duration_in_minutes'];
    disableBackTracking = json['disable_back_tracking'];
    questionSequence = json['question_sequence'];
    if (json['questions'] != null) {
      questions = <Questions>[];
      json['questions'].forEach((v) {
        questions!.add(new Questions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['description'] = this.description;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['maximum_marks'] = this.maximumMarks;
    data['passing_marks'] = this.passingMarks;
    data['question_count'] = this.questionCount;
    data['negative_marking'] = this.negativeMarking;
    data['negative_marks'] = this.negativeMarks;
    data['total_attempts'] = this.totalAttempts;
    data['attempt_count'] = this.attemptCount;
    data['duration_in_minutes'] = this.durationInMinutes;
    data['disable_back_tracking'] = this.disableBackTracking;
    data['question_sequence'] = this.questionSequence;
    if (this.questions != null) {
      data['questions'] = this.questions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Questions {
  int? questionId;
  String? question;
  String? questionType;
  int? questionTypeId;
  int? negativeMarks;
  //int? marks;
  dynamic marks;
  int? attempted;
  String? difficultyLevel;
  List<int?> selectedOption = [];
  List<String>? fillStringValue = [];
  List<dynamic>? selectedMatchingOption = [];
  List<dynamic>? correctAnswerStatement = [];
  Map<int, List<String>> selectedMatchingOptionForIndex = {};
  int? timeTaken;
  List<Options>? options;
  bool? bookMark;
  List<dynamic>? questionImage = [];
  String? responseMedium;
  String? userFile;
  String? answerStatement;
  int? questionTimer;
  QuestionLabel? questionLabel;
  String? blankHtml;

  Questions(
      {this.questionId,
      this.question,
      this.questionType,
      this.questionTypeId,
      this.negativeMarks,
      this.marks,
      this.attempted,
      this.difficultyLevel,
      this.correctAnswerStatement,
      this.timeTaken,
      this.options,
      this.questionImage,
      this.responseMedium,
      this.userFile,
      this.answerStatement,
      this.questionTimer,
      this.questionLabel,
      this.blankHtml});

  Questions.fromJson(Map<String, dynamic> json) {
    questionId = json['question_id'];
    question = json['question'];
    questionType = json['question_type'];
    questionTypeId = json['question_type_id'];
    negativeMarks = json['negative_marks'];
    marks = json['marks'];
    attempted = json['attempted'];
    difficultyLevel = '${tr('${json['difficulty_level'].toString().toLowerCase()}')}';
    //correctAnswerStatement = json['correct_answer_statement'];
    if (json['correct_answer_statement'] != null && json['correct_answer_statement'] is List) {
      correctAnswerStatement = json['correct_answer_statement'] as List<dynamic>;
    }
    timeTaken = json['time_taken'];
    questionImage = json['question_image'];
    responseMedium = json['response_medium'];
    userFile = json.containsKey('userFile') ? json['userFile'] : null;
    answerStatement = json.containsKey('answer_statement') ? json['answer_statement'] : null;
    if (json['options'] != null) {
      options = <Options>[];
      json['options'].forEach((v) {
        options!.add(new Options.fromJson(v));
      });
    }
    if (json['question_image'] != null) {
      questionImage = <String>[];
      json['question_image'].forEach((v) {
        questionImage!.add(v);
      });
    }
    questionTimer = json['question_timer'];
    if(json['question_label'] != null){
      questionLabel= QuestionLabel.fromJson(json['question_label']);
    }

    blankHtml = json.containsKey('blank_html') ? json['blank_html'] : null;

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['question_id'] = this.questionId;
    data['question'] = this.question;
    data['question_type'] = this.questionType;
    data['question_type_id'] = this.questionTypeId;
    data['negative_marks'] = this.negativeMarks;
    data['marks'] = this.marks;
    data['attempted'] = this.attempted;
    data['difficulty_level'] = this.difficultyLevel;
    data['correct_answer_statement'] = this.correctAnswerStatement;
    data['time_taken'] = this.timeTaken;
    data['response_medium'] = this.responseMedium;
    data['user_file'] = this.userFile;
    data['answer_statement'] = this.answerStatement;
    if (this.questionImage != null) {
      data['question_image'] = this.questionImage!.map((v) => v).toList();
    }
    if (this.options != null) {
      data['options'] = this.options!.map((v) => v.toJson()).toList();
    }
    data['question_timer'] = this.questionTimer;
    data['question_label'] = this.questionLabel;
    data['blank_html'] = this.blankHtml;
    return data;
  }
}

class Options {
  int? optionId;
  String? optionStatement;
  String? optionImage;
  int? attempted;
  bool selected = false;

  Options({this.optionId, this.optionStatement, this.optionImage, this.attempted});

  Options.fromJson(Map<String, dynamic> json) {
    optionId = json['option_id'];
    optionStatement = json['option_statement'];
    optionImage = json['option_image'];
    attempted = json['attempted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['option_id'] = this.optionId;
    data['option_statement'] = this.optionStatement;
    data['option_image'] = this.optionImage;
    data['attempted'] = this.attempted;
    return data;
  }
}

class QuestionLabel {
  final int? id;
  final String? name;
  final String? description;
  final String? image;
  final int? contentId;
  final String? createdAt;
  final String? updatedAt;

  QuestionLabel({
    this.id,
    this.name,
    this.description,
    this.image,
    this.contentId,
    this.createdAt,
    this.updatedAt,
  });

  factory QuestionLabel.fromJson(Map<String, dynamic> json) {
    return QuestionLabel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      image: json['image'],
      contentId: json['content_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}

class TestAttemptBean {
  Questions? question;
  int? id;
  Color color;
  int? isVisited = 0;
  String? title;
  bool isBookmark = false;

  TestAttemptBean({
    this.question,
    this.isBookmark = false,
    this.id,
    this.isVisited,
    this.color = ColorConstants.GREY_4,
    this.title,
  });
}

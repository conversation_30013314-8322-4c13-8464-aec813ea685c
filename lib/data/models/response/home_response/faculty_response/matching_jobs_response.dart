// To parse this JSON data, do
//
//     final matchingJobsResponse = matchingJobsResponseFromJson(jsonString);

import 'dart:convert';

import 'package:flutter/cupertino.dart';

MatchingJobsResponse matchingJobsResponseFromJson(String str) => MatchingJobsResponse.fromJson(json.decode(str));

String matchingJobsResponseToJson(MatchingJobsResponse data) => json.encode(data.toJson());

class MatchingJobsResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    MatchingJobsResponse({
        this.status,
        this.data,
        this.error,
    });

    factory MatchingJobsResponse.fromJson(Map<String, dynamic> json) => MatchingJobsResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    List<MatchingJob>? matchingJobs;

    Data({
        this.matchingJobs,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        matchingJobs: json["matching_jobs"] == null ? [] : List<MatchingJob>.from(json["matching_jobs"]!.map((x) => MatchingJob.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "matching_jobs": matchingJobs == null ? [] : List<dynamic>.from(matchingJobs!.map((x) => x.toJson())),
    };
}

class MatchingJob {
    int? id;
    dynamic parentId;
    dynamic categoryId;
    dynamic sessionId;
    String? level;
    String? name;
    String? description;
    String? image;
    DateTime? startDate;
    DateTime? endDate;
    dynamic duration;
    int? createdBy;
    String? status;
    DateTime? createdAt;
    DateTime? updatedAt;
    int? organizationId;
    int? isGlobalProgram;
    int? registrationNeedApproval;
    dynamic assignedRuleId;
    dynamic weightage;
    int? certificateId;
    String? certificateNumberPattern;
    int? certificateLatestNumber;
    dynamic type;
    dynamic shortCode;
    dynamic gScore;
    dynamic subscriptionType;
    dynamic isStructured;
    dynamic isCompetition;
    dynamic terminationDays;
    String? organizedBy;
    dynamic competitionLevel;
    int? isPopular;
    int? isPublished;
    int? isJob;
    dynamic isRecommended;
    int? stepNo;
    dynamic isInternship;
    int? organizedById;
    dynamic sisRefModuleId;
    dynamic languageId;
    dynamic sisModuleId;
    int? contentApproval;
    dynamic departmentId;
    dynamic contentApprovalRule;
    String? jobId;
    dynamic pgScore;
    String? compType;
    String? landingPageUrl;
    int? domainId;
    int? numOfVacancy;
    dynamic location;
    dynamic experience;
    int? minExperience;
    int? maxExperience;
    String? workAddress;
    String? jobStatus;
    int? gulfjobId;
    int? programId;
    int? userId;
    dynamic matchingPer;

    MatchingJob({
        this.id,
        this.parentId,
        this.categoryId,
        this.sessionId,
        this.level,
        this.name,
        this.description,
        this.image,
        this.startDate,
        this.endDate,
        this.duration,
        this.createdBy,
        this.status,
        this.createdAt,
        this.updatedAt,
        this.organizationId,
        this.isGlobalProgram,
        this.registrationNeedApproval,
        this.assignedRuleId,
        this.weightage,
        this.certificateId,
        this.certificateNumberPattern,
        this.certificateLatestNumber,
        this.type,
        this.shortCode,
        this.gScore,
        this.subscriptionType,
        this.isStructured,
        this.isCompetition,
        this.terminationDays,
        this.organizedBy,
        this.competitionLevel,
        this.isPopular,
        this.isPublished,
        this.isJob,
        this.isRecommended,
        this.stepNo,
        this.isInternship,
        this.organizedById,
        this.sisRefModuleId,
        this.languageId,
        this.sisModuleId,
        this.contentApproval,
        this.departmentId,
        this.contentApprovalRule,
        this.jobId,
        this.pgScore,
        this.compType,
        this.landingPageUrl,
        this.domainId,
        this.numOfVacancy,
        this.location,
        this.experience,
        this.minExperience,
        this.maxExperience,
        this.workAddress,
        this.jobStatus,
        this.gulfjobId,
        this.programId,
        this.userId,
        this.matchingPer,
    });

    factory MatchingJob.fromJson(Map<String, dynamic> json) => MatchingJob(
        id: json["id"],
        parentId: json["parent_id"],
        categoryId: json["category_id"],
        sessionId: json["session_id"],
        level: json["level"],
        name: json["name"],
        description: json["description"],
        image: json["image"],
        startDate: json["start_date"] == null ? null : DateTime.parse(json["start_date"]),
        endDate: json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
        duration: json["duration"],
        createdBy: json["created_by"],
        status: json["status"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        organizationId: json["organization_id"],
        isGlobalProgram: json["is_global_program"],
        registrationNeedApproval: json["registration_need_approval"],
        assignedRuleId: json["assigned_rule_id"],
        weightage: json["weightage"],
        certificateId: json["certificate_id"],
        certificateNumberPattern: json["certificate_number_pattern"],
        certificateLatestNumber: json["certificate_latest_number"],
        type: json["type"],
        shortCode: json["short_code"],
        gScore: json["g_score"],
        subscriptionType: json["subscription_type"],
        isStructured: json["is_structured"],
        isCompetition: json["is_competition"],
        terminationDays: json["termination_days"],
        organizedBy: json["organized_by"],
        competitionLevel: json["competition_level"],
        isPopular: json["is_popular"],
        isPublished: json["is_published"],
        isJob: json["is_job"],
        isRecommended: json["is_recommended"],
        stepNo: json["step_no"],
        isInternship: json["is_internship"],
        organizedById: json["organized_by_id"],
        sisRefModuleId: json["sis_ref_module_id"],
        languageId: json["language_id"],
        sisModuleId: json["sis_module_id"],
        contentApproval: json["content_approval"],
        departmentId: json["department_id"],
        contentApprovalRule: json["content_approval_rule"],
        jobId: json["job_id"],
        pgScore: json["pg_score"],
        compType: json["comp_type"],
        landingPageUrl: json["landing_page_url"],
        domainId: json["domain_id"],
        numOfVacancy: json["num_of_vacancy"],
        location: json["location"],
        experience: json["experience"],
        minExperience: json["min_experience"],
        maxExperience: json["max_experience"],
        workAddress: json["work_address"],
        jobStatus: json["job_status"],
        gulfjobId: json["gulfjob_id"],
        programId: json["program_id"],
        userId: json["user_id"],
        matchingPer: json["matching_per"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "category_id": categoryId,
        "session_id": sessionId,
        "level": level,
        "name": name,
        "description": description,
        "image": image,
        "start_date": startDate?.toIso8601String(),
        "end_date": endDate?.toIso8601String(),
        "duration": duration,
        "created_by": createdBy,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "organization_id": organizationId,
        "is_global_program": isGlobalProgram,
        "registration_need_approval": registrationNeedApproval,
        "assigned_rule_id": assignedRuleId,
        "weightage": weightage,
        "certificate_id": certificateId,
        "certificate_number_pattern": certificateNumberPattern,
        "certificate_latest_number": certificateLatestNumber,
        "type": type,
        "short_code": shortCode,
        "g_score": gScore,
        "subscription_type": subscriptionType,
        "is_structured": isStructured,
        "is_competition": isCompetition,
        "termination_days": terminationDays,
        "organized_by": organizedBy,
        "competition_level": competitionLevel,
        "is_popular": isPopular,
        "is_published": isPublished,
        "is_job": isJob,
        "is_recommended": isRecommended,
        "step_no": stepNo,
        "is_internship": isInternship,
        "organized_by_id": organizedById,
        "sis_ref_module_id": sisRefModuleId,
        "language_id": languageId,
        "sis_module_id": sisModuleId,
        "content_approval": contentApproval,
        "department_id": departmentId,
        "content_approval_rule": contentApprovalRule,
        "job_id": jobId,
        "pg_score": pgScore,
        "comp_type": compType,
        "landing_page_url": landingPageUrl,
        "domain_id": domainId,
        "num_of_vacancy": numOfVacancy,
        "location": location,
        "experience": experience,
        "min_experience": minExperience,
        "max_experience": maxExperience,
        "work_address": workAddress,
        "job_status": jobStatus,
        "gulfjob_id": gulfjobId,
        "program_id": programId,
        "user_id": userId,
        "matching_per": matchingPer,
    };
}


class MatchingJobsProvider extends ChangeNotifier {
  List<MatchingJob?> list = [];
  

  MatchingJobsProvider(List<MatchingJob?>? list) {
    if (list != null) this.list = list;
    notifyListeners();
  }

  void updateAppliedStatus(int index) {
    this.list[index]?.jobStatus = 'Applied';
    notifyListeners();
  }

  void resetList(List<MatchingJob?> newData) {
    this.list = newData;
    notifyListeners();
  }

  void addItemList(List<MatchingJob?> newData) {
    this.list.addAll(newData);
    notifyListeners();
  }

  void resetValue() {
    this.list = [];
    notifyListeners();
  }
}



import 'package:flutter/material.dart';

class TestReviewResponse {
  int? status;
  Data? data;
  List<String?>? error;

  TestReviewResponse({this.status, this.data, this.error});

  TestReviewResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = this.error;
    return data;
  }
}

class Data {
  AssessmentReview? assessmentReview;

  Data({this.assessmentReview});

  Data.fromJson(Map<String, dynamic> json) {
    assessmentReview = json['assessment_review'] != null
        ? new AssessmentReview.fromJson(json['assessment_review'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.assessmentReview != null) {
      data['assessment_review'] = this.assessmentReview!.toJson();
    }
    return data;
  }
}

class AssessmentReview {
  String? contentId;
  String? certificateUrl;
  int? startDate;
  int? endDate;
  int? questionCount;
  int? negativeMarking;
  int? negativeMarks;
  int? durationInMinutes;
  int? totalAttempts;
  int? attemptCount;
  dynamic score;
  int? rank;
  int? program;
  List<Questions>? questions;
  LeaderBoard? leaderboard;

  AssessmentReview(
      {this.contentId,
      this.certificateUrl,
      this.startDate,
      this.endDate,
      this.questionCount,
      this.negativeMarking,
      this.negativeMarks,
      this.durationInMinutes,
      this.totalAttempts,
      this.attemptCount,
      this.questions,
      this.leaderboard});

  AssessmentReview.fromJson(Map<String, dynamic> json) {
    contentId = json['content_id'];
    certificateUrl = json['certificate_url'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    questionCount = json['question_count'];
    negativeMarking = json['negative_marking'];
    negativeMarks = json['negative_marks'];
    durationInMinutes = json['duration_in_minutes'];
    totalAttempts = json['total_attempts'];
    attemptCount = json['attempt_count'];
    leaderboard = LeaderBoard.fromJson(json['leaderboard']);
    score = json['score'];
    rank = json['rank'];
    program = json['program'];
    if (json['questions'] != null) {
      questions = <Questions>[];
      json['questions'].forEach((v) {
        questions!.add(new Questions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['content_id'] = this.contentId;
    data['certificate_url'] = this.certificateUrl;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['question_count'] = this.questionCount;
    data['negative_marking'] = this.negativeMarking;
    data['negative_marks'] = this.negativeMarks;
    data['duration_in_minutes'] = this.durationInMinutes;
    data['total_attempts'] = this.totalAttempts;
    data['attempt_count'] = this.attemptCount;
    data['score'] = this.score;
    data['rank'] = this.rank;
    data['program'] = this.program;
    data['leaderboard'] = this.leaderboard;
    if (this.questions != null) {
      data['questions'] = this.questions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Questions {
  int? questionId;
  String? question;
  int? questionTypeId;
  dynamic marks;
  List<String>? correctOptions;
  List<String>? optionSelected;
  List<QuestionOptions>? questionOptions;
  List<String>? fillStringValue = [];
  List<dynamic>? selectedMatchingOption = [];
  List<dynamic>? correctAnswerStatement = [];
  Map<int, List<String>> selectedMatchingOptionForIndex = {};
  String? questionType;
  int? isCorrect;
  //int? marksObtained;
  dynamic marksObtained;
  int? attemptState;
  List<dynamic>? questionImage = [];
  String? userFile;
  String? answerStatement;
  QuestionLabel? questionLabel;
  String? teacherFeedback;
  String? blankHtml;

  Questions(
      {this.questionId,
      this.question,
      this.questionTypeId,
      this.marks,
      this.correctOptions,
      this.questionOptions,
      this.questionType,
      this.isCorrect,
      this.marksObtained,
      this.attemptState,
      this.questionImage,
      this.userFile,
      this.answerStatement,
      this.optionSelected,
      this.questionLabel,
      this.teacherFeedback,
      this.blankHtml});

  Questions.fromJson(Map<String, dynamic> json) {
    questionId = json['question_id'];
    question = json['question'];
    questionTypeId = json['question_type_id'];
    marks = json['marks'];
    correctOptions = json['correct_options'].cast<String>();
    optionSelected = json['option_selected'].cast<String>();
    questionImage = json['question_image'];
    userFile = json.containsKey('user_file') ? json['user_file'] : '';
    answerStatement =
        json.containsKey('answer_statement') ? json['answer_statement'] : '';
    if (json['question_options'] != null) {
      questionOptions = <QuestionOptions>[];
      json['question_options'].forEach((v) {
        questionOptions!.add(new QuestionOptions.fromJson(v));
      });
    }
    if (json['question_image'] != null) {
      questionImage = <String>[];
      json['question_image'].forEach((v) {
        questionImage!.add(v);
      });
    }
    questionType = json['question_type'];
    isCorrect = json['is_correct'];
    marksObtained = json['marks_obtained'];
    attemptState = json['attempt_state'];
    if(json['question_label'] != null){
      questionLabel= QuestionLabel.fromJson(json['question_label']);
    }
    teacherFeedback = json['teacher_feedback'];
    blankHtml = json.containsKey('blank_html') ? json['blank_html'] : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['question_id'] = this.questionId;
    data['question'] = this.question;
    data['question_type_id'] = this.questionTypeId;
    data['marks'] = this.marks;
    data['correct_options'] = this.correctOptions;
    data['option_selected'] = this.optionSelected;
    data['user_file'] = this.userFile;
    data['answer_statement'] = this.answerStatement;

    if (this.questionImage != null) {
      data['question_image'] = this.questionImage!.map((v) => v).toList();
    }
    if (this.questionOptions != null) {
      data['question_options'] =
          this.questionOptions!.map((v) => v.toJson()).toList();
    }
    data['question_type'] = this.questionType;
    data['is_correct'] = this.isCorrect;
    data['marks_obtained'] = this.marksObtained;
    data['attempt_state'] = this.attemptState;
    data['question_label'] = this.questionLabel;
    data['teacher_feedback'] = teacherFeedback;
    data['blank_html'] = this.blankHtml;
    return data;
  }
}

class QuestionOptions {
  int? optionId;
  String? optionStatement;
  String? optionImage;
  int? userAnswer;

  QuestionOptions({this.optionId, this.optionStatement, this.optionImage, this.userAnswer});

  QuestionOptions.fromJson(Map<String, dynamic> json) {
    optionId = json['option_id'];
    optionStatement = json['option_statement'];
    optionImage = json['option_image'];
    userAnswer = json['user_answer'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['option_id'] = this.optionId;
    data['option_statement'] = this.optionStatement;
    data['option_image'] = this.optionImage;
    data['user_answer'] = this.userAnswer;
    return data;
  }
}

class TestReviewBean {
  Questions? question;
  int? id;
  String? title;
  Color color = Colors.white;

  TestReviewBean({
    this.color = Colors.white,
    this.question,
    this.id,
    this.title,
  });
}

class LeaderBoard {
  LeaderBoard({
    this.topStudentsList,
    this.studentRank,
    this.studentLastViewed,
  });

  List<TopStudentsList>? topStudentsList;
  int? studentRank;
  DateTime? studentLastViewed;

  factory LeaderBoard.fromJson(Map<String, dynamic> json) => LeaderBoard(
        topStudentsList: json["topStudentsList"] == null
            ? []
            : List<TopStudentsList>.from(json["topStudentsList"]!
                .map((x) => TopStudentsList.fromJson(x))),
        studentRank: json["studentRank"],
        studentLastViewed:
            json["studentLastViewed"] == null || json["studentLastViewed"] == ""
                ? null
                : DateTime.parse(json["studentLastViewed"]),
      );

  Map<String, dynamic> toJson() => {
        "topStudentsList": topStudentsList == null
            ? []
            : List<dynamic>.from(topStudentsList!.map((x) => x.toJson())),
        "studentRank": studentRank,
        "studentLastViewed": studentLastViewed,
      };
}

class TopStudentsList {
  TopStudentsList({
    this.userId,
    this.completionPercentage,
    this.completionTime,
    this.updatedAt,
    this.name,
    this.email,
    this.profileImage,
  });

  int? userId;
  int? completionPercentage;
  String? completionTime;
  String? updatedAt;
  String? name;
  String? email;
  String? profileImage;

  factory TopStudentsList.fromJson(Map<String, dynamic> json) =>
      TopStudentsList(
        userId: json["user_id"],
        completionPercentage: json["completion_percentage"],
        completionTime: json["completion_time"],
        updatedAt: json["updated_at"],
        name: json["name"],
        email: json["email"],
        profileImage: json["profile_image"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "completion_percentage": completionPercentage,
        "completion_time": completionTime,
        "updated_at": updatedAt,
        "name": name,
        "email": email,
        "profile_image": profileImage,
      };
}

class QuestionLabel {
  final int? id;
  final String? name;
  final String? description;
  final String? image;
  final int? contentId;
  final String? createdAt;
  final String? updatedAt;

  QuestionLabel({
    this.id,
    this.name,
    this.description,
    this.image,
    this.contentId,
    this.createdAt,
    this.updatedAt,
  });

  factory QuestionLabel.fromJson(Map<String, dynamic> json) {
    return QuestionLabel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      image: json['image'],
      contentId: json['content_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}

import 'package:flutter/material.dart';

enum ReelUploadingState{
  initialed, started, uploading, uploaded, error
}

class ReelUploadProvider extends ChangeNotifier {
  ReelUploadingState? reelState;
  ReelUploadProvider(){
    if(this.reelState != null) return;
    this.reelState = ReelUploadingState.initialed;
  }

  void changeState(ReelUploadingState state){
    this.reelState = state;
    notifyListeners();
  }
}
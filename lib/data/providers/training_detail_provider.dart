import 'package:masterg/data/api/api_response.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/training_detail_response.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/providers/base_state.dart';
import 'package:masterg/pages/training_pages/training_service.dart';

class TrainingDetailProvider extends BaseState {
  TrainingService trainingService;
  MProgram? program;
  //late Box box;
  String? description = '';
  List<Modules>? modules = [];
  ApiStatus apiStatus = ApiStatus.LOADING;
  bool certificateAvailable = false;
  dynamic skills;

  TrainingDetailProvider(this.trainingService, this.program) {
    getTraningDetail();
  }

  void getTraningDetail() {
   /* box = Hive.box(DB.TRAININGS);
    if (box.get(program!.id.toString() + "MOD") != null) {
      modules = box
          .get("${program!.id}MOD")
          .map((e) => Modules.fromJson(Map<String, dynamic>.from(e)))
          .cast<Modules>()
          .toList();
    }

    if (box.get("${program!.id}SKILLS") != null) {
      skills = box.get("${program!.id}SKILLS");
    }
    if (box.get("${program!.id}DESC") != null) {
      description = box.get("${program!.id}DESC");
    }*/
    trainingService.getTrainingDetail(program!.id).then((response) {
      ApiResponse apiResponse = response;
      if (apiResponse.success) {
        TrainingDetailResponse trainingDetailResponse =
            TrainingDetailResponse.fromJson(response.body);

        skills = trainingDetailResponse.data?.skills;

        /*box.put(
            program!.id.toString() + "MOD",
            trainingDetailResponse.data!.list!.first.modules!
                .map((e) => e.toJson())
                .toList());*/

        /*box.put(program!.id.toString() + "SKILLS",
            trainingDetailResponse.data?.skills);
        box.put("${program!.id}DESC",
            trainingDetailResponse.data!.list!.first.description);*/

        description = trainingDetailResponse.data!.list!.first.description;
        modules = trainingDetailResponse.data!.list!.first.modules;

        if (skills != null) {
          modules?.sort(((a, b) => a.skillId!.compareTo(b.skillId!)));
        }

        // certificateAvailable =
        // Log.v("check vallue ${trainingDetailResponse.data!.list!.first.toJson()}");
        apiStatus = ApiStatus.SUCCESS;
      } else {
        apiStatus = ApiStatus.ERROR;
        updateErrorWidget(apiResponse.body
            .toString()
            .replaceAll('[', '')
            .replaceAll(']', ''));
      }
      notifyListeners();
    });
  }
}

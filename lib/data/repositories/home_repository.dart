import 'dart:convert';
import 'dart:developer';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/models/request/home_request/poll_submit_req.dart';
import 'package:masterg/data/models/request/home_request/submit_feedback_req.dart';
import 'package:masterg/data/models/request/home_request/submit_survey_req.dart';
import 'package:masterg/data/models/request/home_request/track_announcement_request.dart';
import 'package:masterg/data/models/request/home_request/user_program_subscribe.dart';
import 'package:masterg/data/models/request/save_answer_request.dart';
import 'package:masterg/data/models/response/accept_fee_agreement_response.dart';
import 'package:masterg/data/models/response/auth_response/assessmentReportResp.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/competition_my_activity.dart';
import 'package:masterg/data/models/response/auth_response/event_participate_response.dart';
import 'package:masterg/data/models/response/auth_response/oraganization_program_resp.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/models/response/general_resp.dart';
import 'package:masterg/data/models/response/home_response/add_open_work_response.dart';
import 'package:masterg/data/models/response/home_response/add_portfolio_resp.dart';
import 'package:masterg/data/models/response/home_response/add_skill_response.dart';
import 'package:masterg/data/models/response/home_response/assessment_certificate_response.dart';
import 'package:masterg/data/models/response/home_response/assessment_details_response.dart';
import 'package:masterg/data/models/response/home_response/assignment_submissions_response.dart';
import 'package:masterg/data/models/response/home_response/category_response.dart';
import 'package:masterg/data/models/response/home_response/company_job_list_response.dart';
import 'package:masterg/data/models/response/home_response/company_list_response.dart';
import 'package:masterg/data/models/response/home_response/competition_content_list_resp.dart';
import 'package:masterg/data/models/response/home_response/competition_response.dart';
import 'package:masterg/data/models/response/home_response/content_tags_resp.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/models/response/home_response/create_post_response.dart';
import 'package:masterg/data/models/response/home_response/delete_post_response.dart';
import 'package:masterg/data/models/response/home_response/delete_skill_response.dart';
import 'package:masterg/data/models/response/home_response/domain_filter_list.dart';
import 'package:masterg/data/models/response/home_response/domain_list_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/Attendance_percentage_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/assign_learner_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_assessment_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_assignment_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_class_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_details_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_module_list_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/hod_program_list.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/mark_attendance_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/matching_jobs_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/module_leader_program_list_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/program_completion_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/update_attendance_resp.dart';
import 'package:masterg/data/models/response/home_response/featured_video_response.dart';
import 'package:masterg/data/models/response/home_response/feedback_response.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/data/models/response/home_response/generate_certificate_resp.dart';
import 'package:masterg/data/models/response/home_response/get_certificates_resp.dart';
import 'package:masterg/data/models/response/home_response/get_comment_response.dart';
import 'package:masterg/data/models/response/home_response/get_content_resp.dart';
import 'package:masterg/data/models/response/home_response/get_course_leaderboard_resp.dart';
import 'package:masterg/data/models/response/home_response/get_course_modules_resp.dart';
import 'package:masterg/data/models/response/home_response/get_courses_resp.dart';
import 'package:masterg/data/models/response/home_response/get_kpi_analysis_resp.dart';
import 'package:masterg/data/models/response/home_response/get_module_leaderboard_resp.dart';
import 'package:masterg/data/models/response/home_response/greels_response.dart';
import 'package:masterg/data/models/response/home_response/job_domain_detail_resp.dart';
import 'package:masterg/data/models/response/home_response/joy_category_response.dart';
import 'package:masterg/data/models/response/home_response/joy_contentList_response.dart';
import 'package:masterg/data/models/response/home_response/language_response.dart';
import 'package:masterg/data/models/response/home_response/learning_space_response.dart';
import 'package:masterg/data/models/response/home_response/list_resume_response.dart';
import 'package:masterg/data/models/response/home_response/map_interest_response.dart';
import 'package:masterg/data/models/response/home_response/master_language_response.dart';
import 'package:masterg/data/models/response/home_response/my_assessment_response.dart';
import 'package:masterg/data/models/response/home_response/my_assignment_response.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/data/models/response/home_response/notification_list_resp.dart';
import 'package:masterg/data/models/response/home_response/notification_read_resp.dart';
import 'package:masterg/data/models/response/home_response/notification_resp.dart';
import 'package:masterg/data/models/response/home_response/onboard_sessions.dart';
import 'package:masterg/data/models/response/home_response/popular_courses_response.dart';
import 'package:masterg/data/models/response/home_response/portfolio_competition_response.dart';
import 'package:masterg/data/models/response/home_response/post_comment_response.dart';
import 'package:masterg/data/models/response/home_response/program_list_reponse.dart';
import 'package:masterg/data/models/response/home_response/report_content_response.dart';
import 'package:masterg/data/models/response/home_response/save_answer_response.dart';
import 'package:masterg/data/models/response/home_response/singularis_portfolio_deleteResp.dart';
import 'package:masterg/data/models/response/home_response/skill_rating_resp.dart';
import 'package:masterg/data/models/response/home_response/skill_suggestion_response.dart';
import 'package:masterg/data/models/response/home_response/submit_answer_response.dart';
import 'package:masterg/data/models/response/home_response/submit_feedback_resp.dart';
import 'package:masterg/data/models/response/home_response/survey_data_resp.dart';
import 'package:masterg/data/models/response/home_response/test_attempt_response.dart';
import 'package:masterg/data/models/response/home_response/test_review_response.dart';
import 'package:masterg/data/models/response/home_response/top_score.dart';
import 'package:masterg/data/models/response/home_response/topics_resp.dart';
import 'package:masterg/data/models/response/home_response/training_detail_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/models/response/home_response/update_user_profile_response.dart';
import 'package:masterg/data/models/response/home_response/user_analytics_response.dart';
import 'package:masterg/data/models/response/home_response/user_info_response.dart';
import 'package:masterg/data/models/response/home_response/user_jobs_list_response.dart';
import 'package:masterg/data/models/response/home_response/user_profile_response.dart';
import 'package:masterg/data/models/response/home_response/user_program_subscribe_reponse.dart';
import 'package:masterg/data/models/response/home_response/wow_dashboard_response.dart';
import 'package:masterg/data/models/response/home_response/zoom_open_url_response.dart';
import 'package:masterg/data/providers/home_provider.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/user_profile_page/model/MasterBrand.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import '../../utils/resource/colors.dart';
import '../models/response/auth_response/dashboard_content_resp.dart';
import '../models/response/auth_response/dashboard_view_resp.dart';
import '../models/response/home_response/ExploreJobListResponse.dart';
import '../models/response/home_response/analyse_video_resume_resp.dart';
import '../models/response/home_response/create_portfolio_response.dart';
import '../models/response/home_response/delete_portfolio_response.dart';
import '../models/response/home_response/download_assessment_report_response.dart';
import '../models/response/home_response/explore_job_details_response.dart';
import '../models/response/home_response/fee_agreement_response.dart';
import '../models/response/home_response/goal_intrest_area_responce.dart';
import '../models/response/home_response/leaderboard_resp.dart';
import '../models/response/home_response/list_portfolio_responsed.dart';
import '../models/response/home_response/pi_detail_resp.dart';
import '../models/response/home_response/remove_account_resp.dart';
import '../models/response/home_response/video_resume_delete_response.dart';
import '../models/response/semester_list_response.dart';

class HomeRepository {
  HomeRepository({required this.homeProvider});

  final HomeProvider homeProvider;

  void saveUserInfo(UserInfoResponse user) {
    UserData? userData = user.data?.userData;
    Preference.setInt(Preference.USER_ID, userData!.id!);
    UserSession.userId = userData.id;

    Preference.setInt(Preference.USER_COINS, userData.totalCoins!);
    UserSession.userCoins = userData.totalCoins;

    Preference.setString(Preference.USER_EMAIL, userData.email!);
    UserSession.email = userData.email;

    Preference.setString(Preference.DESIGNATION, userData.designation!);
    UserSession.designation = userData.designation;

    Preference.setString(Preference.FIRST_NAME, userData.name!);
    UserSession.userName = userData.name;

    Preference.setString(Preference.PHONE, userData.mobileNo!);
    UserSession.phone = userData.mobileNo;

    Preference.setString(Preference.PROFILE_IMAGE, userData.profileImage!);
    UserSession.userImageUrl = userData.profileImage;

    Preference.setString(Preference.GENDER, userData.gender!);
    UserSession.gender = userData.gender;

    UserSession.userData = json.encode(userData.toJson()).toString();
    Preference.setString(Preference.USER_DATA, UserSession.userData!);
  }

  Future<MyAssessmentResponse> getMyAssessmentList(
      {String? interestID, int? jobRoleID, int? skillID}) async {
    final response = await homeProvider.getMyAssessmentList(
        interestID: interestID, jobRoleID: jobRoleID, skillID: skillID);
    if (response!.success) {
      Log.v("SUCCESS DATA : ${json.encode(response.body)}");
      Log.v("MyAssingment123");
      MyAssessmentResponse resp = MyAssessmentResponse.fromJson(response.body);
      Log.v("MyAssingment");

      var box = Hive.box("content");
      box.put("myassessment",
          resp.data!.assessmentList!.map((e) => e.toJson()).toList());

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return MyAssessmentResponse(
          error: response.body == null
              ? "Something went wrong:" as List<String>?
              : response.body);
    }
  }

  Future<MyAssignmentResponse> getMyAssignmentList() async {
    final response = await homeProvider.getMyAssignmentList();
    if (response!.success) {
      Log.v("SUCCESS DATA : ${json.encode(response.body)}");
      Log.v("MyAssingment123");
      MyAssignmentResponse resp = MyAssignmentResponse.fromJson(response.body);
      Log.v("MyAssingment");

      var box = Hive.box("content");
      box.put("myassignment", resp.data!.list!.map((e) => e.toJson()).toList());
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return MyAssignmentResponse(
          error: response.body == null
              ? "Something went wrong:" as List<String>?
              : response.body);
    }
  }

  Future<UserProgramSubscribeRes> subscribeProgram(
      UserProgramSubscribeReq req) async {
    final response = await homeProvider.UserSubscribe(subrReq: req);
    if (response!.success) {
      Log.v("User Program Subscribe DATA : ${response.body}");
      UserProgramSubscribeRes resp =
          UserProgramSubscribeRes.fromJson(response.body);

      return resp;
    } else {
      return UserProgramSubscribeRes();
    }
  }

  Future<UserInfoResponse> getSwayamUserProfile() async {
    final response = await homeProvider.getSwayamUserProfile();
    if (response!.success) {
      Log.v("Profile DATA : ${response.body}");
      UserInfoResponse resp = UserInfoResponse.fromJson(response.body);
      Log.v("Sucess DATA : ${resp.toJson()}");
      saveUserInfo(resp);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return UserInfoResponse();
    }
  }

  Future<UserProfileResp> getUserProfile() async {
    final response = await homeProvider.getUserProfile();
    if (response!.success) {
      Log.v("Profile DATA : ${response.body}");
      UserProfileResp resp = UserProfileResp.fromJson(response.body);
      Log.v("ERROR DATA : ${resp.toJson()}");
      var box = Hive.box("content");
      box.put("user_profile_data", resp);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return UserProfileResp();
    }
  }

  Future<UpdateProfileImageResponse> updateUserProfileImage(
      String? filePath, String? name, String? email) async {
    final response =
        await homeProvider.updateUserProfileImage(filePath, name, email);
    if (response!.success) {
      Log.v("Profile DATA : ${response.body}");
      UpdateProfileImageResponse resp =
          UpdateProfileImageResponse.fromJson(response.body);

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return UpdateProfileImageResponse();
    }
  }

  Future<AssessmentReportResp?> getAssessmentReports(
      {int? assessmentId}) async {
    try {
      final response =
          await homeProvider.getAssessmentReports(assessmentId: assessmentId);
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        AssessmentReportResp assessmentReportResp =
            AssessmentReportResp.fromJson(response.body);
        return assessmentReportResp;
      } else {
        Log.v("====> ${response.body}");
        return AssessmentReportResp();
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<AssignLearnerResponse?> getAssignLearner(
      {int? isFaculty, int? programId}) async {
    try {
      final response = await homeProvider.getAssignLearner(
          isFaculty: isFaculty, programId: programId);
      if (response!.success) {
        Log.v("SUCCESS DATA SINGH1:: ${response.body}");
        AssignLearnerResponse assignLearnerResponse =
            AssignLearnerResponse.fromJson(response.body);
        return assignLearnerResponse;
      } else {
        Log.v("====> ${response.body}");
        return AssignLearnerResponse();
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future likeContent(int? contentId, String? type, int? like) async {
    final response = await homeProvider.likeContent(contentId, type, like);
    if (response!.success) {
      UserProfileResp resp = UserProfileResp.fromJson(response.body);

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return;
    }
  }

  Future<ReportContentResp> reportContent(
      String? status, int? contentId, String? category, String? comment) async {
    final response =
        await homeProvider.reportContent(status, contentId, category, comment);
    if (response!.success) {
      Log.v("Report content DATA : ${response.body}");
      ReportContentResp resp = ReportContentResp.fromJson(response.body);

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return ReportContentResp(status: 0, message: 'error');
    }
  }

  Future<DeletePostResponse?> deletePost(int? skillId) async {
    final response = await homeProvider.deletePost(skillId);
    if (response!.success) {
      Log.v("Delete Post DATA : ${response.body}");
      DeletePostResponse resp = DeletePostResponse.fromJson(response.body);

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return DeletePostResponse(status: 0, message: 'error');
    }
  }

  Future<DeleteSkillResponse?> getDeleteSkill(int? skillId) async {
    final response = await homeProvider.getDeleteSkill(skillId);
    if (response!.success) {
      Log.v("Delete Skill DATA : ${response.body}");
      DeleteSkillResponse resp = DeleteSkillResponse.fromJson(response.body);

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return DeleteSkillResponse(
        status: 0,
      );
    }
  }

  Future<AddOpenWorkResponse?> openToWork(int? openToWork) async {
    final response = await homeProvider.openToWork(openToWork);
    if (response!.success) {
      Log.v("open to work  DATA : ${response.body}");
      AddOpenWorkResponse resp = AddOpenWorkResponse.fromJson(response.body);

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return AddOpenWorkResponse(
        status: 0,
      );
    }
  }

  Future<GetCoursesResp?> getCoursesList({int? type}) async {
    final response = await homeProvider.getCoursesList(type: type!);
    var box = Hive.box("analytics");
    if (response!.success) {
      Log.v("SUCCESS DATA : ${response.body}");
      GetCoursesResp gameResponse = GetCoursesResp.fromJson(response.body);
      if (type == 0) {
        box.put("myAnalytics",
            gameResponse.data?.list?.map((e) => e.toJson()).toList());
      }
      if (type == 1) {
        box.put("teamAnalytics",
            gameResponse.data?.list?.map((e) => e.toJson()).toList());
      }
      return gameResponse;
    } else {
      if (type == 0) {
        box.put("myAnalytics", []);
      }
      if (type == 1) {
        box.put("teamAnalytics", []);
      }
      Log.v("====> ${response.body}");
      return GetCoursesResp();
    }
  }

  Future<SubmitFeedbackResp> submitFeedback({FeedbackReq? feedbackReq}) async {
    final response = await homeProvider.submitFeedback(req: feedbackReq);
    if (response!.success) {
      Log.v("ERROR DATA1 : ${json.encode(response.body)}");
      SubmitFeedbackResp resp = SubmitFeedbackResp.fromJson(response.body);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return SubmitFeedbackResp(
          message:
              response.body == null ? "Something went wrong:" : response.body);
    }
  }

  Future<TopicsResp> getTopicsList() async {
    final response = await homeProvider.getTopicsList();
    if (response!.success) {
      Log.v("ERROR DATA : ${json.encode(response.body)}");
      TopicsResp resp = TopicsResp.fromJson(response.body);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return TopicsResp(
          error:
              response.body == null ? "Something went wrong:" : response.body);
    }
  }

  Future<FeedbackResp?> getFeedbackList({int? categoryType}) async {
    final response = await homeProvider.getFeedbackList();
    if (response!.success) {
      Log.v("ERROR DATA : ${json.encode(response.body)}");
      FeedbackResp resp = FeedbackResp.fromJson(response.body);
      var box = Hive.box(DB.CONTENT);
      box.put("ideas", resp.data?.list?.map((e) => e.toJson()).toList());

      return resp;
    } else {
      Log.v("====> ${response.body}");
      return FeedbackResp(
          error:
              response.body == null ? "Something went wrong:" : response.body);
    }
  }

  Future<GetKpiAnalysisResp?> getKPIAnalysisList() async {
    final response = await homeProvider.getKPIAnalysisList();
    var box = Hive.box("analytics");
    if (response!.success) {
      Log.v("SUCCESS DATA : ${response.body}");
      GetKpiAnalysisResp gameResponse =
          GetKpiAnalysisResp.fromJson(response.body);

      box.put("kpiData",
          gameResponse.data?.kpiData?.map((e) => e.toJson()).toList());
      return gameResponse;
    } else {
      box.put("kpiData", []);
      Log.v("====> ${response.body}");
      return GetKpiAnalysisResp();
    }
  }

  Future<GetCertificatesResp?> getCertificatesList() async {
    final response = await homeProvider.getCertificatesList();
    if (response!.success) {
      Log.v("SUCCESS DATA : ${response.body}");
      GetCertificatesResp gameResponse =
          GetCertificatesResp.fromJson(response.body);
      Box box = Hive.box(DB.ANALYTICS);
      box.put(
          "certificates",
          gameResponse.data?.kpiCertificatesData
              ?.map((e) => e.toJson())
              .toList());
      return gameResponse;
    } else {
      Log.v("====> ${response.body}");
      return GetCertificatesResp();
    }
  }

  Future<ContentTagsResp?> getContentTagsList({int? categoryType}) async {
    final response =
        await homeProvider.getContentTagsList(categoryType: categoryType);
    if (response!.success) {
      Log.v("ERROR DATA : ${json.encode(response.body)}");
      ContentTagsResp resp = ContentTagsResp.fromJson(response.body);
      var box = Hive.box("content");
      if (categoryType == 8) {
        box.put("announcementFilters",
            resp.data?.listTags?.map((e) => e.toJson()).toList());
      }
      if (categoryType == 9) {
        box.put("libraryFilters",
            resp.data?.listTags?.map((e) => e.toJson()).toList());
      }
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return ContentTagsResp(
          error:
              response.body == null ? "Something went wrong:" : response.body);
    }
  }

  Future<GetModuleLeaderboardResp?> getModuleLeaderboardList(String moduleId,
      {int type = 0}) async {
    final response =
        await homeProvider.getModuleLeaderboardList(moduleId, type: type);
    if (response!.success) {
      Log.v("SUCCESS DATA 2: ${response.body}");
      GetModuleLeaderboardResp gameResponse =
          GetModuleLeaderboardResp.fromJson(response.body);
      var box = Hive.box("analytics");
      if (type == 0) {
        box.put(moduleId + AnalyticsType.MODULE_LEADERBOARD_TYPE_1,
            gameResponse.data?.list?.map((e) => e.toJson()).toList());
      }
      if (type == 1) {
        box.put(moduleId + AnalyticsType.MODULE_LEADERBOARD_TYPE_2,
            gameResponse.data?.list?.map((e) => e.toJson()).toList());
      }
      return gameResponse;
    } else {
      Log.v("====> ${response.body}");
      return GetModuleLeaderboardResp();
    }
  }

  Future<GetCourseLeaderboardResp?> getCourseLeaderboardList(String courseId,
      {int type = 0}) async {
    final response =
        await homeProvider.getCourseLeaderboardList(courseId, type: type);
    if (response!.success) {
      Log.v("SUCCESS DATA LEADER: ${response.body}");
      GetCourseLeaderboardResp gameResponse =
          GetCourseLeaderboardResp.fromJson(response.body);
      var box = Hive.box("analytics");
      if (type == 0) {
        box.put(courseId + AnalyticsType.COURSE_LEADERBOARD_TYPE_1,
            gameResponse.data?.list?.map((e) => e.toJson()).toList());
      }
      if (type == 1) {
        box.put(courseId + AnalyticsType.COURSE_LEADERBOARD_TYPE_2,
            gameResponse.data?.list?.map((e) => e.toJson()).toList());
      }
      return gameResponse;
    } else {
      Log.v("====> ${response.body}");
      return GetCourseLeaderboardResp();
    }
  }

  Future<GetCourseModulesResp?> getCourseModulesList(String courseId,
      {int type = 0}) async {
    try {
      final response =
          await homeProvider.getCourseModulesList(courseId, type: 0);
      if (response!.success) {
        Log.v("SUCCESS DATA MOD : ${response.body}");
        GetCourseModulesResp gameResponse =
            GetCourseModulesResp.fromJson(response.body);
        var box = Hive.box("analytics");
        if (type == 0) {
          box.put(
              courseId + AnalyticsType.MODULE_TYPE_1,
              gameResponse.data?.list?.first.modules
                  ?.map((e) => e.toJson())
                  .toList());
        }
        if (type == 1) {
          box.put(
              courseId + AnalyticsType.MODULE_TYPE_2,
              gameResponse.data?.list?.first.modules
                  ?.map((e) => e.toJson())
                  .toList());
        }
        return gameResponse;
      } else {
        Log.v("====> ${response.body}");
        return GetCourseModulesResp();
      }
    } catch (e, stacktrace) {
      debugPrint('exception is $e and stacktrace is $stacktrace');
    }
    return null;
  }

  Future<CategoryResp> getCategory() async {
    final response = await homeProvider.getCategorys();
    if (response!.success) {
      Log.v("response!.success : ${response.body}");
      CategoryResp categoryResp = CategoryResp.fromJson(response.body);
      return categoryResp;
    } else {
      Log.v("====> ${response.body}");
      return CategoryResp();
    }
  }

  Future<JoyCategoryResponse> getjoyCategory() async {
    final response = await homeProvider.getjoyCategory();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      JoyCategoryResponse joyCategoryResponse =
          JoyCategoryResponse.fromJson(response.body);
      Log.v('=======at category call ');
      var box = Hive.box("content");
      box.put("joy_category",
          joyCategoryResponse.data!.list!.map((e) => e.toJson()).toList());
      return joyCategoryResponse;
    } else {
      Log.v("====> ${response.body}");
      return JoyCategoryResponse();
    }
  }

  //job
  Future<UserJobsListResponse> getUserJobList() async {
    final response = await homeProvider.getJobList();
    if (response!.success) {
      Log.v("Job List DATA : ${response.body}");
      UserJobsListResponse userJobsListResponse =
          UserJobsListResponse.fromJson(response.body);
      var box = Hive.box("content");
      box.put("userJobList",
          userJobsListResponse.list!.map((e) => e.toJson()).toList());
      return userJobsListResponse;
    } else {
      Log.v("====> ${response.body}");
      return UserJobsListResponse();
    }
  }

  Future<TrainingModuleResponse> getCompetitionDetail(int? moduleId) async {
    try {
      final response =
          await homeProvider.getCompetitionDetail(moduleId: moduleId);

      if (response!.success) {
        Log.v("Competition DATA : ${response.body}");
        TrainingModuleResponse competitionData =
            TrainingModuleResponse.fromJson(response.body);

        var box = Hive.box("content");
        box.put("competitionDetail",
            competitionData.data?.module?.map((e) => e.toJson()).toList());
        return competitionData;
      } else {
        Log.v("====> ${response.body}");
        return TrainingModuleResponse();
      }
    } catch (e, stackTrace) {
      // Handle the exception here
      debugPrint("Error occurred: $e $stackTrace");
      return TrainingModuleResponse(); // or you can throw the exception further
    }
  }

  Future<CompetitionResponse> getCompetitionList(
      bool? isPopular, bool isFilter, String? ids, String? domainId) async {
    final response = await homeProvider.getCompetitionList(
        isPopular: isPopular,
        isFiltter: isFilter,
        jobIds: ids,
        domainId: domainId);
    if (response!.success) {
      Log.v("Competition List  DATA : ${response.body}");
      CompetitionResponse competitionData =
          CompetitionResponse.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return CompetitionResponse();
    }
  }

  Future<CompetitionResponse> getJobCompApiList(
      bool? isPopular,
      bool isFilter,
      String? ids,
      int? isJob,
      int? myJob,
      String? widgetType,
      String? domainId) async {
    final response = await homeProvider.getJobCompApiList(
        isPopular: isPopular,
        isFiltter: isFilter,
        jobIds: ids,
        isJob: isJob,
        myJob: myJob,
        domainId: domainId,
        widgetType: widgetType);
    if (response!.success) {
      Log.v("Competition List  DATA : ${response.body}");
      CompetitionResponse competitionData =
          CompetitionResponse.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return CompetitionResponse();
    }
  }

  Future<DomainListResponse> getDomainList() async {
    final response = await homeProvider.getDomainList();
    if (response!.success) {
      Log.v("Domain List  DATA : ${response.body}");
      DomainListResponse competitionData =
          DomainListResponse.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return DomainListResponse();
    }
  }

  Future<JobDomainResponse> jobDomainDetail(int domainId) async {
    final response = await homeProvider.jobDomainDetail(domainId);
    try {
      if (response!.success) {
        Log.v("Domain By ID List  DATA : ${response.body}");
        JobDomainResponse competitionData =
            JobDomainResponse.fromJson(response.body);
        return competitionData;
      } else {
        Log.v("====> ${response.body}");
        return JobDomainResponse();
      }
    } catch (e, stackTrace) {
      print('$stackTrace');
      return JobDomainResponse();
    }
  }

  Future<DomainFilterListResponse> getFilterDomainList(String ids) async {
    final response = await homeProvider.getFilterDomainList(ids);
    if (response!.success) {
      Log.v("Domain Filter  List  DATA : ${response.body}");
      DomainFilterListResponse competitionData = DomainFilterListResponse();
      try {
        competitionData = DomainFilterListResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        print('exception is $stacktrace');
      }
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return DomainFilterListResponse();
    }
  }

  Future<CompetitionContentListResponse> getCompetitionContentListNew(
      int? competitionId, int? isApplied) async {
    final response = await homeProvider.getCompetitionContentList(
        competitionId: competitionId, isApplied: isApplied);
    if (response!.success) {
      Log.v("Competition Content List  DATA : ${response.body}");
      CompetitionContentListResponse competitionData =
          CompetitionContentListResponse();
      try {
        competitionData =
            CompetitionContentListResponse.fromJson(response.body);
        return competitionData;
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }

      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return CompetitionContentListResponse.fromJson(response.body);
    }
  }

  Future<AddPortfolioResp> addProfessional({Map<String, dynamic>? data}) async {
    final response = await homeProvider.addProfessional(data: data);
    if (response!.success) {
      Log.v("Add Professional Content  DATA : ${response.body}");
      AddPortfolioResp addProfessionalData =
          AddPortfolioResp.fromJson(response.body);
      return addProfessionalData;
    } else {
      Log.v("====> ${response.body}");
      return AddPortfolioResp.fromJson(response.body);
    }
  }

  Future<PortfolioResponse?> getPortfolio(int? userId) async {
    print('new portfolio data event is $userId');
    final response = await homeProvider.getPortfolio(userId);

    if (response!.success) {
      Log.v("Portfolio Content  DATA : ${response.body}");
      PortfolioResponse? portfolioResponse;
      try {
        portfolioResponse = PortfolioResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('Exception: ' + e.toString());
        Log.v('Stacktrace: ' + stacktrace.toString());
      }
      return portfolioResponse;
    } else {
      Log.v("====> ${response.body}");
      return PortfolioResponse.fromJson(response.body);
    }
  }

  Future<AddPortfolioResp?> uploadProfile({Map<String, dynamic>? data}) async {
    final response = await homeProvider.uploadProfile(data!);

    if (response!.success) {
      Log.v("Portfolio Content  DATA : ${response.body}");
      AddPortfolioResp portfolioResponse =
          AddPortfolioResp.fromJson(response.body);
      return portfolioResponse;
    } else {
      Log.v("====> ${response.body}");
      return AddPortfolioResp.fromJson(response.body);
    }
  }

  Future<AddPortfolioResp?> addSocial({Map<String, dynamic>? data}) async {
    try {
      final response = await homeProvider.addSocial(data!);

      if (response!.success) {
        Log.v("Add Social Content  DATA : ${response.body}");
        AddPortfolioResp portfolioResponse =
            AddPortfolioResp.fromJson(response.body);
        return portfolioResponse;
      } else {
        Log.v("====**> ${response.body}");

        return AddPortfolioResp(error: response.body);
      }
    } catch (e) {}
    return null;
  }

  Future<PortfolioCompetitionResponse?> getPortfolioCompetition(
      int? userId) async {
    final response = await homeProvider.getPortfolioCompetition(userId);

    if (response!.success) {
      Log.v("Get portfolio competition response  DATA : ${response.body}");
      PortfolioCompetitionResponse portfolioResponse =
          PortfolioCompetitionResponse.fromJson(response.body);
      return portfolioResponse;
    } else {
      Log.v("====> ${response.body}");
      return PortfolioCompetitionResponse.fromJson(response.body);
    }
  }

  Future<CompetitionMyActivityResponse?> getCompetitionMyActivity(
      {String? competitionType}) async {
    try {
      final response = await homeProvider.getCompetitionMyActivity(
          competitionType: competitionType);

      if (response!.success) {
        Log.v("Get portfolio competition response  DATA : ${response.body}");
        CompetitionMyActivityResponse portfolioResponse =
            CompetitionMyActivityResponse.fromJson(response.body);
        return portfolioResponse;
      } else {
        Log.v("====> ${response.body}");
        return CompetitionMyActivityResponse.fromJson(response.body);
      }
    } catch (e, stackTrace) {
      debugPrint('stacktrace is $stackTrace ans $e');
    }
    return null;
  }

  Future<TopScoringResponse?> topScoringUser(
      {int? userId, bool? skipCurrentUser}) async {
    final response = await homeProvider.topScoringUser(
        userId: userId, skipCurrentUser: skipCurrentUser);
    if (response!.success) {
      Log.v("top scoring response  DATA : ${response.body}");
      try {
        TopScoringResponse portfolioResponse =
            TopScoringResponse.fromJson(response.body);
        return portfolioResponse;
      } catch (e, stackTrace) {
        Log.v(stackTrace);
      }
    } else {
      Log.v("====> ${response.body}");
      return TopScoringResponse.fromJson(response.body);
    }
    return null;
  }

  Future<AddPortfolioResp> addPortfolio({Map<String, dynamic>? data}) async {
    final response = await homeProvider.addPortfolio(data: data);
    if (response!.success) {
      Log.v("Add Portfolio Content  DATA : ${response.body}");
      AddPortfolioResp competitionData =
          AddPortfolioResp.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return AddPortfolioResp.fromJson(response.body);
    }
  }

  Future<AddPortfolioResp> addPortfolioProfile(
      {Map<String, dynamic>? data}) async {
    final response = await homeProvider.addPortfolioProfile(data: data);
    if (response!.success) {
      Log.v("Add Portfolio Profile DATA : ${response.body}");
      AddPortfolioResp competitionData =
          AddPortfolioResp.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return AddPortfolioResp.fromJson(response.body);
    }
  }

  Future<AddPortfolioResp> addResume({Map<String, dynamic>? data}) async {
    final response = await homeProvider.addResume(data!);
    if (response!.success) {
      Log.v("Add Portfolio Resume DATA : ${response.body}");
      AddPortfolioResp competitionData =
          AddPortfolioResp.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return AddPortfolioResp.fromJson(response.body);
    }
  }

  Future<SingularisPortfolioDelete> singularisPortfolioDelete(
      int portfolioId) async {
    final response = await homeProvider.singularisDeletePortfolio(portfolioId);
    if (response!.success) {
      Log.v("Delete Portfolio Content  DATA : ${response.body}");
      SingularisPortfolioDelete competitionData =
          SingularisPortfolioDelete.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return SingularisPortfolioDelete.fromJson(response.body);
    }
  }

  //leaderboard
  Future<LeaderboardResponse> getLeaderboard(
      int? id, String? type, int? skipotherUser, int? skipcurrentUser) async {
    final response = await homeProvider.getLeaderboard(
        id: id,
        type: type,
        skipcurrentUser: skipcurrentUser,
        skipotherUser: skipotherUser);
    if (response!.success) {
      Log.v("Competition Content List  DATA : ${response.body}");
      LeaderboardResponse competitionData =
          LeaderboardResponse.fromJson(response.body);
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return LeaderboardResponse.fromJson(response.body);
    }
  }

  Future<TrainingDetailResponse> getTrainingDetail(int? programId) async {
    final response = await homeProvider.getTrainingDetail(programId);
    if (response!.success) {
      Log.v("Training Detail  DATA : ${response.body}");
      TrainingDetailResponse competitionData =
          TrainingDetailResponse.fromJson(response.body);

      // var box =
      Hive.box("content");
      // box.put("competitionDetail",
      //     competitionData.data?.module?.map((e) => e.toJson()).toList());
      return competitionData;
    } else {
      Log.v("====> ${response.body}");
      return TrainingDetailResponse();
    }
  }

  Future<CommentListResponse> getComment(int? postId) async {
    final response = await homeProvider.getComment(postId);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      CommentListResponse commentListResponse =
          CommentListResponse.fromJson(response.body);

      return commentListResponse;
    } else {
      Log.v("====> ${response.body}");
      return CommentListResponse();
    }
  }

  Future<PostCommentResponse> postComment(
      int? postId, int? parentId, String? comment) async {
    final response = await homeProvider.postComment(postId, parentId, comment);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      PostCommentResponse postCommentResponse =
          PostCommentResponse.fromJson(response.body);

      return postCommentResponse;
    } else {
      Log.v("====> ${response.body}");
      return PostCommentResponse();
    }
  }

  Future<JoyConentListResponse> getjoyContentList(
      JoyContentListEvent event) async {
    final response = await homeProvider.getjoyContentList(event);
    if (response!.success) {
      Log.v("RESPONSE DATA : ${response.body}");
      JoyConentListResponse joyConentListResponse = JoyConentListResponse();
      try {
        joyConentListResponse = JoyConentListResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v("Exception $stacktrace");
      }
      var box = Hive.box("content");
      box.put("joyContentListResponse",
          joyConentListResponse.data!.list?.map((e) => e.toJson()).toList());

      return joyConentListResponse;
    } else {
      Log.v("====> ${response.body}");
      return JoyConentListResponse();
    }
  }

  Future<JoyConentListResponse> getjoyContentByPostId({int? postId}) async {
    final response = await homeProvider.getjoyContentByPostId(postId: postId);
    if (response!.success) {
      Log.v("RESPONSE DATA : ${response.body}");
      JoyConentListResponse joyConentListResponse = JoyConentListResponse();
      try {
        joyConentListResponse = JoyConentListResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v("Exception $stacktrace");
      }
      var box = Hive.box("content");
      box.put("joyContentListResponse",
          joyConentListResponse.data!.list?.map((e) => e.toJson()).toList());

      return joyConentListResponse;
    } else {
      Log.v("====> ${response.body}");
      return JoyConentListResponse();
    }
  }

  Future<DashboardViewResponse> getDashboardIsVisible() async {
    final response = await homeProvider.getDashboardIsVisible();
    if (response!.success) {
      Log.v("RESPONSE DATA : ${response.body}");
      DashboardViewResponse dashboardViewResponse =
          DashboardViewResponse.fromJson(response.body);
      var box = Hive.box(DB.CONTENT);
      try {
        box.put("getDashboardIsVisible", dashboardViewResponse.data?.toJson());
      } catch (e) {}
      return dashboardViewResponse;
    } else {
      Log.v("====> ${response.body}");
      return DashboardViewResponse();
    }
  }

  Future<OrganizationProgramListResp> getOrganizationProgram(
      int? fetchGoalList) async {
    final response = await homeProvider.getOrganizationProgram(fetchGoalList);
    if (response!.success) {
      return OrganizationProgramListResp.fromJson(response.body);
    } else {
      Log.v("====> ${response.body}");
      return OrganizationProgramListResp.fromJson(response.body);
    }
  }

  Future<GoalInterestAreaResponse> getGoalInterestArea(
      int? fetchGoalList) async {
    final response = await homeProvider.getOrganizationProgram(fetchGoalList);
    if (response!.success) {
      return GoalInterestAreaResponse.fromJson(response.body);
    } else {
      return GoalInterestAreaResponse.fromJson(response.body);
    }
  }

  Future<DashboardContentResponse> getDasboardList() async {
    final response = await homeProvider.getDasboardList();
    if (response!.success) {
      Log.v("RESPONSE DATA : ${response.body}");
      DashboardContentResponse dashboardViewResponse =
          DashboardContentResponse();
      try {
        dashboardViewResponse =
            DashboardContentResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('exception $stacktrace');
      }

      var box = Hive.box(DB.CONTENT);

      box.put(
          "dashboard_recommended_courses_limit",
          dashboardViewResponse.data?.dashboardRecommendedCoursesLimit
              ?.map((e) => e.toJson())
              .toList());

      box.put(
          'dashboard_reels_limit',
          dashboardViewResponse.data?.dashboardReelsLimit
              ?.map((e) => e.toJson())
              .toList());
      box.put(
          'dashboard_carvan_limit',
          dashboardViewResponse.data?.dashboardCarvanLimit
              ?.map((e) => e.toJson())
              .toList());
      box.put(
          'dashboard_featured_content_limit',
          dashboardViewResponse.data?.dashboardFeaturedContentLimit
              ?.map((e) => e.toJson())
              .toList());
      box.put(
          'dashboard_my_courses_limit',
          dashboardViewResponse.data?.dashboardMyCoursesLimit
              ?.map((e) => e.toJson())
              .toList());
      box.put(
          'dashboard_sessions_limit',
          dashboardViewResponse.data?.todoActivitiesLimit?.todayClasses
              ?.map((e) => e.toJson())
              .toList());
      box.put(
          'recent_activity',
          dashboardViewResponse.data?.todoActivitiesLimit?.recentActivity
              ?.map((e) => e.toJson())
              .toList());

      // box.put("getDasboardList",
      //     dashboardViewResponse.data?.map((e) => e.toJson()).toList());

      return dashboardViewResponse;
    } else {
      Log.v("====> ${response.body}");
      return DashboardContentResponse();
    }
  }

  Future<onBoardSessions> getLiveClasses() async {
    final response = await homeProvider.getLiveClasses();
    if (response!.success) {
      Log.v("RESPONSE DATA : ${response.body}");
      onBoardSessions joyConentListResponse = onBoardSessions();
      try {
        joyConentListResponse = onBoardSessions.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }
      return joyConentListResponse;
    } else {
      Log.v("====> ${response.body}");
      return onBoardSessions();
    }
  }

  Future<GenerateCertificateResponse?> generateCertificate(
      int certificateId, int programId, String programName) async {
    final response = await homeProvider.generateCertificate(
        certificateId, programId, programName);
    try {
      if (response!.success) {
        Log.v("SUCCESS DATA : ${json.encode(response.body)}");

        GenerateCertificateResponse resp =
            GenerateCertificateResponse.fromJson(response.body);
        return resp;
      } else {
        Log.v("====> ${response.body}");
        return GenerateCertificateResponse();
      }
    } catch (e, stacktrace) {
      print(stacktrace);
      print(e);
    }
    return null;
  }

  Future<ProgramListResponse> getPrograms() async {
    final response = await homeProvider.getPrograms();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      ProgramListResponse programListResponse =
          ProgramListResponse.fromJson(response.body);
      return programListResponse;
    } else {
      Log.v("====> ${response.body}");
      return ProgramListResponse();
    }
  }

  //New
  Future<SemesterListResponse> getSemesterList() async {
    final response = await homeProvider.getSemesterList();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      SemesterListResponse semesterListResponse =
          SemesterListResponse.fromJson(response.body);
      return semesterListResponse;
    } else {
      Log.v("====> ${response.body}");
      return SemesterListResponse();
    }
  }

  Future<CompanyJobListResponse> popularjobInternship() async {
    final response = await homeProvider.popularjobInternship();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      CompanyJobListResponse companyListResponse =
          CompanyJobListResponse.fromJson(response.body);
      return companyListResponse;
    } else {
      Log.v("====> ${response.body}");
      return CompanyJobListResponse();
    }
  }

  Future<CompanyJobListResponse> popularInternship() async {
    final response = await homeProvider.popularInternship();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      CompanyJobListResponse companyListResponse =
          CompanyJobListResponse.fromJson(response.body);
      return companyListResponse;
    } else {
      Log.v("====> ${response.body}");
      return CompanyJobListResponse();
    }
  }

  Future<CompanyJobListResponse> companyJobs(String name) async {
    final response = await homeProvider.companyJobs(name);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      CompanyJobListResponse companyListResponse =
          CompanyJobListResponse.fromJson(response.body);
      return companyListResponse;
    } else {
      Log.v("====> ${response.body}");
      return CompanyJobListResponse();
    }
  }

  Future<AssessmentDetailsResponse> getAssessmentDetails(
      {int? contentId}) async {
    try {
      final response =
          await homeProvider.getAssessmentDetails(contentId: contentId);
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        AssessmentDetailsResponse companyListResponse =
            AssessmentDetailsResponse.fromJson(response.body);
        return companyListResponse;
      } else {
        Log.v("====> ${response.body}");
        return AssessmentDetailsResponse();
      }
    } catch (e, stackTrace) {
      Log.v('exception is $e and $stackTrace');
      return AssessmentDetailsResponse();
    }
  }

  Future<AssessmentCertificateResponse> getAssessmentCertificate(
      {int? certificateId, int? contentId}) async {
    try {
      final response = await homeProvider.getAssessmentCertificate(
          certificateId: certificateId, contentId: contentId);
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        AssessmentCertificateResponse companyListResponse =
            AssessmentCertificateResponse.fromJson(response.body);
        return companyListResponse;
      } else {
        Log.v("====> ${response.body}");
        return AssessmentCertificateResponse();
      }
    } catch (e, stackTrace) {
      Log.v("Exception occurred: $e and $stackTrace");
      debugPrint('exception is $e and $stackTrace');
      return AssessmentCertificateResponse();
    }
  }

  Future<CompanyListResponse> topCompanies() async {
    final response = await homeProvider.topCompanies();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      CompanyListResponse companyResponse =
          CompanyListResponse.fromJson(response.body);
      return companyResponse;
    } else {
      Log.v("====> ${response.body}");
      return CompanyListResponse();
    }
  }

  Future<FacultyBatchDetailsResponse?> getFacultyBatchDetails(
      int? courseId) async {
    try {
      final response = await homeProvider.getFacultyBatchDetails(courseId);
      Log.v("ERROR DATA test: return body ${response?.body}");

      if (response!.success) {
        Log.v("ERROR DATA test: return 1  ${response.success}");

        FacultyBatchDetailsResponse facultyBatchDetails =
            FacultyBatchDetailsResponse();
        Log.v("ERROR DATA test: return 2 ");

        try {
          facultyBatchDetails =
              FacultyBatchDetailsResponse.fromJson(response.body);
        } catch (e, stackTrace) {
          debugPrint("ERROR DATA test: return $e");
          debugPrint("$stackTrace");
        }
        Log.v("ERROR DATA test: return  ${facultyBatchDetails}");

        return facultyBatchDetails;
      } else {
        Log.v("====> ${response.body}");
        return FacultyBatchDetailsResponse();
      }
    } catch (e, stacktrace) {
      debugPrint('the stack trace is $stacktrace');

      return null;
    }
  }

  Future<FacultyBatchDetailsResponse?> getFacultyCourseDetails(
      {int? programId}) async {
    try {
      final response =
          await homeProvider.getFacultyCourseDetails(programId: programId);
      if (response!.success) {
        Log.v("response!.success : ${response.body}");

        FacultyBatchDetailsResponse facultyCourseDetails =
            FacultyBatchDetailsResponse.fromJson(response.body);
        return facultyCourseDetails;
      } else {
        Log.v("====?> ${response.body}");
        return FacultyBatchDetailsResponse();
      }
    } catch (e, StackTrace) {
      log('$e and stacktrace is $StackTrace');
    }
    return null;
  }

  Future<MatchingJobsResponse?> getMatchingJobs() async {
    try {
      final response = await homeProvider.getMatchingJobs();
      if (response!.success) {
        Log.v("response!.success : ${response.body}");

        MatchingJobsResponse facultyCourseDetails =
            MatchingJobsResponse.fromJson(response.body);
        return facultyCourseDetails;
      } else {
        Log.v("====?> ${response.body}");
        return MatchingJobsResponse();
      }
    } catch (e, StackTrace) {
      log('$e and stacktrace is $StackTrace');
    }
    return null;
  }

  // Future<FacultyBatchDetailsResponse?> getFacultyCourseDetails({int?programId}) async {
  //   try {
  //     final response = await homeProvider.getFacultyCourseDetails(programId: programId);
  //       Log.v("ERROR DATA test: return body ${response?.body}");

  //     if (response!.success) {
  //       Log.v("ERROR DATA test: return 1  ${response.success}");

  //       FacultyBatchDetailsResponse facultyBatchDetails = FacultyBatchDetailsResponse();
  //       Log.v("ERROR DATA test: return 2 ");

  //     try{
  //         facultyBatchDetails = FacultyBatchDetailsResponse.fromJson(response.body);
  //          Log.v("ERROR DATA test: return 3 ");

  //     }
  //     catch(e, stackTrace){
  //       debugPrint("ERROR DATA test: return $e");
  //       debugPrint("$stackTrace");
  //     }
  //       Log.v("ERROR DATA test: return  ${facultyBatchDetails}");

  //       return facultyBatchDetails;
  //     } else {
  //       Log.v("====> ${response.body}");
  //       return FacultyBatchDetailsResponse();
  //     }
  //   } catch (e, stacktrace) {
  //     debugPrint('the stack trace is $stacktrace');

  //     return null;
  //   }
  // }

  Future<FacultyBatchClassResponse> getFacultyBatchClass(
      {String? selectedDate}) async {
    final response =
        await homeProvider.getFacultyBatchClass(selectedDate: selectedDate);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      FacultyBatchClassResponse companyResponse =
          FacultyBatchClassResponse.fromJson(response.body);
      return companyResponse;
    } else {
      Log.v("====> ${response.body}");
      return FacultyBatchClassResponse();
    }
  }

  //  Future<WowDashboardResponse> getWowDashboard() async {
  //   final response = await homeProvider.getWowDashboard();
  //   if (response!.success) {
  //     Log.v("ERROR DATA : ${response.body}");
  //     WowDashboardResponse companyResponse =
  //         WowDashboardResponse.fromJson(response.body);
  //     return companyResponse;
  //   } else {
  //     Log.v("====> ${response.body}");
  //     return WowDashboardResponse();
  //   }
  // }

  Future<WowDashboardResponse> getWowDashboard() async {
    try {
      final response = await homeProvider.getWowDashboard();
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        WowDashboardResponse companyResponse =
            WowDashboardResponse.fromJson(response.body);
        return companyResponse;
      } else {
        Log.v("====> ${response.body}");
        return WowDashboardResponse();
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e and $stackTrace');

      throw e;
    }
  }

  Future<HodProgramListtResponse> getHodModuleList() async {
    final response = await homeProvider.getHodModuleList();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      HodProgramListtResponse companyResponse =
          HodProgramListtResponse.fromJson(response.body);
      return companyResponse;
    } else {
      Log.v("====> ${response.body}");
      return HodProgramListtResponse();
    }
  }

  Future<FacultyModuleListResponse> getFacultyModuleList(
      {int? courseId}) async {
    try {
      final response =
          await homeProvider.getFacultyModuleList(courseId: courseId);
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        FacultyModuleListResponse companyResponse =
            FacultyModuleListResponse.fromJson(response.body);
        return companyResponse;
      } else {
        Log.v("====> ${response.body}");
        return FacultyModuleListResponse();
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e and $stackTrace');
    }
    return FacultyModuleListResponse();
  }

  Future<ModuleLeaderProgramListResponse> getModuleLeaderProgramList(
      {int? courseId}) async {
    try {
      final response =
          await homeProvider.getModuleLeaderProgramList(courseId: courseId);
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        ModuleLeaderProgramListResponse companyResponse =
            ModuleLeaderProgramListResponse.fromJson(response.body);
        return companyResponse;
      } else {
        Log.v("====> ${response.body}");
        return ModuleLeaderProgramListResponse();
      }
    } catch (e) {
      log('$e and stacktrace is $StackTrace');
    }
    return ModuleLeaderProgramListResponse();
  }

  Future<FacultyBatchAssignmentResponse> getFacultyBatchAssignment(
      {int? courseId, String? selectedDate}) async {
    final response = await homeProvider.getFacultyBatchAssignment(
        courseId: courseId, selectedDate: selectedDate);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      FacultyBatchAssignmentResponse companyResponse =
          FacultyBatchAssignmentResponse.fromJson(response.body);
      return companyResponse;
    } else {
      Log.v("====> ${response.body}");
      return FacultyBatchAssignmentResponse();
    }
  }

  Future<FacultyBatchAssessmentResponse> getFacultyBatchAssessment(
      {int? courseId, String? selectedDate}) async {
    final response = await homeProvider.getFacultyBatchAssessment(
        courseId: courseId, selectedDate: selectedDate);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      FacultyBatchAssessmentResponse companyResponse =
          FacultyBatchAssessmentResponse.fromJson(response.body);
      return companyResponse;
    } else {
      Log.v("====> ${response.body}");
      return FacultyBatchAssessmentResponse();
    }
  }

  Future<ProgramCompletionResponse?> getProgramCompletion(
      {dynamic programCompId}) async {
    try {
      final response =
          await homeProvider.getProgramCompletion(programCompId: programCompId);
      if (response!.success) {
        Log.v("response!.success : ${response.body}");

        ProgramCompletionResponse programCompletion =
            ProgramCompletionResponse.fromJson(response.body);
        return programCompletion;
      } else {
        Log.v("====?> ${response.body}");
        return ProgramCompletionResponse();
      }
    } catch (e, StackTrace) {
      log('$e and stacktrace is $StackTrace');
    }
    return null;
  }

//   Future<ProgramCompletionResponse> getProgramCompletion({dynamic programCompId}) async {
// final response = await homeProvider.getProgramCompletion(programCompId: programCompId);
//     if (response!.success) {
//       Log.v("ERROR DATA : ${response.body}");
//       ProgramCompletionResponse programCompletion = ProgramCompletionResponse.fromJson(response.body);
//       return programCompletion;
//     } else {
//       Log.v("====> ${response.body}");
//       // Return an empty ProgramCompletionResponse instead of null
//       return ProgramCompletionResponse();
//     }

// }

  Future<UpdateAttendanceResp> getUpdateAttendance(
      {int? contentId, String? attendance, List<int>? users}) async {
    final response = await homeProvider.getUpdateAttendance(
        contentId: contentId, attendance: attendance, users: users);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      UpdateAttendanceResp companyResponse =
          UpdateAttendanceResp.fromJson(response.body);
      return companyResponse;
    } else {
      Log.v("====> ${response.body}");
      return UpdateAttendanceResp();
    }
  }

  Future<MarkAttendanceResponse> getMarkAttendance(
      {int? batchId, int? classId}) async {
    final response = await homeProvider.getMarkAttendance(
        batchId: batchId, classId: classId);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      MarkAttendanceResponse markAttendance =
          MarkAttendanceResponse.fromJson(response.body);
      return markAttendance;
    } else {
      Log.v("====> ${response.body}");
      return MarkAttendanceResponse();
    }
  }

  Future<AttendancePercentageResponse> getAttendancePercentage(
      {programCompletionId}) async {
    final response = await homeProvider.getAttendancePercentage(
        programCompletionId: programCompletionId);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      AttendancePercentageResponse companyResponse =
          AttendancePercentageResponse.fromJson(response.body);
      return companyResponse;
    } else {
      Log.v("====> ${response.body}");
      return AttendancePercentageResponse();
    }
  }

  Future<CourseCategoryListIdResponse> getCourseWithId(
      {int? id, int? semesterID}) async {
    final response = await homeProvider.getCourseWithId(id, semesterID);
    if (response!.success) {
      Log.v("SUCESS DATA : ${response.body}");
      CourseCategoryListIdResponse courseCategoryListIdResponse =
          CourseCategoryListIdResponse.fromJson(response.body);

      var box = Hive.box("content");
      box.put(
          "courseCategoryList",
          courseCategoryListIdResponse.data!.programs!
              .map((e) => e.toJson())
              .toList());
      return courseCategoryListIdResponse;
    } else {
      return CourseCategoryListIdResponse();
    }
  }

  Future<GetContentResp> getContentList({int? contentType}) async {
    final response =
        await homeProvider.getContentList(contentType: contentType);
    if (response!.success) {
      GetContentResp resp = GetContentResp.fromJson(response.body);
      var box = Hive.box("content");
      if (contentType == 16) {
        box.put(
            "announcements", resp.data!.list!.map((e) => e.toJson()).toList());
      }
      if (contentType == 18) {
        box.put("library", resp.data!.list!.map((e) => e.toJson()).toList());
      }
      if (contentType == 10) {
        box.put("benefits", resp.data!.list!.map((e) => e.toJson()).toList());
      }
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return GetContentResp(
          error: response.body == null
              ? "Something went wrong:" as List<String>?
              : response.body);
    }
  }

  Future<FeaturedVideoResponse> getFeaturedVideo() async {
    final response = await homeProvider.getFeaturedVideo();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      FeaturedVideoResponse featuredVideoResponse =
          FeaturedVideoResponse.fromJson(response.body);
      return featuredVideoResponse;
    } else {
      Log.v("====> ${response.body}");
      return FeaturedVideoResponse();
    }
  }

  Future<JoyCategoryResponse> getInterestPrograms() async {
    final response = await homeProvider.getInterestPrograms();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      JoyCategoryResponse interestResponse =
          JoyCategoryResponse.fromJson(response.body);

      var box = Hive.box("content");
      box.put("joy_category",
          interestResponse.data!.list!.map((e) => e.toJson()).toList());
      return interestResponse;
    } else {
      Log.v("====> ${response.body}");
      return JoyCategoryResponse();
    }
  }

  Future<MapInterestResponse> mapInterest(
      String? param, String? mapType) async {
    final response = await homeProvider.mapInterest(param, mapType);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      MapInterestResponse mapInterestResponse =
          MapInterestResponse.fromJson(response.body);
      return mapInterestResponse;
    } else {
      Log.v("====> ${response.body}");
      return MapInterestResponse();
    }
  }

  Future<popularCourses> getPopularCourses() async {
    final response = await homeProvider.getPopularCourses();
    if (response!.success) {
      popularCourses mapInterestResponse = popularCourses();
      try {
        mapInterestResponse = popularCourses.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }
      var box = Hive.box("content");
      box.put("popularCoursess",
          mapInterestResponse.data!.list!.map((e) => e.toJson()).toList());

      Log.v("ERROR DATA popularCoursess: ${response.body}");

      return mapInterestResponse;
    } else {
      Log.v("====> ${response.body}");
      return popularCourses();
    }
  }

  Future<BottomBarResponse> bottombarResponse() async {
    final response = await homeProvider.bottombarResponse();
    if (response!.success) {
      Log.v("Bttom Menu DATA : ${response.body}");
      BottomBarResponse resp = BottomBarResponse.fromJson(response.body);

      // filter according to role
      // resp.data?.menu = resp.data?.menu?.where((element) {
      //   bool containRole = element.role.toString().toLowerCase().contains(
      //       '${Preference.getString(Preference.ROLE)?.toLowerCase()}');
      //   return containRole;
      // }).toList();

      // if (Preference.getString(Preference.ROLE)?.toLowerCase() == 'presenter') {
      //   List<Menu>? tempMenu = resp.data?.menu
      //       ?.where((element) => [
      //             '/g-dashboard',
      //             '/g-carvaan',
      //             '/g-competitions'
      //           ].contains(element.url))
      //       .toList();
      //   resp.data?.menu = tempMenu;
      // }
      Log.v("Bttom Menu DATA len : ${resp.data?.menu?.length}");

      try {
        var box = Hive.box("content");
        box.put("bottomMenu", resp.data?.menu?.map((e) => e.toJson()).toList());
        box.put(
            "sideMenu", resp.data?.menuSide?.map((e) => e.toJson()).toList());
      } catch (e) {}

      return resp;
    } else {
      Log.v("Error ====> ${response.body}");
      return BottomBarResponse();
    }
  }

  Future<popularCourses> getFilteredPopularCourses() async {
    final response = await homeProvider.getFilteredPopularCourses();
    print('getFilteredPopularCourses');
    if (response!.success) {
      popularCourses mapInterestResponse = popularCourses();
      try {
        mapInterestResponse = popularCourses.fromJson(response.body);
      } catch (e, stackTrace) {
        print('stack $stackTrace');
      }

      var box = Hive.box("content");
      box.put("short_term",
          mapInterestResponse.data!.shortTerm!.map((e) => e.toJson()).toList());
      box.put(
          "recommended",
          mapInterestResponse.data!.recommended!
              .map((e) => e.toJson())
              .toList());

      log("recommnaded data ${mapInterestResponse.data!.recommended!.length}");
      box.put(
          "most_viewed",
          mapInterestResponse.data!.mostViewed!
              .map((e) => e.toJson())
              .toList());
      box.put(
          "highly_rated",
          mapInterestResponse.data!.highlyRated!
              .map((e) => e.toJson())
              .toList());
      box.put(
          "other_learners",
          mapInterestResponse.data!.otherLearners!
              .map((e) => e.toJson())
              .toList());

      return mapInterestResponse;
    } else {
      Log.v("====> ${response.body}");
      return popularCourses();
    }
  }

  Future<GCarvaanPostResponse> GCarvaanPost(
      int callCount, int? postId, bool userActivity) async {
    final response =
        await homeProvider.GCarvaanPost(callCount, postId, userActivity);

    if (response!.success) {
      GCarvaanPostResponse gcarvaanPost = GCarvaanPostResponse();
      try {
        gcarvaanPost = GCarvaanPostResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('the stack trace is $stacktrace');
      }

      var box = Hive.box("content");
      if (postId == null) {
        try {
          box.put("gcarvaan_post",
              gcarvaanPost.data?.list?.map((e) => e.toJson()).toList());
        } catch (e, stacktrace) {
          Log.v('make api call ### $stacktrace');
        }
      }

      return gcarvaanPost;
    } else {
      Log.v("====> ${response.body}");
      return GCarvaanPostResponse();
    }
  }

  Future<GCarvaanPostResponse> getCarvaanPost(int? postId) async {
    final response = await homeProvider.getCarvaanPost(postId);

    if (response!.success) {
      GCarvaanPostResponse gcarvaanPost = GCarvaanPostResponse();
      try {
        gcarvaanPost = GCarvaanPostResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('the stack trace is $stacktrace');
      }

      return gcarvaanPost;
    } else {
      Log.v("====> ${response.body}");
      return GCarvaanPostResponse();
    }
  }

  Future<GReelsPostResponse> GReelsPost(GReelsPostEvent event) async {
    final response = await homeProvider.GReelsPost(event);
    print("the ressponse now iss ${response?.body}");
    if (response!.success) {
      GReelsPostResponse gReelsPost = GReelsPostResponse();
      try {
        gReelsPost = GReelsPostResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }
      var box = Hive.box("content");
      box.put("greels_post",
          gReelsPost.data!.list!.map((e) => e.toJson()).toList());

      Log.v("ERROR DATA : ${response.body}");
      return gReelsPost;
    } else {
      Log.v("====> ${response.body}");
      return GReelsPostResponse();
    }
  }

  Future<GReelsPostResponse> getSingleReel(int reelId) async {
    final response = await homeProvider.getSingleReel(reelId);
    if (response!.success) {
      GReelsPostResponse gReelsPost = GReelsPostResponse();
      try {
        gReelsPost = GReelsPostResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }

      Log.v("ERROR DATA : ${response.body}");
      return gReelsPost;
    } else {
      Log.v("====> ${response.body}");
      return GReelsPostResponse();
    }
  }

  Future<CreatePostResponse> CreatePost(
    String? thumbnail,
    int? contentType,
    String? postType,
    String? title,
    String? description,
    List<String?>? filePaths,
  ) async {
    try {
      final response = await homeProvider.createPost(
        thumbnail,
        contentType,
        postType,
        title,
        description,
        filePaths,
      );
      if (response!.success) {
        CreatePostResponse createPostResp =
            CreatePostResponse.fromJson(response.body);

        Log.v("ERROR DATA : ${response.body}");

        return createPostResp;
      } else {
        Log.v("====> ${response.body}");
        return CreatePostResponse();
      }
    } catch (e) {
      Log.v('the expection is $e');
      Get.closeAllSnackbars();

      Get.rawSnackbar(
        messageText: Text(
          'error__uploading_reel',
          style: Styles.regular(size: 14),
        ).tr(),
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorConstants.WHITE,
        borderRadius: 4,
        boxShadows: [
          BoxShadow(
              color: Color(0xff898989).withValues(alpha: 0.1),
              offset: Offset(0, 4.0),
              blurRadius: 11)
        ],
      );
    }
    return CreatePostResponse();
  }

  Future<LanguageResponse> getLanguage(int? languageType) async {
    final response = await homeProvider.getLanguage(languageType);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      LanguageResponse languageResponse =
          LanguageResponse.fromJson(response.body);
      return languageResponse;
    } else {
      Log.v("====> ${response.body}");
      return LanguageResponse();
    }
  }

  Future<ListVideoResumeResponse> listVideoResume(int? index) async {
    final response = await homeProvider.listVideoResume(index);
    ListVideoResumeResponse languageResponse = ListVideoResumeResponse();
    if (response!.success) {
      log("ERROR DATA : ${response.body}");
      try {
        languageResponse = ListVideoResumeResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        debugPrint('ERROR DATA :$stacktrace');
      }
      log("ERROR DATA done}");

      return languageResponse;
    } else {
      Log.v("====> ${response.body}");
      return ListVideoResumeResponse();
    }
  }

  Future<DeleteVideoResumeResponse> deleteVideoResume(
      DeleteVideoResumeEvent event) async {
    final response = await homeProvider.deleteVideoResume(event);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      DeleteVideoResumeResponse resp =
          DeleteVideoResumeResponse.fromJson(response.body);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return DeleteVideoResumeResponse();
    }
  }

  Future<AnalyseVideoResumeResponse> anlyseVideoResume(
      AnalyseVideoResumeEvent event) async {
    final response = await homeProvider.anlyseVideoResume(event);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      AnalyseVideoResumeResponse resp =
          AnalyseVideoResumeResponse.fromJson(response.body);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return AnalyseVideoResumeResponse();
    }
  }

  // Future<MasterLanguageResponse> getMasterLanguage() async {
  //   final response = await homeProvider.getMasterLanguage();
  //   if (response!.success) {
  //     Log.v("ERROR DATA : ${response.body}");
  //     MasterLanguageResponse languageResponse =
  //         MasterLanguageResponse.fromJson(response.body);
  //     return languageResponse;
  //   } else {
  //     Log.v("====> ${response.body}");
  //     return MasterLanguageResponse();
  //   }
  // }
  Future<MasterLanguageResponse> getMasterLanguage() async {
    try {
      final response = await homeProvider.getMasterLanguage();
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        MasterLanguageResponse languageResponse =
            MasterLanguageResponse.fromJson(response.body);
        return languageResponse;
      } else {
        Log.v("====> ${response.body}");
        return MasterLanguageResponse();
      }
    } catch (e, stackTrace) {
      debugPrint("Exception occurred: $e and $stackTrace");
      return MasterLanguageResponse(); // Return a default or empty response in case of an error
    }
  }

  Future<SkillSuggestionResponse> getSkillSuggestion() async {
    final response = await homeProvider.getSkillSuggestion();
    if (response!.success) {
      Log.v("Success DATA : ${response.body}");
      SkillSuggestionResponse skillSuggestion =
          SkillSuggestionResponse.fromJson(response.body);
      return skillSuggestion;
    } else {
      Log.v("====> ${response.body}");
      return SkillSuggestionResponse();
    }
  }

  Future<SkillRatingResponse> getSkillRating() async {
    final response = await homeProvider.getSkillRating();
    if (response!.success) {
      Log.v("Success DATA : ${response.body}");
      SkillRatingResponse skillRating =
          SkillRatingResponse.fromJson(response.body);
      return skillRating;
    } else {
      Log.v("====> ${response.body}");
      return SkillRatingResponse(
        status: 0,
        error: ["Something went wrong"],
      );
    }
  }

  Future<AddSkillResponse> addSkill(Map<String, dynamic> data) async {
    final response = await homeProvider.addSkill(data);
    if (response!.success) {
      Log.v("Success DATA : ${response.body}");
      AddSkillResponse addSkillResponse =
          AddSkillResponse.fromJson(response.body);
      return addSkillResponse;
    } else {
      Log.v("====> ${response.body}");
      return AddSkillResponse();
    }
  }

  Future<UserAnalyticsResp> UserAnalytics() async {
    final response = await homeProvider.getUserAnalytics();
    if (response!.success) {
      Log.v("SUCCESS DATA : ${response.body}");
      UserAnalyticsResp gameResponse = UserAnalyticsResp();
      try {
        gameResponse = UserAnalyticsResp.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }
      return gameResponse;
    } else {
      Log.v("====> ${response.body}");
      return UserAnalyticsResp();
    }
  }

  Future<LearningSpaceResponse> learningSpace() async {
    final response = await homeProvider.getLearningSpaceData();
    if (response!.success) {
      Log.v("SUCCESS DATA : ${response.body}");
      LearningSpaceResponse resp = LearningSpaceResponse();

      try {
        resp = LearningSpaceResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }

      var box = Hive.box("content");
      box.put("learningspace",
          resp.data!.learningSpace!.map((e) => e.toJson()).toList());

      return resp;
    } else {
      Log.v("==> ${response.body}");
      return LearningSpaceResponse();
    }
  }

  Future<AttemptTestResponse?> attemptTest({required String request}) async {
    try {
      final response = await homeProvider.attemptTest(request: request);
      if (response!.success) {
        AttemptTestResponse user = AttemptTestResponse.fromJson(response.body);
        if (user.status == 1) {
          return user;
        } else {
          return AttemptTestResponse(error: [user.error?.first], status: 0);
        }
      } else {
        return AttemptTestResponse(error: ["Something went wrong"], status: 0);
      }
    } catch (e, stacktrace) {
      Log.v("EXCEPTION  :  $e and $stacktrace");
    }
    return null;
  }

  Future<SaveAnswerResponse?> saveAnswer(
      {required SaveAnswerRequest request}) async {
    try {
      final response = await homeProvider.saveAnswer(request: request);
      if (response!.success) {
        SaveAnswerResponse user = SaveAnswerResponse();
        try {
          user = SaveAnswerResponse.fromJson(response.body);
        } catch (e, stackTrace) {
          Log.v('stack is $stackTrace');
        }
        if (user.status == 1) {
          return user;
        } else {
          return SaveAnswerResponse(status: 0);
        }
      } else {
        return SaveAnswerResponse(status: 0);
      }
    } catch (e) {
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  Future<SubmitAnswerResponse?> submitAnswer({String? request}) async {
    try {
      final response = await homeProvider.submitAnswer(request: request);
      if (response!.success) {
        SubmitAnswerResponse user =
            SubmitAnswerResponse.fromJson(response.body);
        if (user.status == 1) {
          return user;
        } else {
          return SubmitAnswerResponse(status: 0);
        }
      } else {
        return SubmitAnswerResponse(status: 0);
      }
    } catch (e) {
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  ///Email
  Future emailCodeSend({String? email, int? isSignup, int? forgotPass}) async {
    try {
      final response = await homeProvider.emailSendCode(
          email: email, isSignup: isSignup, forgotPass: forgotPass);
      if (response?.body['status'] == 1) {
        return 1;
      } else {
        return response?.body['message'];
      }
    } catch (e) {
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  Future<ParticiapteResp?> getParticipate(
      {String? name,
      String? email,
      String? mobileNo,
      int? isMobile,
      int? programId,
      int? countryCode}) async {
    try {
      final response = await homeProvider.getParticipate(
          name: name,
          email: email,
          mobileNo: mobileNo,
          programId: programId,
          isMobile: isMobile,
          countryCode: countryCode);
      if (response!.success) {
        ParticiapteResp participate = ParticiapteResp.fromJson(response.body);
        if (participate.status == 1) {
          return participate;
        } else {
          return ParticiapteResp(status: 0);
        }
      } else {
        return ParticiapteResp(status: 0);
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e and stacktrace is $stackTrace');
    }
    return null;
  }

  Future verifyEmailCodeAnswer({String? email, String? eCode}) async {
    try {
      final response =
          await homeProvider.verifyEmailCode(email: email, eCode: eCode);
      if (response?.body['status'] == 1) {
        return 1;
      } else {
        return 0;
      }
    } catch (e) {
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  Future<dynamic> passwordUpdate(
      {String? email, String? pass, String? locale}) async {
    try {
      final response = await homeProvider.passwordUpdate(
          email: email, pass: pass, locale: locale);
      // if (response?.body['status'] == 1) {
      //   return 1;
      // } else {
      //   return 0;
      // }
      return response;
    } catch (e) {
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  Future<TestReviewResponse?> reviewTest({required String request}) async {
    try {
      final response = await homeProvider.reviewTest(request: request);
      Log.v('response----${response}');
      if (response!.success) {
        TestReviewResponse user = TestReviewResponse();
        try {
          user = TestReviewResponse.fromJson(response.body);
        } catch (e, stacktrace) {
          Log.v('the $stacktrace');
        }
        if (user.status == 1) {
          return user;
        } else {
          return TestReviewResponse(error: [user.error?.first], status: 0);
        }
      } else {
        return TestReviewResponse(error: ["Something went wrong"], status: 0);
      }
    } catch (e, s) {
      Log.v(s);
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  Future<AssignmentSubmissionResponse?> getSubmissions({int? request}) async {
    try {
      final response = await homeProvider.getSubmissions(request: request);
      if (response!.success) {
        AssignmentSubmissionResponse user =
            AssignmentSubmissionResponse.fromJson(response.body);
        if (user.status == 1) {
          return user;
        } else {
          return AssignmentSubmissionResponse(
              error: [user.error?.first], status: 0);
        }
      } else {
        return AssignmentSubmissionResponse(
            error: ["Something went wrong"], status: 0);
      }
    } catch (e, s) {
      Log.v(s);
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  Future createPortfolio(Map<String, dynamic> data) async {
    final response = await homeProvider.createPortfolio(data);
    if (response!.success) {
      Log.v("Create Portfoio DATA : ${response.body}");
      CreatePortfolioResponse resp =
          CreatePortfolioResponse.fromJson(response.body);
      return resp;
    } else {
      Log.v("Error ====> ${response.body}");
      return;
    }
  }

  Future masterBrandCreate(Map<String, dynamic> data) async {
    final response = await homeProvider.masterBrandCreate(data);
    if (response!.success) {
      Log.v("Create Portfoio DATA : ${response.body}");
      MasterBrandResponse respBr = MasterBrandResponse.fromJson(response.body);

      Log.v("Create Portfoio DATA : ${respBr.status}");
      if (respBr.status == 1) {
        return respBr;
      }
    } else {
      Log.v("Error ====> ${response.body}");
      return;
    }
  }

  Future userBrandCreate(Map<String, dynamic> data) async {
    final response = await homeProvider.userBrandCreate(data);
    if (response!.success) {
      Log.v("Create Portfoio DATA : ${response.body}");
      return response.body;
    } else {
      Log.v("Error ====> ${response.body}");
      return;
    }
  }

  Future deletePortfolio(int? id) async {
    final response = await homeProvider.deletePortfolio(id!);
    if (response!.success) {
      Log.v("Delete Portfoio DATA : ${response.body}");
      DeletePortfolioResponse resp =
          DeletePortfolioResponse.fromJson(response.body);

      return resp;
    } else {
      Log.v("Error ====> ${response.body}");
      return;
    }
  }

  Future<ListPortfolioResponse> listPortfolio(String? type, int? userId) async {
    final response = await homeProvider.listPortfolio(type!, userId!);
    if (response!.success) {
      Log.v("List Portfoio DATA : ${response.body}");
      ListPortfolioResponse resp =
          ListPortfolioResponse.fromJson(response.body);

      return resp;
    } else {
      Log.v("Error ====> ${response.body}");
      return ListPortfolioResponse();
    }
  }

  Future updateVideoCompletion(
      int bookmark, int contentId, int completionPercent) async {
    final response = await homeProvider.updateVideoCompletion(
        bookmark, contentId, completionPercent);

    if (response!.success) {
      Log.v("Sucess DATA : ${response.body}");
    } else {
      Log.v("====> ${response.body}");
      return;
    }
  }

  Future<PiDetailResponse> piDetail(int userId) async {
    final response = await homeProvider.piDetail(userId);
    if (response!.success) {
      Log.v("RESPONSE DATA : ${response.body}");
      PiDetailResponse joyConentListResponse = PiDetailResponse();
      try {
        joyConentListResponse = PiDetailResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }
      return joyConentListResponse;
    } else {
      Log.v("====> ${response.body}");
      return PiDetailResponse();
    }
  }

  Future<GeneralResp?> trackAnnouncment(
      {TrackAnnouncementReq? trackAnnouncementReq}) async {
    final response = await homeProvider.trackAnnouncment(
        submitRewardReq: trackAnnouncementReq!);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      GeneralResp resp = GeneralResp.fromJson(response.body);
      Log.v("ERROR DATA : ${resp.toJson()}");
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return GeneralResp();
    }
  }

  Future<GeneralResp?> activityAttempt(
      {String? filePath, int? contentType, int? contentId}) async {
    final response =
        await homeProvider.activityAttempt(filePath, contentType, contentId);
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      GeneralResp resp = GeneralResp.fromJson(response.body);
      Log.v("ERROR DATA : ${resp.toJson()}");
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return GeneralResp();
    }
  }

  Future<SurveyDataResp?> getSurveyDataList({int? contentId, int? type}) async {
    try {
      final response = await homeProvider.getSurveyDataList(
          contentId: contentId, type: type);
      if (response!.success) {
        Log.v("ERROR DATA : ${json.encode(response.body)}");
        SurveyDataResp resp = SurveyDataResp.fromJson(response.body, type!);
        return resp;
      } else {
        Log.v("====> ${response.body}");
        return SurveyDataResp(
            error: response.body == null
                ? "Something went wrong:"
                : response.body);
      }
    } on Exception catch (e, s) {
      Log.v(s);
    }
    return null;
  }

  Future<GeneralResp> submitSurvey({SubmitSurveyReq? submitSurveyReq}) async {
    final response = await homeProvider.submitSurvey(req: submitSurveyReq);
    if (response!.success) {
      Log.v("ERROR DATA : ${json.encode(response.body)}");
      GeneralResp resp = GeneralResp.fromJson(response.body);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return GeneralResp(
          message:
              response.body == null ? "Something went wrong:" : response.body);
    }
  }

  Future<RemoveAccountResponse> removeAccount({String? type}) async {
    final response = await homeProvider.removeAccount(type: type);
    if (response!.success) {
      Log.v("DATA : ${json.encode(response.body)}");
      RemoveAccountResponse resp =
          RemoveAccountResponse.fromJson(response.body);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return RemoveAccountResponse.fromJson(response.body);
    }
  }

  Future<NotificationResp?> getNotifications() async {
    final response = await homeProvider.getNotifications();
    if (response!.success) {
      Log.v("ERROR DATA : ${response.body}");
      NotificationResp notificationResp =
          NotificationResp.fromJson(response.body);
      return notificationResp;
    } else {
      Log.v("====> ${response.body}");
      return NotificationResp();
    }
  }

  Future<NotificationsListResp?> getNotificationsList(
      {int? fromVal, int? toVal}) async {
    try {
      final response = await homeProvider.getNotificationsList(
          fromValue: fromVal, toValue: toVal);
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        NotificationsListResp notificationsListResp =
            NotificationsListResp.fromJson(response.body);
        return notificationsListResp;
      } else {
        Log.v("====> ${response.body}");
        return NotificationsListResp();
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<NotificationReadResp?> getNotificationsRead(
      {String? id, int? notiId, String? type, String? isRead}) async {
    try {
      final response = await homeProvider.getNotificationsRead(
          id: id, notiId: notiId, type: type, isRead: isRead);
      if (response!.success) {
        Log.v("ERROR DATA : ${response.body}");
        NotificationReadResp notificationReadResp =
            NotificationReadResp.fromJson(response.body);
        return notificationReadResp;
      } else {
        Log.v("====> ${response.body}");
        return NotificationReadResp();
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<GeneralResp?> submitPoll({PollSubmitRequest? submitSurveyReq}) async {
    final response = await homeProvider.submitPoll(req: submitSurveyReq);
    if (response!.success) {
      Log.v("ERROR DATA : ${json.encode(response.body)}");
      GeneralResp resp = GeneralResp.fromJson(response.body);
      return resp;
    } else {
      Log.v("====> ${response.body}");
      return GeneralResp(
          message:
              response.body == null ? "Something went wrong:" : response.body);
    }
  }

  Future<ZoomOpenUrlResponse?> getZoomOpenUrl(int contentId) async {
    final response = await homeProvider.getZoomOpenUrl(contentId);
    try {
      if (response?.body != null) {
        ZoomOpenUrlResponse resp = ZoomOpenUrlResponse.fromJson(response?.body);
        return resp;
      } else {
        return ZoomOpenUrlResponse();
      }
    } catch (e, stacktrace) {
      Log.v(stacktrace);
      Log.v(e);
    }
    return null;
  }

  Future<ExploreJobListResponse> getExploreJobList(
      {int? indexNo, bool? doRefresh = false, String? resumeUrl}) async {
    try {
      final response = await homeProvider.getExploreJobList(
          indexNo: indexNo, doRefresh: doRefresh, resumeUrl: resumeUrl);
      if (response!.success) {
        Log.v("Domain Filter  List  DATA : ${response.body}");
        ExploreJobListResponse competitionData = ExploreJobListResponse();
        try {
          competitionData = ExploreJobListResponse.fromJson(response.body);
        } catch (e, stacktrace) {
          print('exception is $e and  $stacktrace');
        }
        return competitionData;
      } else {
        Log.v("====> ${response.body}");
        return ExploreJobListResponse();
      }
    } catch (e, stackTrace) {
      debugPrint('EXCEPTION IS $e and $stackTrace');
    }
    return ExploreJobListResponse();
  }

  Future<ExploreJobDetailsResponse?> getExploreJobDetails(String? jobId) async {
    final response = await homeProvider.getExploreJobDetails(jobId!);
    Log.v('response!.success ${response!.success}');
    try {
      if (response.success) {
        ExploreJobDetailsResponse resp =
            ExploreJobDetailsResponse.fromJson(response.body);
        return resp;
      } else {
        return ExploreJobDetailsResponse();
      }
    } catch (e, stacktrace) {
      print(stacktrace);
      print(e);
    }
    return null;
  }

  Future exploreApplyJob({int? jobId, String? jobType}) async {
    try {
      final response =
          await homeProvider.getApplyJobEntry(jobId: jobId, jobType: jobType);
      if (response?.body['status'] == 1) {
        return 1;
      } else {
        return response?.body['message'];
      }
    } catch (e) {
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }

  Future generateSimilarity({int? submissionId}) async {
    try {
      final response =
          await homeProvider.generateSimilarity(submissionId: submissionId);
      if (response == 1) {
        return response;
      } else {
        return response?.body['message'];
      }
    } catch (e) {
      Log.v("EXCEPTION  :  $e");
    }
    return null;
  }


  Future<DownloadAssessmentReportResponse?> downloadAssessmentReport({int? programId, int? contentId, int? userId}) async {
    final response = await homeProvider.downloadAssessmentReport(programId: programId, contentId: contentId, userId: userId);
    try {
      if (response!.success) {
        Log.v("SUCCESS DATA : ${json.encode(response.body)}");
        DownloadAssessmentReportResponse resp = DownloadAssessmentReportResponse.fromJson(response.body);
        return resp;
      } else {
        Log.v("====> ${response.body}");
        return DownloadAssessmentReportResponse();
      }
    } catch (e, stacktrace) {
      Log.v(e);
    }
    return null;
  }

  //add new
  Future<FeeAgreementResponse?> feeAgreement() async {
    final response = await homeProvider.feeAgreement();
    try {
      if (response!.success) {
        Log.v("SUCCESS DATA : ${json.encode(response.body)}");
        FeeAgreementResponse resp = FeeAgreementResponse.fromJson(response.body);
        return resp;
      } else {
        Log.v("====> ${response.body}");
        return FeeAgreementResponse();
      }
    } catch (e, stacktrace) {
      Log.v(e);
    }
    return null;
  }

  Future<AcceptFeeAgreementResponse?> AcceptFeeAgreement({int? isAccepted}) async {
    final response = await homeProvider.AcceptFeeAgreement(isAccepted: isAccepted);
    try {
      if (response!.success) {
        Log.v("SUCCESS DATA : ${json.encode(response.body)}");
        AcceptFeeAgreementResponse resp = AcceptFeeAgreementResponse.fromJson(response.body);
        return resp;
      } else {
        Log.v("====> ${response.body}");
        return AcceptFeeAgreementResponse();
      }
    } catch (e, stacktrace) {
      Log.v(e);
    }
    return null;
  }

}

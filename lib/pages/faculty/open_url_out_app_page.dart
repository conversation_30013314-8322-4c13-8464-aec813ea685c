import 'package:flutter/material.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:url_launcher/url_launcher.dart';

class OpenUrlOutAppPage extends StatefulWidget {
  final String? url;
  final String? title;
  const OpenUrlOutAppPage({super.key, this.url, this.title});

  @override
  State<OpenUrlOutAppPage> createState() => _OpenUrlOutAppPageState();
}

class _OpenUrlOutAppPageState extends State<OpenUrlOutAppPage> {
  //bool isLoading = true;
  // final _key = UniqueKey();

  Future<void> _launchURL(String strUrl) async {
    final Uri url = Uri.parse(strUrl);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.background,
      appBar: AppBar(
          elevation: 0.0,
          leading: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Icon(
                Icons.arrow_back,
                color: context.appColors.textBlack,
              )),
          title: Text(
            '${widget.title}'
                .replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
            style: Styles.semibold(
              color: context.appColors.textBlack,
            ),
          ),
          backgroundColor: context.appColors.surface),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            _launchURL(widget.url!);
          },
          child: Text('Open Link'),
        ),
      ),

      /*Stack(
        children: <Widget>[


          */ /*isLoading
              ? Center(
            child: CircularProgressIndicator(),
          )
              : Stack(),*/ /*
        ],
      ),*/
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/svg.dart';
import '../../local/pref/Preference.dart';
import '../../utils/styles.dart';
import '../auth_pages/new_language_screen.dart';
import '../auth_pages/terms_and_condition_page.dart';
import '../custom_pages/alert_widgets/alerts_widget.dart';
import '../custom_pages/custom_widgets/next_page_routing.dart';

class FacultyHomePage extends StatefulWidget {
  const FacultyHomePage({super.key});

  @override
  State<FacultyHomePage> createState() => _FacultyHomePageState();
}

class _FacultyHomePageState extends State<FacultyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: context.appColors.background,
        body: SingleChildScrollView(
          child: Container(
            margin: EdgeInsets.all(0),
            child: Column(
              children: [
                //TODO:Top Widget User info
                Container(
                  width: double.infinity,
                  color: context.appColors.primaryDark,
                  height: 130,
                  child: Container(
                    margin: EdgeInsets.only(left: 10.0, right: 10.0, top: 20.0),
                    //width: MediaQuery.of(context).size.width * 0.10,
                    //width: 200,
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/bxs_user_circle.svg',
                          height: 50,
                          width: 50,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: SizedBox(
                            width: 200,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    '${Preference.getString(Preference.FIRST_NAME)}',
                                    style: Styles.bold(
                                        color: context.appColors.textWhite,
                                        size: 16)),
                                Text('Active',
                                    style: Styles.regular(
                                        color: context.appColors.textWhite,
                                        size: 12)),
                              ],
                            ),
                          ),
                        ),
                        Spacer(),
                        InkWell(
                          onTap: () async {
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: tr('confirm_message'),
                                oKText: tr('ok'),
                                icon: 'assets/images/circle_alert_fill.svg',
                                onOkClick: () async {
                                  int? lanuageId = Preference.getInt(
                                          Preference.APP_LANGUAGE) ??
                                      1;
                                  String? appEnglishName = Preference.getString(
                                          Preference.APP_ENGLISH_NAME) ??
                                      'en';
                                  Preference.clearPref().then((value) async {
                                    Preference.setInt(
                                        Preference.APP_LANGUAGE, lanuageId);
                                    Preference.setString(
                                        Preference.APP_ENGLISH_NAME,
                                        appEnglishName);
                                    Navigator.pushAndRemoveUntil(
                                        context,
                                        NextPageRoute(SelectLanguage(
                                          showEdulystLogo: true,
                                        )),
                                        (route) => false);
                                  });
                                });
                          },
                          child: Icon(
                            Icons.logout,
                            color: context.appColors.primaryForeground,
                            size: 25,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                //TODO:Widget one
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Row(
                    children: [
                      Expanded(
                          child: GestureDetector(
                        onTap: () {
                          debugPrint(
                              Preference.getInt(Preference.USER_ID).toString());
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TermsAndCondition(
                                        url:
                                            'https://mecfuture.mec.edu.om/hris/myleave?user_id=${Preference.getInt(Preference.USER_ID)}',
                                        title: tr('my_leave'),
                                      ),
                                  maintainState: false));
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: 5.0),
                          height: 100,
                          decoration: BoxDecoration(
                              color: context.appColors.continueButton,
                              borderRadius: BorderRadius.circular(10)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.leave_bags_at_home,
                                color: context.appColors.primaryForeground,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Text(tr('my_leave'),
                                    style: Styles.regular(
                                        color:
                                            context.appColors.primaryForeground,
                                        size: 12)),
                              )
                            ],
                          ),
                        ),
                      )),
                      Expanded(
                          child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TermsAndCondition(
                                        url:
                                            'https://mecfuture.mec.edu.om/hris/my-profile?user_id=${Preference.getInt(Preference.USER_ID)}',
                                        title: tr('my_profile'),
                                      ),
                                  maintainState: false));
                        },
                        child: Container(
                          height: 100,
                          margin: EdgeInsets.only(left: 5.0),
                          decoration: BoxDecoration(
                              color: context.appColors.error,
                              borderRadius: BorderRadius.circular(10)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.account_circle_rounded,
                                color: context.appColors.primaryForeground,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Text(tr('my_profile'),
                                    style: Styles.regular(
                                        color:
                                            context.appColors.primaryForeground,
                                        size: 12)),
                              )
                            ],
                          ),
                        ),
                      )),
                    ],
                  ),
                ),

                //TODO:Widget Tow
                Padding(
                  padding: const EdgeInsets.only(left: 10.0, right: 10.0),
                  child: Row(
                    children: [
                      Expanded(
                          child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TermsAndCondition(
                                        url:
                                            'https://mecfuture.mec.edu.om/hris/attendancelogs?user_id=${Preference.getInt(Preference.USER_ID)}',
                                        title: tr('attendance_logs'),
                                      ),
                                  maintainState: false));
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: 5.0),
                          height: 100,
                          decoration: BoxDecoration(
                              color: context.appColors.primaryDark,
                              borderRadius: BorderRadius.circular(10)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.calendar_month,
                                color: context.appColors.primaryForeground,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Text(tr('attendance_logs'),
                                    style: Styles.regular(
                                        color:
                                            context.appColors.primaryForeground,
                                        size: 12)),
                              )
                            ],
                          ),
                        ),
                      )),
                      Expanded(
                          child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TermsAndCondition(
                                        url:
                                            'https://mecfuture.mec.edu.om/hris/scheduled-weekly-timetable?user_id=${Preference.getInt(Preference.USER_ID)}',
                                        title: tr('scheduled_weekly'),
                                      ),
                                  maintainState: false));
                        },
                        child: Container(
                          height: 100,
                          margin: EdgeInsets.only(left: 5.0),
                          decoration: BoxDecoration(
                              color: context.appColors.darkBlueButton,
                              borderRadius: BorderRadius.circular(10)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.access_time,
                                color: context.appColors.primaryForeground,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Text(tr('scheduled_weekly'),
                                    style: Styles.regular(
                                        color:
                                            context.appColors.primaryForeground,
                                        size: 12)),
                              )
                            ],
                          ),
                        ),
                      )),
                    ],
                  ),
                ),

                //TODO:Widget three
                Padding(
                  padding:
                      const EdgeInsets.only(left: 10.0, right: 10.0, top: 10.0),
                  child: Row(
                    children: [
                      Expanded(
                          child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TermsAndCondition(
                                        url:
                                            'https://mecfuture.mec.edu.om/hris/holidays?user_id=${Preference.getInt(Preference.USER_ID)}',
                                        title: tr('holidays'),
                                      ),
                                  maintainState: false));
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: 5.0),
                          height: 100,
                          decoration: BoxDecoration(
                              color: context.appColors.green,
                              borderRadius: BorderRadius.circular(10)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.holiday_village_outlined,
                                color: context.appColors.primaryForeground,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Text(tr('holidays'),
                                    style: Styles.regular(
                                        color:
                                            context.appColors.primaryForeground,
                                        size: 12)),
                              )
                            ],
                          ),
                        ),
                      )),
                      Expanded(
                          child: GestureDetector(
                        onTap: () {},
                        child: Container(
                          height: 100,
                          margin: EdgeInsets.only(left: 5.0),
                        ),
                      )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}

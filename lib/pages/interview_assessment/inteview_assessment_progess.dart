import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/pages/interview_assessment/model/question.dart';
import 'package:masterg/pages/interview_assessment/question_attempt_review.dart';
import 'package:masterg/utils/utility.dart';
import 'interview_provider.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class AssessmentProgress extends StatefulWidget {
  final InterviewAssessmentProvider provider;
  const AssessmentProgress({Key? key, required this.provider})
      : super(key: key);

  @override
  _AssessmentProgressState createState() => _AssessmentProgressState();
}

class _AssessmentProgressState extends State<AssessmentProgress> {
  @override
  void initState() {
    if (widget.provider.isReview == false)
      widget.provider.stopWatchTimer.secondTime.listen((value) {
        if (!widget.provider.stopWatchTimer.isRunning) return;
        int secondLeft = max(
            0,
            widget.provider.totalSecond -
                widget.provider.stopWatchTimer.secondTime.value);
        int min = secondLeft ~/ 60;
        int sec = secondLeft - (min * 60);
        if (min <= 0 && sec <= 0) {
          widget.provider.stopWatchTimer.dispose();
          widget.provider.closeAllDialog();
          widget.provider.pauseVideo();

          bool isRTL = Utility().isRTL(context);
          showDialog(
              context: context,
              useSafeArea: false,
              builder: (context) => QuestionAttemptReview(
                    isRTL: isRTL,
                    title: widget.provider.title,
                    interviewAssessmentProvider: widget.provider,
                    disableback: true,
                  )).then((value) {
            if (value == true) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text('assesssment_submitted').tr(),
              ));
            }
          });
        }
      });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final interviewAssessmentProvider = widget.provider;
    int totalSecond = interviewAssessmentProvider.totalSecond;

    return Container(
      color: Color(0xFFF9FBFC),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '${tr('no_of_questions')} - ',
                      style: Styles.regular(size: 14, color: Color(0xff727C95)),
                    ),
                    TextSpan(
                      text:
                          '${interviewAssessmentProvider.currentQuestionIndex + 1}',
                      style: Styles.bold(size: 14, color: Colors.black),
                    ),
                    TextSpan(
                      text: '/${interviewAssessmentProvider.questions.length}',
                      style: Styles.regular(size: 14, color: Color(0xff727C95)),
                    ),
                  ],
                ),
              ),

              // Text(
              //   '${tr('no_of_questions')} - ${interviewAssessmentProvider.currentQuestionIndex + 1}/${interviewAssessmentProvider.questions.length} ',
              //   style: Styles.regular(size: 12, color: Color(0xff727C95)),
              // ),
              if (!widget.provider.isReview)
                Row(
                  children: [
                    SvgPicture.asset('assets/images/time_left.svg'),
                    SizedBox(width: 4),
                    StreamBuilder<int>(
                        stream: interviewAssessmentProvider
                            .stopWatchTimer.secondTime,
                        builder: ((context, snapshot) {
                          if (!snapshot.hasData) return SizedBox();
                          int secondLeft = max(0, totalSecond - snapshot.data!);
                          int min = secondLeft ~/ 60;
                          int sec = secondLeft - (min * 60);
                          String formattedMin = min.toString().padLeft(2, '0');
                          String formattedSec = sec.toString().padLeft(2, '0');

                          return Text(
                            '$formattedMin:$formattedSec ${tr('min')}',
                            // '${max(0, min)}:${max(0, sec)} ${tr('min')}',
                            style: Styles.semibold(
                                size: 20,
                                color: secondLeft <= 120
                                    ? ColorConstants.RED
                                    : Color(0xff00BC4B)),
                          );
                        })),
                  ],
                )
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: Colors.white,
                  width: 2.0,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white,
                    spreadRadius: 3,
                    blurRadius: 5,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              height: 40,
              child: ListView.separated(
                  separatorBuilder: (BuildContext context, int index) {
                    return SizedBox(width: 0);
                  },
                  controller: widget.provider.scrollController,
                  itemCount: interviewAssessmentProvider.questions.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        bool allowQuickView = true;

                        if (interviewAssessmentProvider.isReview ||
                            allowQuickView) {
                          interviewAssessmentProvider.reviewJump(index: index);
                          return;
                        }
                        if (index <=
                            interviewAssessmentProvider.lastVisitedIndex)
                          interviewAssessmentProvider.moveToIndex(
                              questionIndex: index);
                      },
                      child: Container(
                          height: 20,
                          width: 26,
                          margin: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                              border: interviewAssessmentProvider
                                          .currentQuestionIndex ==
                                      index
                                  ? Border.all(
                                      color: ColorConstants()
                                          .primaryColorbtnAlways())
                                  : null,
                              color: interviewAssessmentProvider
                                          .lastVisitedIndex ==
                                      index
                                  ? ColorConstants().primaryColorbtnAlways()
                                  : null),
                          child: Center(
                              child: Text('${index + 1}',
                                  style: Styles.regular(
                                    size: 12,
                                    color: interviewAssessmentProvider
                                                .lastVisitedIndex ==
                                            index
                                        ? ColorConstants.WHITE
                                        : interviewAssessmentProvider
                                                    .questions[index]
                                                    .questionStatus ==
                                                QuestionStatus.pending
                                            ? Color(0xffA1A9C0)
                                            : interviewAssessmentProvider
                                                        .questions[index]
                                                        .questionStatus ==
                                                    QuestionStatus.done
                                                ? Color(0xff00BC4B)
                                                : Color(0xffFF7B17),
                                  )))),
                    );
                  }),
            ),
          ),
        ],
      ),
    );
  }
}

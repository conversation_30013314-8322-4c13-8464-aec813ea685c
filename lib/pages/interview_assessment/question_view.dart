import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/pages/interview_assessment/video_play.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';

import '../../data/models/response/home_response/test_attempt_response.dart';
import '../speech_assessment/audio_player.dart';
import 'interview_provider.dart';

enum FileType {
  Image,
  Video,
  Document,
  Audio,
}

FileType? getFileType(String filePath) {
  final List<String> parts = filePath.split('.');
  if (parts.length > 1) {
    final String extension = parts.last;
    switch (extension.toLowerCase()) {
      case "jpg":
      case "jpeg":
      case "png":
        return FileType.Image;
      case "mp4":
      case "webm":
      case "mov":
        return FileType.Video;
      case "pdf":
      case "doc":
      case "txt":
        return FileType.Document;
      case "mp3":
      case "wav":
      case "flac":
      case "m4a":
        return FileType.Audio;
      default:
    }
  }
  return null;
}

class QuestionView extends StatefulWidget {
  final TestAttemptBean currentQuestion;
  final int index;
  const QuestionView(
      {Key? key, required this.currentQuestion, required this.index})
      : super(key: key);

  @override
  _QuestionViewState createState() => _QuestionViewState();
}

class _QuestionViewState extends State<QuestionView> {
  // VideoPlayerController? questionVideoController;

  @override
  void dispose() {
    log("dispose is called");

    super.dispose();
  }

  @override
  void initState() {
    // setVolumne();

    super.initState();
  }

  // void setVolumne() {
  //   log("set the volumne");
  //   final AudioContext audioContext = AudioContext(
  //     iOS: AudioContextIOS(
  //       category: AVAudioSessionCategory.ambient,
  //       options: [
  //         AVAudioSessionOptions.defaultToSpeaker,
  //         AVAudioSessionOptions.mixWithOthers,
  //       ],
  //     ),
  //     android: AudioContextAndroid(
  //       isSpeakerphoneOn: false,
  //       stayAwake: false,
  //       contentType: AndroidContentType.sonification,
  //       usageType: AndroidUsageType.assistanceSonification,
  //       audioFocus: AndroidAudioFocus.none,
  //     ),
  //   );
  //   AudioPlayer.global.setGlobalAudioContext(audioContext);
  // }

  @override
  Widget build(BuildContext context) {
    final interviewAssessmentProvider =
        context.read<InterviewAssessmentProvider>();
    // BuildContext? dialogContext;
    // TestAttemptBean currentQuestion = interviewAssessmentProvider
    //     .questionList[interviewAssessmentProvider.currentQuestionIndex];

    return Container(
      color: Colors.white,
      width: width(context) * 0.9,
      padding: const EdgeInsets.symmetric(horizontal: 6),
      child: SingleChildScrollView(
        child: Column(

            // shrinkWrap: true,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      children: [
                        Text(
                          'Q',
                          style:
                              Styles.bold(size: 16, color: Color(0xff222631)),
                        ),
                        Text(
                          '${widget.index + 1}.',
                          style:
                              Styles.bold(size: 16, color: Color(0xff222631)),
                        ),
                      ],
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    // Text('${interviewAssessmentProvider.questions.length}\n'),
                    // Text(
                    //     '${interviewAssessmentProvider.questions[0].userUploadedFileUrl.first.split('/').last}'),
                    // Text(() {
                    //   String? files;
                    //   int index = 1;
                    //   interviewAssessmentProvider.questions.forEach((element) {
                    //     Log.v(
                    //         "my fiel are ${interviewAssessmentProvider.questions[1].userAnswerTxt}");
                    //     files = '$files\t$index ${element.userAnswerTxt} \n';
                    //     index++;
                    //   });
                    //   return ' $files';
                    // }()),
                    SizedBox(
                        width: width(context) * 0.8,
                        child: Text(
                          '${widget.currentQuestion.question?.question}',
                          style: Styles.semibold(
                              size: 14, color: Color(0xff222631)),
                          maxLines: 10,
                        )),
                  ],
                ),
              ),
              if (widget.currentQuestion.question?.questionImage?.length != 0)
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: getFileType(
                                '${widget.currentQuestion.question?.questionImage?.first}') ==
                            FileType.Video
                        ? VideoPlay(
                            videoUrl:
                                '${widget.currentQuestion.question?.questionImage?.first}',
                          )
                        : getFileType(
                                    '${widget.currentQuestion.question?.questionImage?.first}') ==
                                FileType.Audio
                            ? InkWell(
                                onTap: () {
                                  showDialog(
                                          context: context,
                                          builder: (BuildContext context) {
                                            // dialogContext = context;
                                            interviewAssessmentProvider
                                                .addContext(context);
                                            return AlertDialog(
                                                backgroundColor:
                                                    Colors.transparent,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                10.0))),
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 0),
                                                content: Container(
                                                    width: 300.0,
                                                    child: Stack(
                                                      children: [
                                                        Column(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .stretch,
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: <Widget>[
                                                            Center(
                                                                child:
                                                                    Container(
                                                                        decoration: BoxDecoration(
                                                                            color: Colors
                                                                                .white,
                                                                            borderRadius: BorderRadius.circular(
                                                                                10)),
                                                                        margin: const EdgeInsets
                                                                            .all(
                                                                            10),
                                                                        child:
                                                                            AudioUrlPlayer(
                                                                          source:
                                                                              Uri.encodeFull('${widget.currentQuestion.question?.questionImage?.first}'),
                                                                          isLocalFile:
                                                                              false,
                                                                        ))),
                                                          ],
                                                        ),
                                                        Positioned(
                                                          right: 0,
                                                          child: Column(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .end,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .end,
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: <Widget>[
                                                              GestureDetector(
                                                                onTap: () {
                                                                  Navigator.pop(
                                                                      context);
                                                                },
                                                                child:
                                                                    Container(
                                                                  height: 30,
                                                                  width: 30,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: ColorConstants
                                                                        .WHITE,
                                                                    shape: BoxShape
                                                                        .circle,
                                                                  ),
                                                                  child: Icon(
                                                                    Icons
                                                                        .close_rounded,
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    )));
                                          })
                                      .then((_) => interviewAssessmentProvider
                                          .removeLastContext());
                                },
                                child: Container(
                                    margin: const EdgeInsets.all(10),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 4, vertical: 4),
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            color: Color(0xffA1A9C0)),
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    width: double.infinity,
                                    child: Row(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                              color: ColorConstants()
                                                  .primaryColorbtnAlways()
                                                  .withValues(
                                                    alpha: 0.2,
                                                  ),
                                              borderRadius:
                                                  BorderRadius.circular(6)),
                                          padding: const EdgeInsets.all(6),
                                          child: SvgPicture.asset(
                                            'assets/images/audio.svg',
                                            width: 30,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        SizedBox(
                                          width: width(context) * 0.75,
                                          child: Text(
                                            '${widget.currentQuestion.question?.questionImage?.first}'
                                                .split('/')
                                                .last,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        )
                                      ],
                                    )),
                              )
                            : getFileType(
                                        '${widget.currentQuestion.question?.questionImage?.first}') ==
                                    FileType.Document
                                ? InkWell(
                                    onTap: () {
                                      bool isRTL = Utility().isRTL(context);
                                      showDialog(
                                              context: context,
                                              useSafeArea: false,
                                              builder: (context) {
                                                interviewAssessmentProvider
                                                    .addContext(context);
                                                return Directionality(
                                                  textDirection:
                                                      Utility.setDirection(
                                                          isRTL),
                                                  child: Scaffold(
                                                    appBar: AppBar(
                                                      elevation: 0,
                                                      backgroundColor:
                                                          ColorConstants.WHITE,
                                                      iconTheme: IconThemeData(
                                                          color: ColorConstants
                                                              .BLACK),
                                                    ),
                                                    body: PDF(
                                                      enableSwipe: true,
                                                      swipeHorizontal: false,
                                                      pageFling: false,
                                                      fitPolicy: FitPolicy.BOTH,
                                                      onError: (error) {
                                                        print(error.toString());
                                                      },
                                                      onPageError:
                                                          (page, error) {
                                                        print(
                                                            '$page: ${error.toString()}');
                                                      },
                                                    ).cachedFromUrl(
                                                        '${widget.currentQuestion.question?.questionImage?.first}'),
                                                  ),
                                                );
                                              })
                                          .then((_) =>
                                              interviewAssessmentProvider
                                                  .removeLastContext());
                                    },
                                    child: Container(
                                        margin: const EdgeInsets.all(10),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4, vertical: 4),
                                        decoration: BoxDecoration(
                                            border: Border.all(
                                                color: Color(0xffA1A9C0)),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        width: double.infinity,
                                        child: Row(
                                          children: [
                                            Container(
                                              decoration: BoxDecoration(
                                                  color: ColorConstants()
                                                      .primaryColorbtnAlways()
                                                      .withValues(
                                                        alpha: 0.2,
                                                      ),
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                              padding: const EdgeInsets.all(6),
                                              child: SvgPicture.asset(
                                                'assets/images/file.svg',
                                                width: 30,
                                              ),
                                            ),
                                            SizedBox(
                                              width: 10,
                                            ),
                                            SizedBox(
                                              width: width(context) * 0.75,
                                              child: Text(
                                                '${widget.currentQuestion.question?.questionImage?.first}'
                                                    .split('/')
                                                    .last,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            )
                                          ],
                                        )),
                                  )
                                // ? SizedBox(
                                //     height: height(context) * 0.7,
                                //     child: ViewPdfPage(
                                //       path:
                                //           '${widget.currentQuestion.question?.questionImage?.first}',
                                //       hideAppbar: true,
                                //     ))
                                : InkWell(
                                    onTap: () {
                                      bool isRTL = Utility().isRTL(context);
                                      showDialog(
                                              context: context,
                                              builder: (context) {
                                                interviewAssessmentProvider
                                                    .addContext(context);
                                                return Directionality(
                                                  textDirection:
                                                      Utility.setDirection(
                                                          isRTL),
                                                  child: Scaffold(
                                                    appBar: AppBar(
                                                      elevation: 0,
                                                      backgroundColor:
                                                          ColorConstants.WHITE,
                                                      iconTheme: IconThemeData(
                                                          color: ColorConstants
                                                              .BLACK),
                                                    ),
                                                    body: InteractiveViewer(
                                                      panEnabled: true,
                                                      minScale: 1,
                                                      maxScale: 4,
                                                      child: Center(
                                                        child: CachedNetworkImage(
                                                            fit:
                                                                BoxFit.fitWidth,
                                                            imageUrl:
                                                                '${widget.currentQuestion.question?.questionImage?.first}'),
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              })
                                          .then((_) =>
                                              interviewAssessmentProvider
                                                  .removeLastContext());
                                    },
                                    child: CachedNetworkImage(
                                        imageUrl:
                                            '${widget.currentQuestion.question?.questionImage?.first}'),
                                  ),
                  ),
                ),
            ]),
      ),
    );
  }
}

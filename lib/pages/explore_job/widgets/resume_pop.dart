import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/pages/explore_job/explore_job_proivder.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:provider/provider.dart';

import '../../custom_pages/custom_widgets/NextPageRouting.dart';
import '../../user_profile_page/portfolio_create_form/view_resume.dart';
import '../../video_resume/video_resume.dart';

class ResumePop extends StatelessWidget {
  const ResumePop({super.key});

  @override
  Widget build(BuildContext context) {
    ExploreJobProvider jp = Provider.of<ExploreJobProvider>(context);
    return Container(
      padding: const EdgeInsets.only(bottom: 30, top: 20, left: 18, right: 18),
      child: Column(children: [
        ResumePopCard(
          onClick: () {
            if (jp.resumeDetail != null) {
              Navigator.push(
                  context,
                  NextPageRoute(ViewResume(
                    resumUrl: jp.resumeDetail?.url,
                    resumeId: jp.resumeDetail?.id,
                    viewOnly: false,
                  ))).then((value) => Navigator.pop(context));
            }
          },
          imageSvg: 'assets/images/resume.svg',
          text: 'view_resume',
        ),
        Divider(),
        ResumePopCard(
          onClick: () {
            if (jp.fromVideoResume == true) {
              Navigator.pop(context);
              Navigator.pop(context);
            } else {
              Navigator.push(context, NextPageRoute(VideoResume()))
                  .then((value) => Navigator.pop(context));
            }
          },
          imageSvg: 'assets/images/ar_on_you.svg',
          text: 'video_resume',
        ),
      ]),
    );
  }
}

class ResumePopCard extends StatelessWidget {
  final Function onClick;
  final String imageSvg;
  final String text;
  const ResumePopCard(
      {Key? key,
      required this.onClick,
      required this.imageSvg,
      required this.text})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onClick();
      },
      child: Row(
        children: [
          SvgPicture.asset(
            '$imageSvg',
            colorFilter:
                ColorFilter.mode(ColorConstants.BLACK, BlendMode.srcIn),
            height: 22,
            width: 22,
          ),
          SizedBox(width: 10),
          Text('$text').tr(),
          Spacer(),
          Icon(Icons.arrow_forward_ios_sharp)
        ],
      ),
    );
  }
}

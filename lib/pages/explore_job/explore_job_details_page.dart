import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/utility.dart';

import 'package:shimmer/shimmer.dart';

import '../../blocs/bloc_manager.dart';
import '../../blocs/home_bloc.dart';
import '../../data/api/api_service.dart';
import '../../data/models/response/home_response/explore_job_details_response.dart';
import '../../utils/Log.dart';
import '../../utils/Styles.dart';
import '../../utils/resource/colors.dart';
import '../../utils/resource/size_constants.dart';

class ExploreJobDetailsPage extends StatefulWidget {
  final String? id;
  final String? skillMatching;

  ExploreJobDetailsPage({Key? key, this.id, this.skillMatching})
      : super(key: key);

  @override
  State<ExploreJobDetailsPage> createState() => _ExploreJobDetailsPageState();
}

class _ExploreJobDetailsPageState extends State<ExploreJobDetailsPage> {
  bool? competitionDetailLoading = true;
  ExploreJobDetailsResponse? contentList;
  int? applied = 0;
  List<dynamic> emptyCheck = [null, ''];

  @override
  void initState() {
    print('widget.id ${widget.id}');
    getExploreJobDetails(widget.id);
    super.initState();
  }

  void getExploreJobDetails(String? jobId) {
    BlocProvider.of<HomeBloc>(context)
        .add(ExploreJobDetailsEvent(jobId: jobId));
  }

  void exploreJobApply(int? jobId, String? jobType) {
    BlocProvider.of<HomeBloc>(context)
        .add(ExploreApplyJobEvent(jobId: jobId, jobType: jobType));
  }

  void handleExploreJobDetailsState(ExploreJobDetailsState state) {
    var jobState = state;
    setState(() {
      switch (jobState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          competitionDetailLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Competition Content List State....................");
          contentList = jobState.response;

          print('contentList ==');
          competitionDetailLoading = false;
          /*if (applied != 0) {
            Get.rawSnackbar(
              messageText: Text(
                'application_submitted',
                style: Styles.regular(size: 14, color: ColorConstants.WHITE),
              ).tr(),
              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: ColorConstants.BLACK,
              borderRadius: 4,
              duration: Duration(seconds: 3),
              boxShadows: [
                BoxShadow(
                    color: Color(0xff898989).withValues(alpha: 0.1),
                    offset: Offset(0, 4.0),
                    blurRadius: 11)
              ],
            );
          }*/
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Competition Content ..........................${jobState.response?.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'competition_content', parameters: {
            "ERROR": jobState.response?.error ?? '',
          });

          competitionDetailLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleExploreJobApplyState(ExploreApplyJobState state) {
    var jobState = state;
    //setState(() {
    switch (jobState.apiState) {
      case ApiStatus.LOADING:
        Log.v("Loading....................");
        break;
      case ApiStatus.SUCCESS:
        Log.v(
            "handle ExploreJobApply State ..........................${jobState.error}");
        /*if (applied != 0) {
            Get.rawSnackbar(
              messageText: Text(
                'application_submitted',
                style: Styles.regular(size: 14, color: ColorConstants.WHITE),
              ).tr(),
              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: ColorConstants.BLACK,
              borderRadius: 4,
              duration: Duration(seconds: 3),
              boxShadows: [
                BoxShadow(
                    color: Color(0xff898989).withValues(alpha: 0.1),
                    offset: Offset(0, 4.0),
                    blurRadius: 11)
              ],
            );
          }*/
        break;
      case ApiStatus.ERROR:
        Log.v(
            "Error Competition Content ..........................${jobState.error}");
        FirebaseAnalytics.instance
            .logEvent(name: 'explore_job_details', parameters: {
          "ERROR": '${jobState.error}',
        });

        break;
      case ApiStatus.INITIAL:
        break;
    }
    //});
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is ExploreJobDetailsState)
            handleExploreJobDetailsState(state);

          if (state is ExploreApplyJobState) handleExploreJobApplyState(state);
        },
        child: Scaffold(
          appBar: AppBar(
            iconTheme: IconThemeData(
              color: Colors.black,
            ),
            elevation: 0.0,
            backgroundColor: ColorConstants.WHITE,
            title: Text(
              '',
              style: TextStyle(color: Colors.black),
            ),
          ),
          backgroundColor: ColorConstants.WHITE,
          body: _makeBody(),
        ),
      ),
    );
  }

  Widget _makeBody() {
    return SingleChildScrollView(
      child: Container(
        margin: EdgeInsets.only(bottom: SizeConstants.JOB_BOTTOM_SCREEN_MGN),
        width: MediaQuery.of(context).size.width,
        child: competitionDetailLoading == true
            ? BlankPage()
            : _jobDetailsWidget(),
      ),
    );
  }

  Widget _jobDetailsWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 3,
                  child: Container(
                      padding: EdgeInsets.only(
                        right: 10.0,
                      ),
                      child: contentList?.data?.resArr?.logoUrl != null
                          ? Image.network(
                              '${contentList?.data?.resArr?.logoUrl}',
                            )
                          : Image.asset(
                              'assets/images/pb_2.png',
                            )),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(left: 5.0, right: 5.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${contentList?.data?.resArr?.jobRole ?? ''}',
                            style: Styles.bold(
                                size: 14,
                                color:
                                    ColorConstants().primaryColorbtnAlways())),
                        SizedBox(height: 6),
                        Text('${contentList?.data?.resArr?.company ?? ''}',
                            style: Styles.bold(
                                size: 12, color: ColorConstants.BLACK)),
                        if (contentList?.data?.resArr?.jobPostedDateTimestamp !=
                            null) ...[
                          SizedBox(height: 6),
                          Row(
                            children: [
                              Text('posted_on',
                                      style: Styles.regular(
                                          size: 12,
                                          color: ColorConstants.GREY_3))
                                  .tr(),
                              Text(
                                  ': ${'${contentList?.data?.resArr?.jobPostedDateTimestamp != null ? Utility.convertDateFromMillis(contentList!.data!.resArr!.jobPostedDateTimestamp!, Strings.REQUIRED_DATE_DD_MMM_YYYY) : ''}'}',
                                  style: Styles.regular(
                                      size: 12, color: ColorConstants.GREY_3)),
                            ],
                          )
                        ]
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 10),
          Row(
            children: [
              Icon(Icons.work_outline, size: 16, color: ColorConstants.GREY_3),
              SizedBox(width: 8),
              Text('${tr('exp')}: ',
                  style:
                      Styles.regular(size: 12, color: ColorConstants.GREY_3)),
              Text(
                  '${contentList?.data?.resArr?.minExperience != null ? contentList?.data?.resArr?.minExperience : "0"}' +
                      '-${contentList?.data?.resArr?.maxExperience != null ? contentList?.data?.resArr?.maxExperience : "0"} ${tr('yrs')} ',
                  style:
                      Styles.regular(size: 12, color: ColorConstants.GREY_3)),
            ],
          ),
          SizedBox(height: 10),
          contentList?.data?.resArr?.location != null
              ? Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 16,
                      color: ColorConstants.GREY_3,
                    ),
                    SizedBox(width: 8),
                    SizedBox(
                      width: 100,
                      child: Text(
                        '${contentList?.data?.resArr?.location}',
                        style: Styles.regular(
                            size: 12, color: ColorConstants.GREY_3),
                        overflow: TextOverflow.visible,
                        maxLines: 1,
                        softWrap: false,
                      ),
                    ),
                  ],
                )
              : SizedBox(),
          SizedBox(height: 10),
          contentList?.data?.resArr?.vacancies != null
              ? Row(
                  children: [
                    Icon(Icons.group, size: 16, color: ColorConstants.GREY_3),
                    SizedBox(width: 8),
                    Text('${tr('Vacancies')}: ',
                        style: Styles.regular(
                            size: 12, color: ColorConstants.GREY_3)),
                    Text('${contentList?.data?.resArr?.vacancies}',
                        style: Styles.regular(
                            size: 12, color: ColorConstants.GREY_3)),
                  ],
                )
              : SizedBox(),
          SizedBox(height: 10),
          if (widget.skillMatching != '')
            Row(
              children: [
                Icon(Icons.checklist, size: 16, color: ColorConstants.GREY_3),
                SizedBox(width: 8),
                RichText(
                    text: TextSpan(children: [
                  TextSpan(
                      text: widget.skillMatching,
                      // text: '5 out of 5 skills',
                      style: Styles.textExtraBoldUnderline(size: 12)),
                  TextSpan(
                      text: ' ',
                      style: Styles.textExtraBoldUnderline(size: 12)),
                  TextSpan(
                      text: '${tr('from_your_profile_matching_jp')}',
                      style: Styles.regular(
                          size: 12, color: ColorConstants.GREY_3)),
                ]))
              ],
            ),
          InkWell(
            onTap: () {
              FirebaseAnalytics.instance.logEvent(
                  name: 'careers_details_page_job_apply',
                  parameters: {
                    "job_name": contentList?.data?.resArr?.jobRole ?? '',
                  });

              if (contentList?.data?.resArr?.isApplied == 0) {
                if (contentList?.data?.resArr?.jdUrl == 'company') {
                  exploreJobApply(contentList?.data?.resArr?.id, 'int');
                } else {
                  exploreJobApply(contentList?.data?.resArr?.id, 'ext');
                }
                applied = 1;
                _onLoadingForJob();

                this.setState(() {
                  contentList?.data?.resArr?.status =
                      tr('application_under_process');
                  contentList?.data?.resArr?.isApplied = 1;
                });
              } else {
                //job all ready apply
              }
            },
            child: Container(
              height: 40,
              margin: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                color: contentList?.data?.resArr?.isApplied == 0
                    ? ColorConstants().primaryColorbtnAlways()
                    : ColorConstants.WHITE,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Align(
                alignment: Alignment.center,
                child: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (contentList?.data?.resArr?.isApplied == 1) ...[
                        Icon(
                          Icons.check,
                          color: ColorConstants.SELECTED_GREEN,
                          size: 20,
                        ),
                        SizedBox(width: 10),
                      ],
                      Text(
                        contentList?.data?.resArr?.isApplied == 0
                            ? 'i_am_interested'
                            : 'interested',
                        textAlign: TextAlign.center,
                        style: Styles.bold(
                          size: 13,
                          color: contentList?.data?.resArr?.isApplied == 0
                              ? ColorConstants.WHITE
                              : ColorConstants.SELECTED_GREEN,
                        ),
                      ).tr()
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 10),
          Transform.scale(
            scale: 2,
            child: Container(
              width: double.infinity,
              height: 6,
              color: ColorConstants.BG_GREY,
            ),
          ),
          if (!emptyCheck
              .contains(contentList?.data?.resArr?.sectorIndustryDomain)) ...[
            SizedBox(height: 16),
            Text(
              'domain/industry',
              style: Styles.regular(color: ColorConstants.GREY_3, size: 12),
            ).tr(),
            Text(
              '${contentList?.data?.resArr?.sectorIndustryDomain}',
              style: Styles.semibold(size: 14),
            ),
          ],
          if (!emptyCheck.contains(contentList?.data?.resArr?.education)) ...[
            SizedBox(height: 16),
            Text(
              'qualification',
              style: Styles.regular(color: ColorConstants.GREY_3, size: 12),
            ).tr(),
            Text(
              '${contentList?.data?.resArr?.education}',
              style: Styles.semibold(size: 14),
            ),
          ],
          if (!emptyCheck.contains(contentList?.data?.resArr?.nationality)) ...[
            SizedBox(height: 16),
            Text(
              'nationality',
              style: Styles.regular(color: ColorConstants.GREY_3, size: 12),
            ).tr(),
            Text(
              '${contentList?.data?.resArr?.nationality ?? ''}',
              style: Styles.semibold(size: 14),
            ),
          ],
          if (!emptyCheck.contains(contentList?.data?.resArr?.description)) ...[
            SizedBox(height: 30),
            Transform.scale(
              scale: 1.1,
              child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
                  color: ColorConstants.BG_GREY,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'job_description',
                        style: Styles.semibold(size: 12.5),
                      ).tr(),
                      SizedBox(height: 6),
                      Text(
                        '${contentList?.data?.resArr?.description}',
                        style: Styles.regular(
                            size: 11.5,
                            color: ColorConstants.GREY_3,
                            lineHeight: 2),
                      )
                    ],
                  )),
            )
          ]
        ],
      ),
    );
  }

  void _onLoadingForJob() {
    BuildContext? dialogContext;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        dialogContext = context;
        return Dialog(
          child: Container(
            padding: EdgeInsets.only(left: 20, right: 10),
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: new Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                new CircularProgressIndicator(
                  color: Colors.blue,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: new Text("${tr('job_apply')}..."),
                ),
              ],
            ),
          ),
        );
      },
    );
    new Future.delayed(new Duration(seconds: 2), () {
      // Navigator.pop(context); //pop dialog
      Navigator.pop(dialogContext!);
    });
  }
}

extension on String {
  // String capital() {
  //   return this[0].toUpperCase() + this.substring(1);
  // }
}

class BlankPage extends StatelessWidget {
  const BlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 230,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 180,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 190,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Container(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 30.0, left: 15),
              child: Shimmer.fromColors(
                baseColor: Color(0xffe6e4e6),
                highlightColor: Color(0xffeaf0f3),
                child: Container(
                    height: 13,
                    margin: EdgeInsets.only(left: 2),
                    width: 180,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5.0, left: 15, right: 15.0),
              child: Shimmer.fromColors(
                baseColor: Color(0xffe6e4e6),
                highlightColor: Color(0xffeaf0f3),
                child: Container(
                    height: 50,
                    margin: EdgeInsets.only(left: 2),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    )),
              ),
            ),
          ],
        )),
        Container(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 30.0, left: 15),
              child: Shimmer.fromColors(
                baseColor: Color(0xffe6e4e6),
                highlightColor: Color(0xffeaf0f3),
                child: Container(
                    height: 13,
                    margin: EdgeInsets.only(left: 2),
                    width: 180,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5.0, left: 15, right: 15.0),
              child: Shimmer.fromColors(
                baseColor: Color(0xffe6e4e6),
                highlightColor: Color(0xffeaf0f3),
                child: Container(
                    height: 70,
                    margin: EdgeInsets.only(left: 2),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    )),
              ),
            ),
          ],
        )),
        Container(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 30.0, left: 15),
              child: Shimmer.fromColors(
                baseColor: Color(0xffe6e4e6),
                highlightColor: Color(0xffeaf0f3),
                child: Container(
                    height: 13,
                    margin: EdgeInsets.only(left: 2),
                    width: 180,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5.0, left: 15, right: 15.0),
              child: Shimmer.fromColors(
                baseColor: Color(0xffe6e4e6),
                highlightColor: Color(0xffeaf0f3),
                child: Container(
                    height: 90,
                    margin: EdgeInsets.only(left: 2),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    )),
              ),
            ),
          ],
        )),
      ],
    );
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:masterg/utils/utility.dart';

import '../../data/models/response/home_response/ExploreJobListResponse.dart';
import '../../data/models/response/home_response/new_portfolio_response.dart';

class ExploreJobProvider extends ChangeNotifier {
  List<dynamic> countryFilterList = [];
  List<dynamic> experienceFilterList = [];
  List<int> matchingLevelFilterList = [];
  Resume? resumeDetail;
  List<ExploreJob>? allJobs = [];
  List<ExploreJob>? filterJobs = [];
  double popHeight = 0.0;
  int pageIndex = 0;
  bool fromVideoResume = false;

  //applied filter list
  List<dynamic> countryFilterListApplied = [];
  List<dynamic> experienceFilterListApplied = [];
  List<int> matchingFilterListApplied = [];

  // List<dynamic> countryFilterFinal = [];
  // List<dynamic> experienceFilterFinal = [];
  // List<int> matchingFilterFinal = [];

  bool isVerified = false;

  bool loading = false;
  void updateFromVideoResume({required bool fromVideoResume}) {
    this.fromVideoResume = fromVideoResume;
    notifyListeners();
  }

  void updateResumeDetail({required Resume? data}) {
    if (data == null) return;
    this.resumeDetail = data;
    notifyListeners();
  }

  void resetResume() {
    this.resumeDetail = null;
    notifyListeners();
  }

  void updateSkillMatch({required String jobID, required int skillID}) {
    this
        .allJobs
        ?.firstWhere((element) => element.jobId == jobID)
        .skills
        ?.firstWhere((element) => element.id == skillID)
        .userMatch = 1;

    this
        .filterJobs
        ?.firstWhere((element) => element.jobId == jobID)
        .skills
        ?.firstWhere((element) => element.id == skillID)
        .userMatch = 1;

    notifyListeners();
  }

  void reset() {
    countryFilterListApplied = [];
    experienceFilterListApplied = [];
    matchingFilterListApplied = [];
    this.allJobs = [];
    this.filterJobs = [];
    pageIndex = 0;
    notifyListeners();
  }

  void incrpageIndex() {
    this.pageIndex++;
    notifyListeners();
  }

  void resetFilter() {
    countryFilterListApplied = [];
    experienceFilterListApplied = [];
    matchingFilterListApplied = [];
    getFilteredList();
    notifyListeners();
  }

  void updatePopHeight({required double popHeight}) {
    this.popHeight = popHeight;
    notifyListeners();
  }

  void addAppliedCountryFilter({required String country}) {
    this.countryFilterListApplied.add(country);
    notifyListeners();
    getFilteredList();
  }

  void resetAppliedCountryFilter() {
    this.countryFilterListApplied = [];
    notifyListeners();
    getFilteredList();
  }

  void removeAppliedCountryFilter({required String country}) {
    this.countryFilterListApplied.remove(country);
    notifyListeners();
    getFilteredList();
  }

  void addAppliedExperienceFilter({required experience}) {
    this.experienceFilterListApplied.add(experience);
    notifyListeners();
    getFilteredList();
  }

  void resetAppliedExperienceFilter() {
    this.experienceFilterListApplied = [];
    notifyListeners();
    getFilteredList();
  }

  void removeAppliedExperienceFilter({required String experience}) {
    this.experienceFilterListApplied.remove(experience);
    notifyListeners();
    getFilteredList();
  }

  void addAppliedMatchingLevelFilter({required int level}) {
    this.matchingFilterListApplied.add(level);
    notifyListeners();
    getFilteredList();
  }

  void resetAppliedMatchingLevelFilter() {
    this.matchingFilterListApplied = [];
    notifyListeners();
    getFilteredList();
  }

  void removeAppliedMatchingLevelFilter({required int level}) {
    this.matchingFilterListApplied.remove(level);
    notifyListeners();
    getFilteredList();
  }

  // void applyFilter() {
  //   this.countryFilterFinal = this.countryFilterListApplied;
  //   this.experienceFilterFinal = this.experienceFilterListApplied;
  //   this.matchingFilterFinal = this.matchingFilterListApplied;
  //   getFilteredList();
  // }

  void updateFilter() async {
    Set<String> country = {};
    Set<String> experience = {};
    Set<int> matchingLevel = {};
    Utility.tryCatch(fn: () {
      for (var jobs in this.allJobs!) {
        if (jobs.location != null && jobs.location != 'N/A') {
          country.add(jobs.location!);
        }

        if (jobs.experience != null &&
            jobs.experience != 'N/A' &&
            jobs.experience?.indexOf('-') != 0) {
          experience.add(jobs.experience!);
        }

        if (jobs.resumeRating != null) {
          matchingLevel.add(int.parse('${jobs.resumeRating}'));
          log("the value is  update ${jobs.resumeRating}");
        }
      }
      this.countryFilterList.clear();
      this.experienceFilterList.clear();
      this.matchingLevelFilterList.clear();

      this.countryFilterList.addAll(country);
      this.experienceFilterList.addAll(experience);
      this.matchingLevelFilterList.addAll(matchingLevel);
    });

    notifyListeners();
  }

  void setLoading({required bool isLoading}) {
    this.loading = isLoading;
    notifyListeners();
  }

  void setCountryFilterList(List<String> countries) {
    this.countryFilterList = countries;
    notifyListeners();
  }

  void addJobs(List<ExploreJob> jobs) {
    this.allJobs = [];
    this.allJobs?.addAll(jobs);

    getFilteredList();
    // if (this.filterJobs?.length == 0) {
    //   this.filterJobs?.addAll(jobs);
    // }
    notifyListeners();
  }

  void updateIsVerified({required bool isVerified}) {
    this.isVerified = isVerified;

    // notifyListeners();
    getFilteredList();
  }

  void getFilteredList() {
    this.filterJobs = [];
    for (var jobs in this.allJobs!) {
      if (countryFilterListApplied.length != 0) {
        if (!countryFilterListApplied.contains(jobs.location)) continue;
      }
      if (experienceFilterListApplied.length != 0) {
        if (!experienceFilterListApplied.contains(jobs.experience)) continue;
      }

      if (matchingFilterListApplied.length != 0) {
        if (!matchingFilterListApplied
            .contains(int.tryParse('${jobs.resumeRating}'))) {
          continue;
        }
      }

      if (this.isVerified == true) {
        if (jobs.isVerified == 1) {
          this.filterJobs?.add(jobs);
        }
      } else
        this.filterJobs?.add(jobs);
    }

    notifyListeners();
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import '../../../../utils/config.dart';
import '../../../auth_pages/terms_and_condition_page.dart';

class IntractiveView extends StatefulWidget {
  final InteractiveContent interactiveContent;
  const IntractiveView({super.key, required this.interactiveContent});

  @override
  State<IntractiveView> createState() => _IntractiveViewState();
}

class _IntractiveViewState extends State<IntractiveView> {
  @override
  Widget build(BuildContext context) {
    bool isButtonActive = false;
    Color bgColor;
    if (widget.interactiveContent.contentType?.toLowerCase() ==
        'interactive_content') {
      isButtonActive = true;
    } else {
      isButtonActive = false;
    }

    bgColor = !isButtonActive
        ? ColorConstants.GREY_2
        : ColorConstants().primaryColorAlways();
    return Container(
      color: ColorConstants.COURSE_BG,
      height: MediaQuery.of(context).size.height * 0.24,
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row(
          //   children: [
          //     Text(
          //       '${tr('submit_before')} : ',
          //       style: Styles.bold(size: 14, color: ColorConstants.WHITE),
          //     ),
          //   ],
          // ),
          // SizedBox(height: 15),
          Text('${widget.interactiveContent.title}',
              style: Styles.bold(size: 16, color: ColorConstants.WHITE)),
          if (widget.interactiveContent.description != null)
            Text('${widget.interactiveContent.description}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: Styles.semibold(size: 14, color: ColorConstants.WHITE)),
          SizedBox(height: 8),
          Spacer(),
          InkWell(
            onTap: () {
              if (isButtonActive) {
                String? h5pUrl =
                    '${APK_DETAILS['domain_url']}/attemptH5pContentWebView/${widget.interactiveContent.h5PContentId.toString()}?user_id=${Preference.getInt(Preference.USER_ID)}';

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) {
                      return h5pUrl != '' && h5pUrl.isNotEmpty
                          ? SizedBox(
                              height: height(context) * 0.29,
                              child: TermsAndCondition(
                                url: h5pUrl,
                                title: tr('${widget.interactiveContent.title}'),
                              ),
                            )
                          : SizedBox(
                              height: height(context) * 0.26,
                              child: Center(
                                child: CircularProgressIndicator(),
                              ),
                            );
                    },
                  ),
                );
              }
            },
            child: Container(
                width: width(context),
                height: 38,
                margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                decoration: BoxDecoration(
                    color: bgColor,
                    gradient: bgColor == ColorConstants().primaryColorAlways()
                        ? LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: <Color>[
                                ColorConstants().gradientLeft(),
                                ColorConstants().gradientRight()
                              ])
                        : null,
                    borderRadius: BorderRadius.circular(8)),
                child: Center(
                  child: Text(
                    'open_h5p',
                    style: Styles.regular(
                        size: 14,
                        color: bgColor == ColorConstants.GREY_2
                            ? ColorConstants.GREY_3
                            : ColorConstants().primaryForgroundColor()),
                    textAlign: TextAlign.center,
                  ).tr(),
                )),
          )
        ],
      ),
    );
  }
}

import 'dart:math';
import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/models/response/home_response/training_detail_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/CommonHTMLWebview.dart';
import 'package:masterg/pages/training_pages/program_content/program_content_provider.dart';

import 'package:masterg/pages/training_pages/program_content/widgets/filter_card.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

import '../../../utils/config.dart';
import 'widgets/content_view.dart';

class TrainingModules extends StatefulWidget {
  final List<Modules>? module;
  const TrainingModules({super.key, required this.module});

  @override
  State<TrainingModules> createState() => _TrainingModulesState();
}

class _TrainingModulesState extends State<TrainingModules> {
  late List<ExpandableController> _expandableController;

  @override
  void initState() {
    super.initState();
    _expandableController = List.generate(max(1, widget.module?.length ?? 0),
        (index) => ExpandableController(initialExpanded: index == 0));
  }

  @override
  void dispose() {
    for (var controller in _expandableController) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //set first module default open and close

    TrainingDetailProvider trainingDetailProvider =
        Provider.of<TrainingDetailProvider>(context, listen: false);
    return ChangeNotifierProvider<ProgramContentProvider>(
      create: (context) => ProgramContentProvider(
        widget.module,
      ),
      child:
          Consumer<ProgramContentProvider>(builder: ((context, value, child) {
        if (value.loading ||
            (value.shortContentList.isEmpty && value.selectedContent == null)) {
          return _ModuleSkeletonLoader();
        }
        return OrientationBuilder(
            builder: (BuildContext context, Orientation orientation) {
          if (kIsWeb) orientation = Orientation.portrait;

          return Column(
            children: [
              Expanded(
                flex: orientation == Orientation.portrait ? 0 : 60,
                child: Container(
                  width: double.infinity,
                  child: ContentPreview(
                    selectedContent: value.selectedContent,
                  ),
                ),
              ),
              if (value.selectedContent != null &&
                  APK_DETAILS['package_name'] !=
                      'com.singularis.mescdigilibrary' &&
                  orientation == Orientation.portrait)
                Container(
                  color: ColorConstants.WHITE,
                  width: MediaQuery.of(context).size.width,
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Column(
                    // mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                          (value.selectedContent is Sessions ||
                                  (value.selectedContent is LearningShots &&
                                      ['video', 'video_yts'].contains((value
                                              .selectedContent as LearningShots)
                                          .contentType
                                          ?.toLowerCase())))
                              ? '${tr('now_playing')}'
                              : tr(
                                  '${value.selectedContent.contentType.toLowerCase()}'),
                          style: Styles.regular(
                            size: 14,
                          )),
                      Text('${value.selectedContent.title}',
                          style: Styles.semibold(size: 16)),
                    ],
                  ),
                ),
              if (APK_DETAILS['package_name'] !=
                      'com.singularis.mescdigilibrary' &&
                  orientation == Orientation.portrait)
                FilterCard(
                  value: value,
                ),
              value.shortContentListFiltered.isNotEmpty == true
                  ? Expanded(
                      child: ListView.builder(
                          itemCount: value.filtermodules?.length,
                          shrinkWrap: true,
                          physics: BouncingScrollPhysics(),
                          itemBuilder: (context, modIndex) {
                            String? subjectName = '';

                            try {
                              subjectName =
                                  '${trainingDetailProvider.skills['${value.modules?[modIndex].skillId}']['name']}';
                            } catch (e) {}

                            return Column(
                              children: [
                                if (Preference.getBool(Preference.ENABLE_SKILL,
                                            def: false) ==
                                        true &&
                                    subjectName != null &&
                                    (modIndex == 0 ||
                                        value.filtermodules?[modIndex]
                                                .skillId !=
                                            value.filtermodules?[modIndex - 1]
                                                .skillId)) ...[
                                  Column(
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        // margin: const EdgeInsets.all(4),

                                        decoration: BoxDecoration(
                                          // color: ColorConstants()
                                          //     .primaryColorAlways()
                                          //     .withValues(alpha: 0.8),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          // border: Border.all()
                                        ),
                                        padding: const EdgeInsets.only(
                                          top: 12,
                                          left: 12,
                                          right: 12,
                                        ),
                                        child: Text(
                                          '$subjectName',
                                          style: Styles.bold(
                                            lineHeight: 1,
                                            size: 16,
                                            // color: ColorConstants.WHITE
                                          ),
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) {
                                                return CommonHTMLWebView(
                                                  title:
                                                      '${trainingDetailProvider.skills['${value.modules?[modIndex].skillId}']['name']}',
                                                  url:
                                                      '${trainingDetailProvider.skills['${value.modules?[modIndex].skillId}']['description']}',
                                                  addHtmlTag: true,
                                                );
                                              },
                                            ),
                                          );
                                        },
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(left: 13.0),
                                          child: Row(
                                            children: [
                                              Text('description_view',
                                                      style:
                                                          Styles.bold(size: 12))
                                                  .tr(),
                                              SizedBox(width: 3),
                                              Icon(Icons.visibility, size: 15),
                                            ],
                                          ),
                                          //  Row(
                                          //   children: [
                                          //     Icon(Icons.visibility,size: 15),
                                          //     SizedBox(width: 3),
                                          //     Icon(Icons.html_outlined,
                                          //         size: 30),
                                          //     SizedBox(width: 3),
                                          //     Text('View',
                                          //         style: Styles.bold(size: 12)),
                                          //   ],
                                          // ),
                                        ),
                                      )
                                    ],
                                  ),
                                  // Divider(
                                  //   thickness: 1,
                                  // ),
                                ],
                                Container(
                                  // margin: const EdgeInsets.only(
                                  //     left: 15, right: 15),
                                  padding: EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 8),
                                  decoration: BoxDecoration(
                                    color: ColorConstants.WHITE,
                                  ),
                                  child: ExpandablePanel(
                                      controller: _expandableController.length >
                                              modIndex
                                          ? _expandableController[modIndex]
                                          : ExpandableController(),
                                      theme: ExpandableThemeData(
                                        hasIcon: true,
                                      ),
                                      header: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              '${value.filtermodules?[modIndex].name}',
                                              // '${trainingDetailProvider.modules?[modIndex].name}',
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              softWrap: false,
                                              style: Styles.semibold(size: 16)),
                                          /*Text(
                                            '${value.filtermodules?[modIndex].description}',
                                            // '${trainingDetailProvider.modules?[modIndex].description}',
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            softWrap: false,
                                            style: Styles.regular(size: 14),
                                          )*/
                                          // ReadMoreText(
                                          //   text:
                                          //       '${value.filtermodules?[modIndex].description}',
                                          //   color: ColorConstants.GREY_2,
                                          //   viewMore: tr('view_more'),
                                          // ),
                                          InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) {
                                                    return CommonHTMLWebView(
                                                      title:
                                                          '${value.filtermodules?[modIndex].name}',
                                                      url:
                                                          '${value.filtermodules?[modIndex].description}',
                                                      addHtmlTag: true,
                                                    );
                                                  },
                                                ),
                                              );
                                            },
                                            child: Row(
                                              children: [
                                                Text('description_view',
                                                        style: Styles.bold(
                                                            size: 12))
                                                    .tr(),
                                                SizedBox(width: 3),
                                                Icon(Icons.visibility,
                                                    size: 15),
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            height: 5,
                                          ),
                                        ],
                                      ),
                                      collapsed: SizedBox(
                                        height: 0,
                                      ),
                                      expanded: value.shortContentListFiltered[
                                                  modIndex] !=
                                              null
                                          ? ListView.builder(
                                              shrinkWrap: true,
                                              physics: BouncingScrollPhysics(),
                                              itemCount: value
                                                      .shortContentListFiltered[
                                                          modIndex]
                                                      .length ??
                                                  0,
                                              itemBuilder: (context, index) {
                                                if (value.shortContentListFiltered[modIndex][index]
                                                        ['sorted_content'] ==
                                                    'sessions') {
                                                  dynamic sessionData =
                                                      Sessions.fromJson(value
                                                              .shortContentListFiltered[
                                                          modIndex][index]);
                                                  return _moduleCard(
                                                      provider: value,
                                                      leadingid:
                                                          Utility.classStatus(
                                                              int.parse(
                                                                  '${sessionData.startDate}'),
                                                              int.parse(
                                                                '${sessionData.endDate}',
                                                              ),
                                                              currentIndiaTime!),
                                                      time:
                                                          ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(sessionData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''} ',
                                                      title:
                                                          '${sessionData.title}',
                                                      description: sessionData
                                                                  .contentType
                                                                  ?.toLowerCase() ==
                                                              'teamsclass'
                                                          ? 'teams'
                                                          : '${sessionData.contentType?.toLowerCase() == 'otherclass' ? 'weblink' : sessionData.contentType?.toLowerCase() == 'liveclass' || sessionData.contentType?.toLowerCase() == 'zoomclass' ? 'Live' : 'Classroom'}',
                                                      type: 'session',
                                                      data: sessionData,
                                                      index: index,
                                                      context: context,
                                                      showLiveStatus: Utility.isBetween(
                                                          int.parse(
                                                              '${sessionData.startDate}'),
                                                          int.parse(
                                                              '${sessionData.endDate}'),
                                                          currentIndiaTime!));
                                                } else if (value.shortContentListFiltered[modIndex]
                                                            [index]
                                                        ['sorted_content'] ==
                                                    'assessments') {
                                                  dynamic assessmentData =
                                                      Assessments.fromJson(value
                                                              .shortContentListFiltered[
                                                          modIndex][index]);

                                                  return _moduleCard(
                                                    provider: value,
                                                    leadingid:
                                                        Utility.classStatus(
                                                            int.parse(
                                                                '${assessmentData.startDate}'),
                                                            int.parse(
                                                              '${assessmentData.endDate}',
                                                            ),
                                                            currentIndiaTime!),
                                                    time:
                                                        ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(assessmentData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
                                                    title:
                                                        '${assessmentData.title}',
                                                    description:
                                                        '${capitalize(assessmentData.contentType)}',
                                                    type: 'assessment',
                                                    data: assessmentData,
                                                    index: index,
                                                    context: context,
                                                  );
                                                } else if (value.shortContentListFiltered[modIndex]
                                                            [index]
                                                        ['sorted_content'] ==
                                                    'assignments') {
                                                  dynamic assignmentData =
                                                      Assignments.fromJson(value
                                                              .shortContentListFiltered[
                                                          modIndex][index]);

                                                  return _moduleCard(
                                                      provider: value,
                                                      leadingid: Utility.classStatus(
                                                          int.parse(
                                                              '${assignmentData.startDate}'),
                                                          int.parse(
                                                              '${assignmentData.endDate}'),
                                                          currentIndiaTime!),
                                                      time:
                                                          '${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(assignmentData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
                                                      title:
                                                          '${assignmentData.title}',
                                                      description:
                                                          '${capitalize(assignmentData.contentType)}',
                                                      type: 'assignment',
                                                      data: assignmentData,
                                                      index: index,
                                                      context: context,
                                                      submissionDate:
                                                          assignmentData
                                                              .submissionDate);
                                                } else if (value.shortContentListFiltered[modIndex]
                                                            [index]
                                                        ['sorted_content'] ==
                                                    'learning_shots') {
                                                  dynamic learningShorts =
                                                      LearningShots.fromJson(
                                                          value.shortContentListFiltered[
                                                              modIndex][index]);
                                                  // String? dueByText =
                                                  //     '${Utility().isRTL(context) ? '' : '•'} ${tr('due_by')} ${learningShorts.dueDate != null ? learningShorts.dueDate : ''}';

                                                  return _moduleCard(
                                                    provider: value,

                                                    leadingid: learningShorts
                                                                .completion ==
                                                            100.0
                                                        ? 2
                                                        : learningShorts
                                                                    .completion !=
                                                                0.0
                                                            ? 3
                                                            : 1,
                                                    dueDate:
                                                        '${Utility().isRTL(context) ? '•' : ''} ${tr('due_by')} ${Utility.convertCourseTime(learningShorts.dueDate != null ? learningShorts.dueDate : '', Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                                                    // '${Utility().isRTL(context) ? '' : '•'}${tr('due_by')} ${learningShorts.dueDate != null ? learningShorts.dueDate : ''}',

                                                    time:
                                                        '${Utility().isRTL(context) ? '' : ' •'} ${learningShorts.contentType == 'notes' ? learningShorts.noPages : learningShorts.durationInMinutes != null ? learningShorts.durationInMinutes : ''}${learningShorts.contentType == 'notes' ? tr('page') : learningShorts.durationInMinutes == 0 || learningShorts.durationInMinutes == 1 ? tr('min ') : learningShorts.durationInMinutes != null ? tr('mins ') : ''}${Utility().isRTL(context) ? '' : learningShorts.durationInMinutes != null ? '•' : ""}',

                                                    //     ' ${Utility().isRTL(context) ? '' : '•'} ${learningShorts.durationInMinutes} ${learningShorts.durationInMinutes == 1 ? tr('min') : tr('mins')} ${Utility().isRTL(context) ? '•' : ''}',
                                                    title:
                                                        '${learningShorts.title ?? ''}',
                                                    description:
                                                        '${capitalize(learningShorts.contentType ?? '')}',
                                                    type: 'learningShots',
                                                    data: learningShorts,
                                                    contentType: learningShorts
                                                                .contentType ==
                                                            'text'
                                                        ? 'text'
                                                        : '',

                                                    index: index,
                                                    context: context,
                                                  );
                                                } else if (value.shortContentListFiltered[modIndex]
                                                            [index]
                                                        ['sorted_content'] ==
                                                    'scorm') {
                                                  dynamic scormContent =
                                                      Scorm.fromJson(value
                                                              .shortContentListFiltered[
                                                          modIndex][index]);

                                                  return _moduleCard(
                                                    provider: value,
                                                    leadingid: Utility.classStatus(
                                                        int.parse(
                                                            '${scormContent.startDate ?? ''}'),
                                                        int.parse(
                                                            '${scormContent.endDate ?? ''}'),
                                                        currentIndiaTime!),
                                                    time:
                                                        ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(scormContent.startDate ?? '', Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
                                                    title:
                                                        '${scormContent.title ?? ''}',
                                                    description:
                                                        '${capitalize(scormContent.contentType ?? '')}',
                                                    type: 'scorm',
                                                    data: scormContent,
                                                    index: index,
                                                    context: context,
                                                  );
                                                } else if (value.shortContentListFiltered[modIndex]
                                                        [index]['sorted_content'] ==
                                                    'interactive_content') {
                                                  dynamic intractiveContent =
                                                      InteractiveContent.fromJson(
                                                          value.shortContentListFiltered[
                                                              modIndex][index]);

                                                  return _moduleCard(
                                                    provider: value,
                                                    leadingid:
                                                        Utility.classStatus(
                                                            intractiveContent
                                                                .startDate,
                                                            intractiveContent
                                                                .endDate,
                                                            currentIndiaTime!),
                                                    time:
                                                        '${Utility().isRTL(context) ? '' : ''} ${intractiveContent.contentType == 'interactive_content' ? tr('intractive_content_title') : intractiveContent.contentType} ${Utility().isRTL(context) ? '' : ''}',
                                                    title:
                                                        '${intractiveContent.title ?? ''}',
                                                    description: '',
                                                    type: 'interactive_content',
                                                    data: intractiveContent,
                                                    index: index,
                                                    context: context,
                                                  );
                                                }

                                                return SizedBox();
                                              },
                                            )
                                          : SizedBox()),
                                ),
                              ],
                            );
                          }),
                    )
                  : Container(
                      width: width(context) * 0.8,
                      height: 400,
                      child: Center(
                        child: Text(
                          '${tr('lbl_no_available_module')}',
                          style: Styles.regular(),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
              /*InkWell(
                onTap: () {
                },
                child: Container( width:double.infinity,
                height: 60,
                 child: CertificateContainer(),
                ),
              )*/
            ],
          );
        });
      })),
    );
  }

  Widget _moduleCard(
      {required String? time,
      required String? title,
      required String? description,
      required String? type,
      String? contentType,
      String? dueDate,
      required dynamic data,
      required int index,
      required context,
      required ProgramContentProvider provider,
      int leadingid = 1,
      bool showLiveStatus = false,
      int? submissionDate}) {
    bool isSelected = false;
    try {
      isSelected =
          provider.selectedContent.programContentId == data.programContentId;
    } catch (e) {}
    return InkWell(
      onTap: () async {
        provider.updateSelectedContent(selectedContent: data);
      },
      child: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
                color: ui.Color.fromARGB(255, 255, 255, 255),
                borderRadius: BorderRadius.circular(12),
                border: isSelected ? Border.all(width: 0.6) : null),
            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                leadingid == 0
                    ? SvgPicture.asset(
                        'assets/images/live_icon.svg',
                        height: 20.0,
                        width: 20.0,
                        allowDrawingOutsideViewBox: true,
                      )
                    : leadingid == 1
                        ? SvgPicture.asset(
                            'assets/images/empty_circle.svg',
                            height: 20.0,
                            width: 20.0,
                            allowDrawingOutsideViewBox: true,
                          )
                        : leadingid == 2
                            ? Icon(
                                Icons.check_circle,
                                color: ColorConstants.GREEN,
                                size: 20.0,
                              )
                            : leadingid == 3
                                ? SvgPicture.asset(
                                    'assets/images/ongoing_icon.svg',
                                    height: 20.0,
                                    width: 20.0,
                                    allowDrawingOutsideViewBox: true,
                                  )
                                : SvgPicture.asset(
                                    'assets/images/pending_icon.svg',
                                    height: 20.0,
                                    width: 20.0,
                                    allowDrawingOutsideViewBox: true,
                                  ),
                SizedBox(
                  width: 20,
                ),
                Container(
                  width: MediaQuery.of(context).size.width * (0.8 - 0.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: showLiveStatus == true
                                ? MediaQuery.of(context).size.width *
                                    (0.6 - 0.0)
                                : MediaQuery.of(context).size.width *
                                    (0.8 - 0.0),
                            child: Text(
                              '${title ?? ''}',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              softWrap: false,
                              style: Styles.semibold(size: 16),
                            ),
                          ),
                          if (showLiveStatus) ...[
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset('assets/images/live_icon.svg',
                                    height: 15.0,
                                    width: 15.0,
                                    allowDrawingOutsideViewBox: true),
                                Text(
                                  data.contentType == 'liveclass' ||
                                          data.contentType == 'zoomclass'
                                      ? '${tr('Live_now')}'
                                      : tr('ongoing'),
                                  style: Styles.regular(
                                      size: 12, color: ColorConstants.RED),
                                )
                              ],
                            )
                          ]
                        ],
                      ),

                      Row(
                        children: [
                          Text(
                            '${description?.toLowerCase() == 'video_yts' ? tr('video') : tr('$description'.toLowerCase())}',
                            maxLines: 1,
                            textDirection: ui.TextDirection.ltr,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(
                              size: 14,
                            ),
                          ),
                          // SizedBox(width: 5),

                          //singh
                          Text(
                            '${time ?? ''}',
                            textDirection: ui.TextDirection.ltr,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(
                              size: 14,
                            ),
                          ),
                          Text(
                            '${dueDate ?? ''}',
                            // textDirection: ui.TextDirection.ltr,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(
                              size: 14,
                            ),
                          )
                        ],
                      ),

                      ///Add New For submission date
                      type == 'assignment'
                          ? Row(
                              children: [
                                Text('${tr('submission_date')}:'),
                                Text(
                                  ' ${'${submissionDate != null ? Utility.convertDateFromMillis(submissionDate, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}'}',
                                  style: Styles.regular(
                                    size: 14,
                                  ),
                                  textDirection: ui.TextDirection.ltr,
                                ),
                              ],
                            )
                          : SizedBox(),

                      if (type == 'learningShots' && contentType != 'text')
                        Container(
                          height: 10,
                          width:
                              MediaQuery.of(context).size.width * (0.8 - 0.0),
                          margin: EdgeInsets.only(top: 5),
                          decoration: BoxDecoration(
                              color: ColorConstants.GREY,
                              borderRadius: BorderRadius.circular(10)),
                          child: Stack(
                            children: [
                              Container(
                                height: 10,
                                width: MediaQuery.of(context).size.width *
                                    (0.8 - 0.0) *
                                    data.completion /
                                    100,
                                decoration: BoxDecoration(
                                    color: Color(0xffFFB72F),
                                    borderRadius: BorderRadius.circular(10)),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
                Expanded(
                  child: SizedBox(),
                ),
              ],
            ),
          ),
          if (!showLiveStatus &&
              APK_DETAILS['package_name'] == 'com.singulariswow.aid' &&
              (data is Sessions) == true &&
              data.contentType.toString().toLowerCase() == 'offlineclass')
            Positioned(
              right: 4,
              top: 0,
              bottom: 0,
              child: Center(
                child: Text(
                  '${data.isAttended ? 'present' : 'absent'}',
                  style: Styles.semibold(
                      size: 14,
                      color: data.isAttended
                          ? ColorConstants.GREEN
                          : ColorConstants.RED),
                ).tr(),
              ),
            )
        ],
      ),
    );
  }

  String capitalize(String? letter) {
    return "${letter![0].toUpperCase()}${letter.substring(1).toLowerCase()}";
  }
}

class _ModuleSkeletonLoader extends StatelessWidget {
  const _ModuleSkeletonLoader();

  @override
  Widget build(BuildContext context) {
    return ListView.builder(itemBuilder: (context, index) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Shimmer.fromColors(
          baseColor: ColorConstants.GREY_4,
          highlightColor: ColorConstants.GREY_4,
          child: Container(
            height: 100,
            margin: EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      );
    });
  }
}

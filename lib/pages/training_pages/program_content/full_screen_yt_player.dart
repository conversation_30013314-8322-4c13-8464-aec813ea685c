import 'package:flutter/material.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class FullScreenYoutubePlayer extends StatefulWidget {
  final YoutubePlayerController controller;

  FullScreenYoutubePlayer({Key? key, required this.controller})
      : super(key: key);

  @override
  State<FullScreenYoutubePlayer> createState() =>
      _FullScreenYoutubePlayerState();
}

class _FullScreenYoutubePlayerState extends State<FullScreenYoutubePlayer> {
  @override
  void initState() {
    scrollandPlay();
    super.initState();
  }

  void scrollandPlay() {
    Future.delayed(Duration(seconds: 2)).then((value) {
      widget.controller.play();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Transform.scale(
          scale: 0.7,
          child: Container(
            decoration: BoxDecoration(
              color: ColorConstants.BLACK.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_back,
                size: 30,
                color: ColorConstants.WHITE,
              ),
              onPressed: () {
                try {
                  Navigator.pop(context);
                } catch (e) {
                  print('exc $e');
                }
              },
            ),
          ),
        ),
      ),
      body: YoutubePlayer(
        onEnded: (YoutubeMetaData data) {
          Navigator.pop(context);
        },
        controller: widget.controller,
        showVideoProgressIndicator: true,
        bottomActions: [
          CurrentPosition(),
          RemainingDuration(),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'dynamic_webview_height_manager.dart';

/// Example usage of the DynamicHeightWebView widget
/// This demonstrates how to use the new dynamic WebView system
class DynamicWebViewExample extends StatefulWidget {
  const DynamicWebViewExample({Key? key}) : super(key: key);

  @override
  State<DynamicWebViewExample> createState() => _DynamicWebViewExampleState();
}

class _DynamicWebViewExampleState extends State<DynamicWebViewExample> {
  final List<String> _sampleHtmlContents = [
    // Simple text
    '<p>This is a simple text content.</p>',
    
    // Math equation (LaTeX)
    '<p>Here is a math equation:</p><img src="https://latex.codecogs.com/svg.image?x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}" alt="Quadratic Formula">',
    
    // Complex content with table
    '''
    <h3>Sample Question</h3>
    <p>Which of the following is correct?</p>
    <table border="1">
      <tr><th>Option</th><th>Value</th></tr>
      <tr><td>A</td><td>10</td></tr>
      <tr><td>B</td><td>20</td></tr>
      <tr><td>C</td><td>30</td></tr>
    </table>
    <p>Select the correct answer.</p>
    ''',
    
    // Long content
    '''
    <h2>Long Content Example</h2>
    <p>This is a longer piece of content that should demonstrate how the WebView automatically adjusts its height based on the content length.</p>
    <ul>
      <li>First item in the list</li>
      <li>Second item in the list</li>
      <li>Third item in the list</li>
      <li>Fourth item in the list</li>
      <li>Fifth item in the list</li>
    </ul>
    <p>Additional paragraph to make the content even longer and test the height calculation mechanism.</p>
    ''',
  ];

  int _currentIndex = 0;
  double _lastCalculatedHeight = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dynamic WebView Example'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Content Type: ${_getContentType(_currentIndex)}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Last Calculated Height: ${_lastCalculatedHeight.toStringAsFixed(1)}px',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            
            // Navigation buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _currentIndex > 0 ? _previousContent : null,
                  child: const Text('Previous'),
                ),
                ElevatedButton(
                  onPressed: _currentIndex < _sampleHtmlContents.length - 1 
                      ? _nextContent 
                      : null,
                  child: const Text('Next'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Dynamic WebView
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DynamicHeightWebView(
                htmlContent: _sampleHtmlContents[_currentIndex],
                zoomLevel: 2.5,
                minHeight: 50,
                maxHeight: 400,
                onHeightChanged: (height) {
                  setState(() {
                    _lastCalculatedHeight = height;
                  });
                },
                loadingWidget: const CircularProgressIndicator(),
              ),
            ),
            
            const SizedBox(height: 16),
            const Text(
              'Features Demonstrated:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Automatic height calculation based on content\n'
              '• Smooth height transitions when content changes\n'
              '• Support for math equations (LaTeX)\n'
              '• Proper zoom level handling (2.5x)\n'
              '• Error handling and fallback mechanisms\n'
              '• Loading indicators during content loading',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  String _getContentType(int index) {
    switch (index) {
      case 0:
        return 'Simple Text';
      case 1:
        return 'Math Equation';
      case 2:
        return 'Table Content';
      case 3:
        return 'Long Content';
      default:
        return 'Unknown';
    }
  }

  void _previousContent() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
        _lastCalculatedHeight = 0; // Reset height display
      });
    }
  }

  void _nextContent() {
    if (_currentIndex < _sampleHtmlContents.length - 1) {
      setState(() {
        _currentIndex++;
        _lastCalculatedHeight = 0; // Reset height display
      });
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/CommonWebView.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/utility.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/models/request/home_request/user_program_subscribe.dart';
import '../../../local/pref/Preference.dart';
import '../../../utils/Styles.dart';
import '../../../utils/resource/colors.dart';
import '../../custom_pages/TapWidget.dart';
import '../../custom_pages/alert_widgets/alerts_widget.dart';

class CoursesDetailsPage extends StatefulWidget {
  final String? imgUrl;
  final String? tagName;
  final int? indexc;
  final String? name;
  final String? description;
  final int? regularPrice;
  final int? salePrice;
  final String? trainer;
  final int? enrolmentCount;
  final int? id;
  final String? shortCode;
  final String? type;
  final bool? isSubscribed;

  const CoursesDetailsPage(
      {Key? key,
      required this.imgUrl,
      required this.indexc,
      this.tagName,
      this.name,
      this.description,
      this.regularPrice,
      this.salePrice,
      this.trainer,
      this.enrolmentCount,
      this.id,
      this.type,
      this.shortCode,
      required this.isSubscribed})
      : super(key: key);

  @override
  State<CoursesDetailsPage> createState() => _CoursesDetailsPageState();
}

class _CoursesDetailsPageState extends State<CoursesDetailsPage> {
  @override
  void initState() {
    isApplied = widget.isSubscribed!;

    print('salePrice==${widget.salePrice}');
    super.initState();
  }

  // Future<bool> _willPopCallback() async {
  //   Navigator.pop(context, isApplied);
  //   return false;
  // }

  bool loading = false;
  bool isApplied = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) {
            Navigator.pop(context, isApplied);
          }
        },
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) async {
            if (state is UserProgramSubscribeState)
              _handleApplyCourseState(state);
          },
          child: Scaffold(
            appBar: AppBar(
                backgroundColor: ColorConstants.ACCENT_COLOR,
                elevation: 0,
                leading: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.arrow_back,
                      color: Color(0xff0E1638),
                    )),
                title: Text(
                  '${widget.name.toString()}',
                  style: Styles.semibold(),
                )),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            backgroundColor: Colors.white,
            floatingActionButton: Container(
              width: MediaQuery.of(context).size.width,
              height: 100,
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
              decoration: BoxDecoration(
                color: ColorConstants.WHITE,
                boxShadow: [
                  BoxShadow(
                    color: ColorConstants.BLACK.withValues(alpha: 0.15),
                    spreadRadius: 0,
                    blurRadius: 20,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  widget.enrolmentCount != 0 && widget.enrolmentCount != null
                      ? Text(
                          '${widget.enrolmentCount} ${tr('student_already_enrolled')}',
                          style: Styles.regular(size: 12))
                      : SizedBox(),
                  SizedBox(
                    height: 10,
                  ),
                  TapWidget(
                    onTap: () async {
                      if (isApplied) return;
                      if (widget.type == 'paid') {
                        final url =
                            'https://learnandbuild.in/signin.php?username=${Preference.getString(Preference.USER_EMAIL)}&slug=${widget.shortCode}';

                        await Navigator.push(
                            context,
                            NextPageRoute(CommonWebView(
                              url: url,
                            ))).then((isSuccess) {
                          if (isSuccess == true) {
                            Navigator.pop(context, true);
                          }
                        });
                      } else
                        _subscribeRequest(
                            widget.type?.toLowerCase(), widget.id);
                      AlertsWidget.showCustomDialog(
                          context: context,
                          title: widget.type == 'approve'
                              ? tr('approval_request_sent')
                              : tr('subscribed_sucessfully'),
                          text: widget.type == 'approve'
                              ? tr('course_assign_soon')
                              : '',
                          icon: 'assets/images/success_icon.svg',
                          alertType: 2,
                          showCancel: false,
                          oKText: tr('ok'),
                          onOkClick: () async {});

                      setState(() {
                        isApplied = true;
                      });
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(colors: [
                          isApplied
                              ? ColorConstants.GREY_3
                              : ColorConstants().gradientLeft(),
                          isApplied
                              ? ColorConstants.GREY_3
                              : ColorConstants().gradientRight(),
                        ]),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: 8, right: 8, top: 4, bottom: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              isApplied
                                  ? tr('already_applied')
                                  : widget.type == 'paid'
                                      ? '${tr('enroll_now')}'
                                      : '${tr('request')}',
                              style: Styles.textExtraBold(
                                  size: 14, color: ColorConstants.WHITE),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            body: CustomScrollView(
              slivers: <Widget>[
                SliverAppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: Colors.white,
                  expandedHeight: width(context) * 0.67,
                  flexibleSpace: FlexibleSpaceBar(
                    title: Text('', textScaler: TextScaler.linear(1)),
                    background: Hero(
                      tag: widget.tagName! + widget.indexc.toString(),
                      child: ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(0),
                          topRight: Radius.circular(0),
                        ),
                        child: Image.network(
                          widget.imgUrl!,
                          width: width(context),
                          errorBuilder: (context, error, stackTrace) {
                            return SvgPicture.asset(
                              'assets/images/gscore_postnow_bg.svg',
                            );
                          },
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.all(20.0),
                    margin: const EdgeInsets.only(bottom: 120.0),
                    child: SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.name.toString(),
                            style: Styles.bold(),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            child:
                                Utility().isHtml('${widget.description ?? ''}')
                                    ? Html(
                                        data: '${widget.description ?? ''}',
                                      )
                                    : Text(
                                        '${widget.description}',
                                        style: Styles.regular(size: 14),
                                      ),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          widget.trainer != '' && widget.trainer != 'null'
                              ? RichText(
                                  text: TextSpan(children: [
                                  TextSpan(
                                      text: '${tr('trainer_name')}: ',
                                      style: Styles.regular(size: 12)),
                                  TextSpan(
                                      text: Utility()
                                          .decrypted128('${widget.trainer}'),
                                      style: Styles.bold(size: 12)),
                                ]))
                              : SizedBox(),
                          SizedBox(
                            height: 20,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              if (widget.regularPrice != widget.salePrice)
                                Text('₹${widget.regularPrice}',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    softWrap: false,
                                    style: TextStyle(
                                      fontSize: 15,
                                      decoration: TextDecoration.lineThrough,
                                    )),
                              SizedBox(
                                width: 10,
                              ),
                              if (widget.salePrice != null)
                                Text('₹${widget.salePrice}',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    softWrap: false,
                                    style: Styles.bold(
                                        size: 23, color: ColorConstants.GREEN)),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  void _handleApplyCourseState(UserProgramSubscribeState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          loading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("JoyCategoryState....................");
          Log.v(state.response!.data);

          loading = false;
          break;
        case ApiStatus.ERROR:
          loading = false;
          Log.v("ErrorHome..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  _subscribeRequest(type, id) {
    if (type == "approve") {
      BlocProvider.of<HomeBloc>(context).add(UserProgramSubscribeEvent(
          subrReq: UserProgramSubscribeReq(programId: id)));
    }

    if (type == "open" || type == null) {
      BlocProvider.of<HomeBloc>(context).add(UserProgramSubscribeEvent(
          subrReq: UserProgramSubscribeReq(programId: id)));
    }
  }
}

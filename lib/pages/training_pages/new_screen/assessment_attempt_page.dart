import 'dart:async';
import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/save_answer_request.dart';
import 'package:masterg/data/models/response/home_response/test_attempt_response.dart';
import 'package:masterg/data/models/response/home_response/test_review_response.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/interview_assessment/model/question.dart';
import 'package:masterg/pages/interview_assessment/question_attempt_review.dart';
import 'package:masterg/pages/training_pages/new_screen/widget/dynamic_webview_height_manager.dart';
import 'package:masterg/pages/training_pages/new_screen/widget/question_option_web_view.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';
//import 'package:video_compress/video_compress.dart';
import '../../../utils/constant.dart';
import '../../content_view/countdown_progress_indicator_page.dart';
import '../../interview_assessment/interview_assessment.dart';
import '../../interview_assessment/interview_provider.dart';
import 'assessment_your_answers_page.dart';

class AssessmentAttemptPage extends StatefulWidget {
  final int? contentId;
  final bool isReview;
  final bool isResumed;
  final int? programId;
  final bool isVideoTypeQuiz;
  final int? attemptAllowed;
  final bool? isEvent;

  AssessmentAttemptPage(
      {this.contentId,
      this.isReview = false,
      this.isResumed = false,
      this.programId,
      required this.isVideoTypeQuiz,
      this.attemptAllowed,
      this.isEvent});

  @override
  _AssessmentAttemptPageState createState() => _AssessmentAttemptPageState();

  Timer? _allTimer;
  var _pageViewController = PageController();
  int currentSection = 0;
  var _currentQuestion = 0;
  // int? _currentQuestionId;
  ScrollController? _questionController;
  bool? imageClick;
  List<TestAttemptBean> _list = [];
  List<TestReviewBean> _reviewResplist = [];
  Map<int, bool> attemptList = Map<int, bool>();
  int? _durationMins = 0;
  bool isSubmit = false;
  bool showSubmitDialog = false;
  StopWatchTimer _stopWatchTimer = StopWatchTimer();
  bool pageChange = true;
  bool _isOptionSelected = false;
  bool _isResumedLoading = false;
  bool lastSave = false;
  bool _isEverythingOver = false;
  bool _savedAnswer = false;
  bool _isContinued = false;
  bool isSavedManually = false;
}

class IdMapper {
  int? questionId;
  String? color;
  int? timeTaken;

  IdMapper({this.questionId, this.color, this.timeTaken});

  IdMapper.fromJson(Map<String, dynamic> json) {
    questionId = json['questionId'];
    color = json['color'];
    timeTaken = json['timeTaken'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['questionId'] = this.questionId;
    data['color'] = this.color;
    data['timeTaken'] = this.timeTaken;
    return data;
  }
}

class _AssessmentAttemptPageState extends State<AssessmentAttemptPage>
    with WidgetsBindingObserver {
  //final GlobalKey<ScaffoldState> _key = new GlobalKey<ScaffoldState>();
  //HtmlEditorController controller = HtmlEditorController();
  final _key = GlobalKey<ScaffoldMessengerState>();
  var _isLoading = false;
  var _scaffoldContext;
  late HomeBloc _authBloc;
  String? _title;
  bool willPop = false;
  int? _disableBackTracking;
  String? _questionSequence;
  // final _controller = CountDownController();
  bool onLongPress = false;
  int? selectedIndex = -1;
  int? selectedIndexTop = -1;
  var currentQuestion;
  String? questionType;

  List<TextEditingController> _fillTheBlankController = [];
  List<TextEditingController> _subjectiveController = [];
  List<HtmlEditorController> _htmlController = [];
  final Map<String, double> _webViewHeights = {};
  String? qunType;

  int? assessmentAttemptId;
  InterviewAssessmentProvider? videoInterviewProvider;
  late InAppWebViewController _webViewController;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.inactive) {
      widget._stopWatchTimer.onStopTimer();
      //widget._stopWatchTimer.onExecute.add(StopWatchExecute.stop);
    }
    if (state == AppLifecycleState.resumed) {
      widget._stopWatchTimer.onStartTimer();
      // widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
    }
  }

  void _handleAReviewTestResponse(AttemptTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          this.setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            widget._list.clear();
            widget._durationMins =
                state.response!.data!.assessmentDetails!.durationInMinutes;
            _title = state.response!.data!.assessmentDetails!.title;
            _disableBackTracking =
                state.response!.data!.assessmentDetails!.disableBackTracking;
            _questionSequence =
                state.response!.data!.assessmentDetails!.questionSequence;

            if (state.response!.data!.assessmentDetails!.questionCount ==
                null) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                duration: Duration(milliseconds: 500),
                content: Text('no_data').tr(),
              ));
            }

            if (widget._durationMins == null || widget._durationMins == '')
              widget._durationMins = 1800;
            else
              widget._durationMins = widget._durationMins! * 60;
            widget._stopWatchTimer.onStartTimer();
            //widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
            widget._allTimer = Timer.periodic(Duration(seconds: 1), (timer) {
              if (widget._allTimer!.tick == widget._durationMins) {
                widget._isEverythingOver = true;
              }
              setState(() {});
            });
            for (int i = 0;
                i < state.response!.data!.assessmentDetails!.questions!.length;
                i++) {
              widget._list.add(
                TestAttemptBean(
                    question:
                        state.response!.data!.assessmentDetails!.questions![i],
                    id: state.response!.data!.assessmentDetails!.questions![i]
                        .questionId,
                    isVisited: 0,
                    title: state.response!.data!.assessmentDetails!
                        .questions![i].question),
              );
            }

            _fillTheBlankController = List.generate(
                widget._list.length, (_) => TextEditingController());
            _subjectiveController = List.generate(
                widget._list.length, (_) => TextEditingController());
            _htmlController = List.generate(
                widget._list.length, (_) => HtmlEditorController());
          }
          Utility.waitFor(2).then((value) {
            widget._isResumedLoading = false;
            widget._pageViewController.jumpToPage(widget._currentQuestion);
          });
          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  void _handleSaveAnswerResponse(SaveAnswerState state) {
    try {
      setState(() async {
        switch (state.apiState) {
          case ApiStatus.LOADING:
            videoInterviewProvider?.updateUploadingStatus(isUploading: true);
            _isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            videoInterviewProvider?.updateUploadingStatus(isUploading: false);
            if (videoInterviewProvider != null) {
              videoInterviewProvider?.updateInputWidth(isExpanded: false);
              if (videoInterviewProvider?.isLast() == true) {
                videoInterviewProvider?.pauseVideo();
                bool isRTL = Utility().isRTL(context);
                await showDialog(
                    context: context,
                    useSafeArea: false,
                    builder: (context) {
                      // interviewAssessmentProvider
                      //     .addContext(context);
                      return QuestionAttemptReview(
                          isRTL: isRTL,
                          title: videoInterviewProvider?.title,
                          interviewAssessmentProvider: videoInterviewProvider!);
                    }).then((value) {
                  // interviewAssessmentProvider.removeLastContext();
                  if (value == true) {
                    Navigator.pop(context, 1);
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text('assesssment_submitted').tr(),
                    ));
                    videoInterviewProvider?.playVideo();
                  }
                });
              } else
                videoInterviewProvider?.next();
            }
            assessmentAttemptId = state.response?.data?.attemptId;
            widget._isOptionSelected = false;
            widget._savedAnswer = false;
            widget.isSavedManually = false;
            _isLoading = false;
            setState(() {});

            // bool pageChanged = false;
            if (widget._isContinued == false) {
              if (widget._isEverythingOver == false) {
                // pageChanged = true;
                widget._pageViewController.nextPage(
                  duration: Duration(milliseconds: 100),
                  curve: Curves.ease,
                );
                // }
              } else {
                _submitAnswers();
              }

              _isLoading = false;
            } else {
              _isLoading = false;
              widget._isContinued = false;
              widget.pageChange = true;
            }
            break;
          case ApiStatus.ERROR:
            _isLoading = false;
            widget._isOptionSelected = false;
            widget._savedAnswer = false;
            widget.isSavedManually = false;
            if (videoInterviewProvider != null) {
              videoInterviewProvider?.updateUploadingStatus(isUploading: false);
              videoInterviewProvider?.changeCurrentAttemptStatus(
                  isChanged: true);
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text('assesssment_save_error').tr(),
              ));
              videoInterviewProvider
                  ?.questions[videoInterviewProvider!.currentQuestionIndex]
                  .questionStatus = QuestionStatus.pending;
            }

            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e) {}
  }

  void _handleReviewTestResponse(ReviewTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          this.setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            widget._reviewResplist.clear();
            for (int i = 0;
                i < state.response!.data!.assessmentReview!.questions!.length;
                i++) {
              widget._reviewResplist.add(
                TestReviewBean(
                    question:
                        state.response!.data!.assessmentReview!.questions![i],
                    id: state.response!.data!.assessmentReview!.questions![i]
                        .questionId,
                    title: state.response!.data!.assessmentReview!.questions![i]
                        .question),
              );
            }

            if (widget._reviewResplist.length > 0) {
              // widget._currentQuestionId =
              widget._reviewResplist.first.question!.questionId;
            }
          }
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.ERROR:
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  void _handleSubmitAnswerResponse(SubmitAnswerState state) {
    try {
      setState(() async {
        widget._stopWatchTimer.onResetTimer();
        //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
        widget._allTimer!.cancel();
        widget.isSubmit = true;
        switch (state.apiState) {
          case ApiStatus.LOADING:
            videoInterviewProvider?.updateUploadingStatus(isUploading: true);

            _isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            videoInterviewProvider?.updateUploadingStatus(isUploading: true);
            _isLoading = false;

            bool isRTL = Utility().isRTL(context);
            if (widget.isVideoTypeQuiz) {
              await showDialog(
                  useSafeArea: false,
                  context: context,
                  builder: (context) {
                    return StatefulBuilder(
                      builder: (context, setState) {
                        return Directionality(
                          textDirection: Utility.setDirection(isRTL),
                          child: Scaffold(
                            appBar: AppBar(
                                backgroundColor: ColorConstants.WHITE,
                                elevation: 0.3,
                                iconTheme:
                                    IconThemeData(color: ColorConstants.BLACK),
                                title: Text('video_interview',
                                        style: Styles.bold(
                                            color: ColorConstants.BLACK))
                                    .tr()),
                            body: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: height(context) * 0.2,
                                ),

                                // Utility().isRTL(context)
                                //     ? Transform(
                                //         alignment: Alignment.center,
                                //         transform: Matrix4.identity()
                                //           ..scale(-1.0, 1.0),
                                //         child: SvgPicture.asset(
                                //             'assets/images/video_interview_done.svg'),
                                //       )
                                //     :

                                SvgPicture.asset(
                                    'assets/images/video_interview_done.svg'),
                                SizedBox(
                                  height: height(context) * 0.02,
                                ),
                                Text(
                                  'thank_you_for_your_submission',
                                  style: Styles.semibold(
                                      size: 18,
                                      color: ColorConstants()
                                          .primaryColorbtnAlways()),
                                  textAlign: TextAlign.center,
                                ).tr(),
                                SizedBox(
                                  height: height(context) * 0.02,
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    'participation_interview_assessment',
                                    style: Styles.regular(
                                        color: Color(0xff727C95)),
                                    textAlign: TextAlign.center,
                                  ).tr(),
                                ),
                                SizedBox(
                                  height: height(context) * 0.02,
                                ),
                                Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'your_reference_no',
                                        style: Styles.regular(
                                            color: Color(0xff222631)),
                                        textAlign: TextAlign.center,
                                      ).tr(),
                                      Text(
                                        '-$assessmentAttemptId',
                                        style: Styles.regular(
                                            color: Color(0xff222631)),
                                        textAlign: TextAlign.center,
                                      ).tr(),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: height(context) * 0.02,
                                ),
                                InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 6, horizontal: 18),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          color: ColorConstants()
                                              .primaryColorbtnAlways()),
                                      child: Text(
                                        'done',
                                        style: Styles.regular(
                                            color: ColorConstants.WHITE),
                                      ).tr()),
                                )
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  });
              Navigator.pop(context);
            } else {
              AlertsWidget.alertWithOkBtn(
                context: _scaffoldContext,
                onOkClick: () {
                  // Navigator.pushReplacement(context,
                  //     MaterialPageRoute(builder: (context) => homePage()));

                  if (widget.attemptAllowed == 1) {
                    Navigator.pop(context);
                    Navigator.pop(context, 1);
                    /*Navigator.pop(context);
                    Navigator.pushAndRemoveUntil(context,
                        MaterialPageRoute(builder: (context) => CompetitionDetail(
                          competitionId: widget.programId,
                          isEvent: widget.isEvent,
                        )));*/
                  } else {
                    Navigator.pop(context);
                  }
                },
                text: "${tr('app_assessment_submit_one')}",
              );
            }
            break;
          case ApiStatus.ERROR:
            if (widget.isVideoTypeQuiz) {
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                duration: Duration(milliseconds: 500),
                content: Text('error').tr(),
              ));
              videoInterviewProvider?.updateUploadingStatus(isUploading: false);
            }

            Navigator.pop(context);

            _isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e) {}
  }

  //Subscription? _subscription;

  @override
  void initState() {
    widget._questionController = ScrollController();
    WidgetsBinding.instance.addObserver(this);
    if (widget._list.isNotEmpty && _fillTheBlankController.isEmpty) {
      _fillTheBlankController =
          List.generate(widget._list.length, (_) => TextEditingController());
    }

    super.initState();
    _downloadListener();
  }

  @override
  void dispose() {
    if (widget.isSubmit || widget._list.length == 0) {
      widget._stopWatchTimer.dispose();
      widget._allTimer?.cancel();
    }
    //_subscription?.unsubscribe();
    for (var controller in _fillTheBlankController) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Application(context);
    _authBloc = BlocProvider.of<HomeBloc>(context);
    return BlocManager(
      initState: (context) {
        if (widget.isVideoTypeQuiz && widget.isReview) {
          _authBloc.add(
            ReviewTestEvent(
              request: '${widget.contentId}?program_id=${widget.programId}',
            ),
          );
        } else if (widget._list.length == 0) {
          _authBloc.add(
            AttemptTestEvent(
              request: widget.contentId.toString(),
            ),
          );
        }
      },
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is AttemptTestState) _handleAReviewTestResponse(state);
          if (state is SaveAnswerState) _handleSaveAnswerResponse(state);
          if (state is SubmitAnswerState) _handleSubmitAnswerResponse(state);
          // if (state is UploadImageState) _handleUploadImageResponse(state);
          if (state is ReviewTestState) _handleReviewTestResponse(state);
        },
        child: Builder(builder: (_context) {
          _scaffoldContext = _context;
          return PopScope(
            canPop: !willPop,
            child: Scaffold(
              backgroundColor: ColorConstants.WHITE,
              key: _key,
              bottomNavigationBar:
                  widget._list.length == 0 || widget.isVideoTypeQuiz == true
                      ? SizedBox()
                      : _buildBottomAppBar(),
              body: GestureDetector(
                onTap: () {
                  FocusScope.of(context).requestFocus(new FocusNode());
                },
                child: SafeArea(
                  child: ScreenWithLoader(
                    body: widget._list.length == 0 &&
                            widget._reviewResplist.length == 0
                        ? Column(
                            children: [
                              _heading(false),
                              Center(
                                child:
                                    Text(_isLoading ? 'please_wait' : 'no_data')
                                        .tr(),
                              ),
                            ],
                          )
                        : widget.isVideoTypeQuiz == true
                            ? InterviewAssessment(
                                title: '$_title',
                                reviewRespList: widget._reviewResplist,
                                questionList: widget._list,
                                isReview: widget.isReview,
                                durationInMin: widget._durationMins ?? 0,
                                onProviderCreate: (provider) {
                                  debugPrint("on create");
                                  videoInterviewProvider = provider;
                                  debugPrint("on create 2");

                                  // _subscription = VideoCompress.compressProgress$
                                  //     .subscribe((progress) {
                                  //   debugPrint('progress: $progress');
                                  //   provider?.updateCompressionPer(per: progress);
                                  // });
                                },
                                onFinalSubmit: () {
                                  _submitAnswers(isInterviewSubmit: true);
                                },
                                onSubmit: (
                                    {required int questionId,
                                    required String userFile,
                                    required String userText,
                                    required int durationInSec}) {
                                  bool isSkip =
                                      userText == '' && userFile == '';
                                  videoInterviewProvider
                                          ?.questions[videoInterviewProvider!
                                              .currentQuestionIndex]
                                          .questionStatus =
                                      isSkip
                                          ? QuestionStatus.skipped
                                          : QuestionStatus.done;
                                  _authBloc.add(
                                    SaveAnswerEvent(
                                      request: SaveAnswerRequest(
                                          contentId:
                                              widget.contentId.toString(),
                                          durationSec: durationInSec.toString(),
                                          questionId: questionId.toString(),
                                          userFile: userFile,
                                          optionId: [],
                                          skip: isSkip ? 1 : 0,
                                          answerStatement: userText,
                                          questionSequence: _questionSequence),
                                    ),
                                  );
                                })
                            : _content(),
                    isLoading: _isLoading,
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  _saveClick() {
    if (widget._savedAnswer == false) {
      widget._savedAnswer = true;

      widget.attemptList[
              widget._list[widget._currentQuestion].question!.questionId!] =
          widget._list[widget._currentQuestion].question!.selectedOption
              .isNotEmpty;

      _authBloc.add(
        SaveAnswerEvent(
          request: SaveAnswerRequest(
              contentId: widget.contentId.toString(),
              durationSec: widget._stopWatchTimer.secondTime.value.toString(),
              questionId: widget
                  ._list[widget._currentQuestion].question!.questionId
                  .toString(),
              /*optionId: questionType == '10' || questionType == '11' ? widget._list[widget._currentQuestion].question!.fillStringValue
                : widget._list[widget._currentQuestion].question!.selectedOption,*/
              optionId: questionType == '10' || questionType == '11'
                  ? widget
                      ._list[widget._currentQuestion].question!.selectedOption
                  : widget
                      ._list[widget._currentQuestion].question!.selectedOption,
              answerStatement: widget._list[widget._currentQuestion].question!
                          .fillStringValue!.length !=
                      0
                  ? widget._list[widget._currentQuestion].question!
                      .fillStringValue!.first
                  : '',
              questionSequence: _questionSequence),
        ),
      );
      if (widget._list[widget._currentQuestion].isBookmark) {
        if (widget
            ._list[widget._currentQuestion].question!.selectedOption.isEmpty) {
          widget._list[widget._currentQuestion].color = ColorConstants.REVIEWED;
        } else {
          widget._list[widget._currentQuestion].color =
              ColorConstants.ANSWERED_REVIEWS;
        }
      } else {
        if (widget
            ._list[widget._currentQuestion].question!.selectedOption.isEmpty) {
          widget._list[widget._currentQuestion].color =
              ColorConstants.NOT_ANSWERED;
        } else {
          widget._list[widget._currentQuestion].color = ColorConstants.ANSWERED;
        }
      }
      widget._list[widget._currentQuestion].question!.timeTaken =
          widget._stopWatchTimer.secondTime.value;
      _setTimer();

      if (((widget._list.length - 1) == widget._currentQuestion)) {
        // showDialog(
        //   context: context,
        //   builder: (BuildContext context) => AssessmentYourAnswersPage(
        //     contentId: widget.contentId,
        //     isReview: widget.isReview,
        //     isOptionSelected: widget._isOptionSelected,
        //     sendValue: (value) {
        //       setState(() {
        //         willPop = value;
        //       });
        //     },
        //   ),
        // );

        Log.v('Count List:---${widget.attemptList.length}');

        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (BuildContext context) => AssessmentYourAnswersPage(
                      contentId: widget.contentId,
                      isReview: widget.isReview,
                      attemptList: widget.attemptList,
                      isOptionSelected: widget._isOptionSelected,
                      programId: widget.programId,
                      attemptAllowed: widget.attemptAllowed,
                      disableBackTracking: _disableBackTracking,
                      sendValue: (value) {
                        setState(() {
                          willPop = value;
                        });
                      },
                    ))).then((value) {
          widget._savedAnswer = false;
          widget._isOptionSelected = false;
        });
      }
    }
  }

  void _setTimer() {
    if ((widget._list.length - 1) != widget._currentQuestion) {
      if (widget._list[widget._currentQuestion + 1].question?.timeTaken !=
              null &&
          widget._list[widget._currentQuestion + 1].question!.timeTaken != 0) {
        widget._stopWatchTimer.setPresetSecondTime(
            widget._list[widget._currentQuestion + 1].question!.timeTaken!);
      } else {
        //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
        //widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
        widget._stopWatchTimer.onResetTimer();
        widget._stopWatchTimer.onStartTimer();
      }
      widget.pageChange = false;
    }
  }

  _heading(bool iscontent) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Row(
        children: [
          TapWidget(
            onTap: () {
              if (iscontent) {
                AlertsWidget.alertWithOkCancelBtn(
                  context: _scaffoldContext,
                  onOkClick: () {
                    widget._stopWatchTimer.onResetTimer();
                    //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
                    widget._allTimer!.cancel();
                    widget._isEverythingOver = true;
                    _saveClick();
                    //_submitAnswers();
                  },
                  text: "${tr('Yapp_assessment_submit_two')}",
                  title: "${tr('finish_test')}",
                );
              } else {
                Navigator.pop(context);
              }
            },
            child: Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(10))),
              child: Icon(
                Icons.arrow_back,
                color: ColorConstants.DARK_BLUE,
              ),
            ),
          ),
          Expanded(
              child: Text(
            _title ?? "",
            textAlign: TextAlign.center,
            style: Styles.regular(size: 20),
          ))
        ],
      ),
    );
  }

  Widget _content() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _size(),
          _heading(true),
          _timerSubmit(),
          _size(),
          Expanded(
            child: Container(
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 19),
                    child: Divider(),
                  ),
                  _pageView(),
                ],
              ),
            ),
          ),
          ((widget._list.length - 1) == widget._currentQuestion)
              ? Padding(
                  padding: const EdgeInsets.only(left: 10.0, bottom: 8.0),
                  child: Text(
                    'attempt_save_msg',
                    style: TextStyle(color: Colors.red, fontSize: 12),
                    textAlign: TextAlign.center,
                  ).tr(),
                )
              : SizedBox(),
        ],
      ),
    );
  }

  _pageView() {
    return Expanded(
      child: Container(
        child: PageView.builder(
          itemBuilder: (context, index) {
            return widget._isResumedLoading
                ? Center(
                    child: CircularProgressIndicator(),
                  )
                : _pageItem(widget._list[index], index);
          },
          onPageChanged: (pageNumber) {
            setState(() {
              widget.currentSection = 0;
              // widget._currentQuestionId =
              widget._list[pageNumber].question!.questionId;
              widget._currentQuestion = pageNumber;
              widget._list[pageNumber].isVisited = 1;
              final questionsLength = widget._list.length;
              Utility.waitForMili(200).then((value) {
                if (widget._currentQuestion + 2 >= questionsLength * .6) {
                  if (widget._questionController!.position.pixels !=
                      widget._questionController!.position.maxScrollExtent) {
                    widget._questionController!.jumpTo(
                        ((widget._currentQuestion + 2) * 30).toDouble());
                  }
                } else if ((widget._currentQuestion + 2) <=
                    questionsLength * .3) {
                  if (widget._questionController!.position.pixels != 0) {
                    widget._questionController!.jumpTo(0);
                  }
                }
              });
            });
          },
          controller: widget._pageViewController,
          itemCount: widget._list.length,
          physics: NeverScrollableScrollPhysics(),
        ),
      ),
    );
  }

  _pageItem(TestAttemptBean testAttemptBean, int? index) {
    return Container(
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            testAttemptBean.question!.questionTimer != null &&
                    testAttemptBean.question!.questionTimer != 0
                ? Padding(
                    padding: const EdgeInsets.only(
                        left: 10.0, right: 10.0, bottom: 10.0),
                    child: CountdownProgressIndicator(
                      duration: testAttemptBean.question!.questionTimer! - 1,
                      onTimerEnd: () {
                        _saveClick();
                        widget.isSavedManually = true;
                      },
                    ),
                  )
                : SizedBox(),

//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 20),
//               child: Text(
//                 testAttemptBean.question!.question ?? "",
//                 style: Styles.textExtraBold(size: 16),
//               ),
//             ),

            //singh

            //singh Add New 28/02-2025
            testAttemptBean.question?.questionLabel?.image != null &&
                    testAttemptBean.question?.questionLabel?.image != ''
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.network(
                      '${testAttemptBean.question?.questionLabel?.image}',
                      height: 200,
                      fit: BoxFit.fitWidth,
                      width: MediaQuery.of(context).size.width,
                    ),
                  )
                : SizedBox(),
            //TODO: Show description
            testAttemptBean.question!.questionLabel?.description != null &&
                    testAttemptBean.question!.questionLabel?.description != ''
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Utility().isHtml(testAttemptBean
                                .question!.questionLabel?.description ??
                            "")
                        ? Utility().isMathEquation(testAttemptBean
                                    .question!.questionLabel?.description ??
                                '')
                            ? DynamicHeightWebView(
                                htmlContent:
                                    '<div id="masterg_content_view">${testAttemptBean.question!.questionLabel?.description}</div>',
                                zoomLevel: 2.5,
                                minHeight: 80,
                                maxHeight: 300,
                                onHeightChanged: (height) {
                                  setState(() {
                                    _webViewHeights[
                                            'q_desc_${testAttemptBean.question!.questionId}'] =
                                        height;
                                  });
                                },
                                loadingWidget: SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: ColorConstants().primaryColor(),
                                  ),
                                ),
                              )
                            : Html(
                                data:
                                    """${testAttemptBean.question!.questionLabel?.description}""",
                                style: {
                                  "table": Style(
                                    border: Border.all(
                                        color: Colors.grey, width: 0.5),
                                    //padding: EdgeInsets.all(0),
                                    padding: HtmlPaddings.all(0),
                                  ),
                                  "td": Style(
                                    //padding: EdgeInsets.all(0),
                                    padding: HtmlPaddings.all(0),
                                    border: Border.all(
                                        color: Colors.grey, width: 0.5),
                                  ),
                                },
                                // customRenders: {
                                //   tableMatcher(): tableRender(),
                                // },
                              )
                        : Text(
                            '${testAttemptBean.question!.questionLabel?.description}'),
                  )
                : SizedBox(),

            //TODO: Show questions
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Utility().isHtml(testAttemptBean.question!.question ?? "")
                  ? Utility().isMathEquation(
                          testAttemptBean.question!.question ?? '')
                      ? DynamicHeightWebView(
                          htmlContent:
                              '<div id="masterg_content_view">${testAttemptBean.question!.question}</div>',
                          zoomLevel: 2.5,
                          minHeight: 80,
                          maxHeight: 300,
                          onHeightChanged: (height) {
                            setState(() {
                              _webViewHeights[
                                      'q_${testAttemptBean.question!.questionId}'] =
                                  height;
                            });
                          },
                          loadingWidget: SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: ColorConstants().primaryColor(),
                              )),
                        )
                      : Html(
                          data: """${testAttemptBean.question!.question}""",
                          style: {
                            "table": Style(
                              border:
                                  Border.all(color: Colors.grey, width: 0.5),
                              //padding: EdgeInsets.all(0),
                              padding: HtmlPaddings.all(0),
                            ),
                            "td": Style(
                              //padding: EdgeInsets.all(0),
                              padding: HtmlPaddings.all(0),
                              border:
                                  Border.all(color: Colors.grey, width: 0.5),
                            ),
                          },
                          // customRenders: {
                          //   tableMatcher(): tableRender(),
                          // },
                        )
                  : Text(
                      testAttemptBean.question!.question ?? "",
                      style: Styles.textExtraBold(size: 16),
                    ),
            ),

            _size(height: 5),
            if (testAttemptBean.question!.questionImage != null)
              for (int i = 0;
                  i < testAttemptBean.question!.questionImage!.length;
                  i++)
                if (testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.mp4') ||
                    testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.mp3') ||
                    testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.ogg') ||
                    testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.wav'))
                  Container(
                    height: testAttemptBean.question!.questionImage![i]
                                .toString()
                                .contains('.mp3') ||
                            testAttemptBean.question!.questionImage![i]
                                .toString()
                                .contains('.ogg') ||
                            testAttemptBean.question!.questionImage![i]
                                .toString()
                                .contains('.wav')
                        ? 100
                        : 300,
                    width: MediaQuery.of(context).size.width,
                    alignment: Alignment.center,
                    child: Center(
                      child: InAppWebView(
                        initialSettings: InAppWebViewSettings(
                          mediaPlaybackRequiresUserGesture: true,
                          useShouldOverrideUrlLoading: true,
                          allowsInlineMediaPlayback: true,
                          allowsLinkPreview: false,
                        ),
                        initialUrlRequest: URLRequest(
                            url: WebUri(
                                testAttemptBean.question!.questionImage![i])),
                        onLoadStop: (controller, url) {
                          // Inject JavaScript to mute all video and audio elements
                          controller.evaluateJavascript(source: """
            document.querySelectorAll('video, audio').forEach(media => {
            
              document.body.style.backgroundColor = "#FFFFFF";
            });
          """);
                        },
                      ),
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.network(
                      testAttemptBean.question!.questionImage![i],
                      height: 200,
                      fit: BoxFit.fitWidth,
                      width: MediaQuery.of(context).size.width,
                    ),
                  ),
            _size(height: 10),
            _solutionType(testAttemptBean.question!.questionTypeId.toString(),
                testAttemptBean, index),
            _size(height: 10),
          ],
        ),
      ),
    );
  }

  _size({double height = 10}) {
    return SizedBox(
      height: height,
    );
  }

  String _getAllTime() {
    String _timeH = "";
    String _timeM = "";
    String _timeS = "";
    var localTime;

    localTime = widget._durationMins! - widget._allTimer!.tick;
    if ((localTime / 3600).truncate() < 10) {
      _timeH = "0${(localTime / 3600).truncate()}";
    } else {
      _timeH = (localTime / 3600).truncate().toString();
    }
    _timeM = (((localTime / 60).truncate()) % 60).toString().padLeft(2, '0');
    if ((localTime % 60).truncate() < 10) {
      _timeS = "0${(localTime % 60).truncate()}";
    } else {
      _timeS = (localTime % 60).truncate().toString();
    }

    if (widget._allTimer!.tick == widget._durationMins && !widget.isSubmit) {
      _submitAnswers();
    }

    return "$_timeH:$_timeM:$_timeS";
  }

  _timerSubmit() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 0),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.08),
                      blurRadius: 32,
                      offset: Offset(0, 4))
                ]),
            child: Text(
              "${tr('time_left')} : ${_getAllTime()}",
              style: Styles.textBold(size: 16),
            ),
          ),
          Spacer(),
        ],
      ),
    );
  }

  // _questionCount() {
  //   return widget._list.length == 0
  //       ? SizedBox()
  //       : Padding(
  //           padding: const EdgeInsets.symmetric(horizontal: 19),
  //           child: Container(
  //             height: 60,
  //             width: MediaQuery.of(_scaffoldContext).size.width,
  //             child: ListView.builder(
  //                 controller: widget._questionController,
  //                 shrinkWrap: true,
  //                 physics: ClampingScrollPhysics(),
  //                 itemBuilder: (context, index) {
  //                   return TapWidget(
  //                     onTap: () {
  //                       if (widget._isOptionSelected) {
  //                         AlertsWidget.alertWithOkBtn(
  //                             context: _scaffoldContext,
  //                             text: "${tr('app_assessment_submit_three')}");
  //                         return;
  //                       }
  //                       if (widget._currentQuestionId ==
  //                           widget._list[index].question!.questionId) {
  //                         return;
  //                       }
  //                       for (int i = 0; i < widget._list.length; i++) {
  //                         if (widget._list[i].question!.questionId ==
  //                             widget._list[index].question!.questionId) {
  //                           widget._currentQuestionId =
  //                               widget._list[index].question!.questionId;
  //                           widget._currentQuestion = i;
  //                           //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
  //                           widget._stopWatchTimer.onResetTimer();
  //                           widget._pageViewController.animateToPage(i,
  //                               duration: Duration(milliseconds: 100),
  //                               curve: Curves.ease);
  //                           Utility.waitForMili(200).then((value) {
  //                             widget._stopWatchTimer = StopWatchTimer();

  //                             widget._stopWatchTimer.setPresetSecondTime(widget
  //                                 ._list[widget._currentQuestion]
  //                                 .question!
  //                                 .timeTaken!);
  //                             widget._stopWatchTimer.onStartTimer();
  //                             // widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
  //                           });

  //                           break;
  //                         }
  //                       }
  //                     },
  //                     child: Padding(
  //                       padding: const EdgeInsets.only(right: 32),
  //                       child: Container(
  //                         width: 35,
  //                         height: 35,
  //                         decoration: BoxDecoration(
  //                           shape: BoxShape.circle,
  //                           boxShadow: [
  //                             BoxShadow(
  //                                 color: Colors.grey,
  //                                 offset: Offset(0, 8),
  //                                 blurRadius: 30)
  //                           ],
  //                           color: widget._list[index].color,
  //                         ),
  //                         alignment: Alignment.center,
  //                         child: Text(
  //                           "${index + 1}",
  //                           style: index == widget._currentQuestion
  //                               ? Styles.textBold()
  //                               : Styles.textLight(),
  //                         ),
  //                       ),
  //                     ),
  //                   );
  //                 },
  //                 scrollDirection: Axis.horizontal,
  //                 itemCount: widget._list.length),
  //           ),
  //         );
  // }

  // _questionNumber(TestAttemptBean testAttemptBean) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 20),
  //     child: Row(
  //       children: [
  //         Text(
  //           "${tr('q')}.${(widget._currentQuestion + 1).toString().padLeft(2, '0')}",
  //           style: Styles.textRegular(size: 22),
  //         ),
  //         SizedBox(
  //           width: 10,
  //         ),
  //         Padding(
  //           padding: const EdgeInsets.only(top: 0.0),
  //           child: TapWidget(
  //             onTap: () {
  //               widget._list[widget._currentQuestion].isBookmark =
  //                   !widget._list[widget._currentQuestion].isBookmark;
  //               if (widget._list[widget._currentQuestion].isBookmark) {
  //                 if (widget._list[widget._currentQuestion].question!
  //                     .selectedOption.isEmpty) {
  //                   widget._list[widget._currentQuestion].color =
  //                       ColorConstants.REVIEWED;
  //                 } else {
  //                   widget._list[widget._currentQuestion].color =
  //                       ColorConstants.ANSWERED_REVIEWS;
  //                 }
  //               } else {
  //                 if (widget._list[widget._currentQuestion].question!
  //                     .selectedOption.isEmpty) {
  //                   widget._list[widget._currentQuestion].color =
  //                       ColorConstants.NOT_ANSWERED;
  //                 } else {
  //                   widget._list[widget._currentQuestion].color =
  //                       ColorConstants.ANSWERED;
  //                 }
  //               }
  //               setState(() {});
  //             },
  //             child: Icon(
  //               widget._list[widget._currentQuestion].isBookmark
  //                   ? Icons.bookmark_outlined
  //                   : Icons.bookmark_border_outlined,
  //               color: ColorConstants.REVIEWED,
  //               size: 25,
  //             ),
  //           ),
  //         ),
  //         SizedBox(
  //           width: 10,
  //         ),
  //         Visibility(
  //           visible: (widget._list[widget._currentQuestion].question
  //                       ?.selectedOption.length ??
  //                   0) >
  //               0,
  //           child: TapWidget(
  //             onTap: () {
  //               setState(() {
  //                 widget._list[widget._currentQuestion].question!.selectedOption
  //                     .clear();
  //                 for (var data in widget
  //                     ._list[widget._currentQuestion].question!.options!) {
  //                   data.selected = false;
  //                 }
  //                 widget._isOptionSelected = false;
  //               });
  //             },
  //             child: Text(
  //               'clear_response',
  //               style: Styles.textBold(),
  //             ).tr(),
  //           ),
  //         ),
  //         Spacer(),
  //       ],
  //     ),
  //   );
  // }

  _solutionType(String type, TestAttemptBean testAttemptBean, int? index) {
    qunType = type;
    switch (type) {
      case "1":
        return _multipleChoose(testAttemptBean); //MULTIPLE_CHOICE

      case "2":
        //return _options(testAttemptBean); //SINGLE_INTEGER //hide 3 oct 2024 for add new future MQR
        return _multipleSelection(testAttemptBean); //MULTIPLE Selection MQR

      case "3":
        return _chooseOne(testAttemptBean); //MULTIPLE_RESPONSE

      case "4":
        return _chooseOne(testAttemptBean); //FILL_IN_THE_BLANK

      case "5":
        return _chooseOne(testAttemptBean); //TRUE_FALSE

      case "6":
      //return _subjective(testAttemptBean); //SUBJECTIVE

      case "7":
        return Container(); //MATCHING
      case "8":
        return Container(); //MATCHING
      case "9":
        //return _dragonDropMatchAnswer(testAttemptBean); //DRAGON DROP MATCH ANSWER 24-Oct-2024
        return _matchingAnswer(
            testAttemptBean); //DRAGON DROP MATCH ANSWER 24-Oct-2024

      case "10":
        return _fillInTheBlanks(
            testAttemptBean, type, index); //FILL IN THE BLANKS

      case "11":
        return _subjectiveText(
            testAttemptBean, type, index); //Text Question Answers

      case "12":
        return _shortAnswer(testAttemptBean, type, index);

      case "13":
        return _multipleMCQShortAnswer(testAttemptBean, type, index);

      default:
        return Container(); //MATCHING
    }
  }

  Future download2(String url, String savePath) async {
    try {
      _key.currentState!.showSnackBar(
        SnackBar(
          content: Text(
            'downloading_start',
            style: Styles.boldWhite(),
          ).tr(),
          backgroundColor: ColorConstants.BLACK,
          duration: Duration(seconds: 2),
        ),
      );
      // final taskId =
      await FlutterDownloader.enqueue(
        url: url,
        savedDir: savePath,
        showNotification: true,
        headers: {"auth": "test_for_sql_encoding"},
        openFileFromNotification: true,
      );
    } catch (e) {
      Log.v(e);
    }
  }

  // static void downloadCallback(
  //     String id, DownloadTaskStatus status, int progress) {}
  // // static void downloadCallback(
  //     String id, int status, int progress) {}

  // ReceivePort _port = ReceivePort();

  _downloadListener() {
    // FlutterDownloader.registerCallback(downloadCallback);
  }

  // _options(TestAttemptBean testAttemptBean) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 20),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Padding(
  //           padding: const EdgeInsets.symmetric(vertical: 20),
  //           child: Text(
  //             testAttemptBean.question!.questionType ?? "",
  //             style: Styles.textBold(size: 18),
  //           ),
  //         ),
  //         Column(
  //           children: List.generate(
  //             widget._list[widget._currentQuestion].question!.options!.length,
  //             (index) => Column(
  //               children: [
  //                 TapWidget(
  //                   onTap: () {
  //                     widget._isOptionSelected = true;
  //                     setState(() {
  //                       widget._list[widget._currentQuestion].question!
  //                           .selectedOption
  //                           .clear();
  //                       widget._list[widget._currentQuestion].question!
  //                           .selectedOption
  //                           .add(widget._list[widget._currentQuestion].question!
  //                               .options![index].optionId);
  //                       for (var data = 0;
  //                           data <
  //                               widget._list[widget._currentQuestion].question!
  //                                   .options!.length;
  //                           data++) {
  //                         if (widget._list[widget._currentQuestion].question!
  //                                 .options![data].optionId ==
  //                             widget._list[widget._currentQuestion].question!
  //                                 .selectedOption.first) {
  //                           widget._list[widget._currentQuestion].question!
  //                               .options![data].selected = true;
  //                         } else {
  //                           widget._list[widget._currentQuestion].question!
  //                               .options![data].selected = false;
  //                         }
  //                       }
  //                     });
  //                   },
  //                   child: Container(
  //                     decoration: BoxDecoration(
  //                         color: widget._list[widget._currentQuestion].question!
  //                                 .options![index].selected
  //                             ? ColorConstants.SELECTED_GREEN
  //                             : Colors.white,
  //                         borderRadius: BorderRadius.circular(8),
  //                         boxShadow: [
  //                           BoxShadow(
  //                               color: Color.fromRGBO(0, 0, 0, 0.05),
  //                               blurRadius: 16,
  //                               offset: Offset(0, 8))
  //                         ]),
  //                     child: Container(
  //                       width: MediaQuery.of(_scaffoldContext).size.width,
  //                       alignment: Alignment.center,
  //                       padding: const EdgeInsets.symmetric(
  //                           horizontal: 15, vertical: 25),
  //                       child: Text(
  //                         widget._list[widget._currentQuestion].question!
  //                             .options![index].optionStatement!,
  //                         style: widget._list[widget._currentQuestion].question!
  //                                 .options![index].selected
  //                             ? Styles.boldWhite(size: 18)
  //                             : Styles.textRegular(
  //                                 size: 18,
  //                               ),
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //                 _size(height: 20),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  /// TODO: Dragon Drop Match Answer 24-Oct-2024
  // _dragonDropMatchAnswer(TestAttemptBean testAttemptBean) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 20),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Padding(
  //           padding: const EdgeInsets.only(top: 15),
  //           child: Text(
  //             'question_type',
  //             style: Styles.textRegular(size: 12, color: Colors.grey),
  //           ).tr(),
  //         ),
  //         Padding(
  //           padding: const EdgeInsets.symmetric(vertical: 1),
  //           child: Text(
  //             testAttemptBean.question!.questionType ?? "",
  //             style: Styles.textRegular(size: 18),
  //           ),
  //         ),
  //         _size(height: 20),
  //         ReorderableListView(
  //           shrinkWrap: true,
  //           physics: const BouncingScrollPhysics(),
  //           onReorder: (int oldIndex, int newIndex) {
  //             setState(() {
  //               // Ensure the newIndex is always valid
  //               if (newIndex > oldIndex) {
  //                 newIndex -= 1;
  //               }

  //               // Swap the items instead of shifting the list
  //               final itemToMove = widget._list[widget._currentQuestion]
  //                   .question!.options![oldIndex];
  //               final targetItem = widget._list[widget._currentQuestion]
  //                   .question!.options![newIndex];

  //               // Perform the swap
  //               widget._list[widget._currentQuestion].question!
  //                   .options![oldIndex] = targetItem;
  //               widget._list[widget._currentQuestion].question!
  //                   .options![newIndex] = itemToMove;
  //             });
  //           },
  //           // Generate the list of options
  //           children: List.generate(
  //             widget._list[widget._currentQuestion].question!.options!.length,
  //             (index) => Column(
  //               key: ValueKey(widget._list[widget._currentQuestion].question!
  //                   .options![index].optionId), // Important for reorder
  //               children: [
  //                 GestureDetector(
  //                   onTap: () {
  //                     print(
  //                         'onLongPressEnd---${widget._list[widget._currentQuestion].question!.options!.length}');
  //                     print(
  //                         'onLongPressEnd---${widget._list[widget._currentQuestion].question!.options![index].optionStatement!}');
  //                     print(
  //                         'onLongPressEnd---${widget._list[widget._currentQuestion].question!.selectedOption}');
  //                     print(
  //                         'onLongPressEnd---${widget._list[widget._currentQuestion].question!.correctAnswerStatement}');
  //                   },
  //                   child: Container(
  //                     decoration: BoxDecoration(
  //                       color: widget._list[widget._currentQuestion].question!
  //                               .options![index].selected
  //                           ? ColorConstants.SELECTED_GREEN
  //                           : Colors.white,
  //                       borderRadius: BorderRadius.circular(8),
  //                       border: Border.all(
  //                         color: widget._list[widget._currentQuestion].question!
  //                                 .options![index].selected
  //                             ? ColorConstants.SELECTED_GREEN
  //                             : Colors.grey,
  //                       ),
  //                     ),
  //                     child: Container(
  //                       width: MediaQuery.of(_scaffoldContext).size.width,
  //                       padding: const EdgeInsets.symmetric(
  //                           horizontal: 15, vertical: 12),
  //                       child: Text(
  //                         widget._list[widget._currentQuestion].question!
  //                             .options![index].optionStatement!,
  //                         style: widget._list[widget._currentQuestion].question!
  //                                 .options![index].selected
  //                             ? Styles.boldWhite(size: 18)
  //                             : Styles.textRegular(
  //                                 size: 14, color: Colors.black),
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //                 _size(height: 12),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  void shuffleListOnce() {
    if (widget._list[widget._currentQuestion].question?.correctAnswerStatement!
            .length !=
        0) {
      widget._list[widget._currentQuestion].question?.correctAnswerStatement
          ?.sort((a, b) {
        return Random().nextInt(2) == 0 ? -1 : 1;
      });
    }
  }

  _matchingAnswer(TestAttemptBean testAttemptBean) {
    // Ensure that the question object is not null
    final currentQuestion = widget._list[widget._currentQuestion].question;
    if (currentQuestion == null) {
      return Container(); // or handle the null case as appropriate
    }

    // Ensure selectedMatchingOptionForIndex is initialized
    currentQuestion.selectedMatchingOptionForIndex ??= {};

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: Text(
              'question_type',
              style: Styles.textRegular(size: 12, color: Colors.grey),
            ).tr(),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              currentQuestion.questionType ?? "",
              style: Styles.textRegular(size: 18),
            ),
          ),
          _size(height: 20),
          Column(
            children: List.generate(
              currentQuestion.options!.length,
              (index) {
                return Column(
                  children: [
                    // Each option container
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey),
                      ),
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 15, vertical: 12),
                      child: Utility().isMathEquation(widget
                                  ._list[widget._currentQuestion]
                                  .question!
                                  .options![index]
                                  .optionStatement ??
                              "")
                          ? DynamicHeightWebView(
                              htmlContent:
                                  '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"><div id="masterg_content_view" style="word-wrap: break-word; font-size: 14px; line-height: 1.4; padding: 8px; margin: 0; overflow: visible;">${widget._list[widget._currentQuestion].question!.options![index].optionStatement ?? ""}</div>',
                              zoomLevel: 1.0, // Already scaled in HTML
                              minHeight: 60,
                              maxHeight: 200,

                              onHeightChanged: (height) {
                                setState(() {
                                  _webViewHeights[
                                          'match_opt_${widget._list[widget._currentQuestion].question!.options![index].optionId}'] =
                                      height;
                                });
                              },
                              loadingWidget: SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: ColorConstants().primaryColor(),
                                ),
                              ),
                            )
                          : Text(
                              currentQuestion.options![index].optionStatement!,
                              style: Styles.textRegular(
                                  size: 14, color: Colors.black),
                            ),
                    ),
                    // Horizontal scroll for correctAnswerStatement
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: List.generate(
                            currentQuestion.correctAnswerStatement!.length,
                            (indexIn) {
                          return Column(
                            children: [
                              TapWidget(
                                onTap: () {
                                  setState(() {
                                    // Toggle selection for this option
                                    String selectedOption = indexIn.toString();
                                    currentQuestion
                                            .selectedMatchingOptionForIndex[
                                        index] ??= [];
                                    List<String> selectedForCurrentOption =
                                        currentQuestion
                                                .selectedMatchingOptionForIndex[
                                            index]!;

                                    // Toggle logic
                                    if (selectedForCurrentOption
                                        .contains(selectedOption)) {
                                      selectedForCurrentOption
                                          .remove(selectedOption);
                                    } else {
                                      selectedForCurrentOption.clear();
                                      selectedForCurrentOption
                                          .add(selectedOption);
                                    }
                                    widget._list[widget._currentQuestion]
                                        .question!.selectedOption
                                        .clear();
                                    // Flatten the selected options and parse them to integers
                                    List<int?> selectedOptions = currentQuestion
                                        .selectedMatchingOptionForIndex.values
                                        .expand((list) => list)
                                        .map((item) => int.tryParse(item))
                                        .where((item) =>
                                            item !=
                                            null) // Ensure no null values
                                        .toList();

                                    // Add the selected options to selectedOption, allowing duplicates
                                    widget._list[widget._currentQuestion]
                                        .question!.selectedOption
                                        .addAll(selectedOptions);
                                  });
                                },
                                child: Container(
                                  width: 220,
                                  margin: const EdgeInsets.all(10.0),
                                  decoration: BoxDecoration(
                                    color: currentQuestion
                                                .selectedMatchingOptionForIndex[
                                                    index]
                                                ?.contains(
                                                    indexIn.toString()) ==
                                            true
                                        ? ColorConstants.SELECTED_GREEN
                                        : Colors.white,
                                    borderRadius: BorderRadius.circular(100),
                                    border: Border.all(
                                      color: currentQuestion
                                                  .selectedMatchingOptionForIndex[
                                                      index]
                                                  ?.contains(
                                                      indexIn.toString()) ==
                                              true
                                          ? ColorConstants.SELECTED_GREEN
                                          : Colors.grey,
                                    ),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 10, horizontal: 25),
                                  child: Text(
                                    '${currentQuestion.correctAnswerStatement![indexIn]}',
                                    style: TextStyle(
                                      color: currentQuestion
                                                  .selectedMatchingOptionForIndex[
                                                      index]
                                                  ?.contains(
                                                      indexIn.toString()) ==
                                              true
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        }),
                      ),
                    ),
                    _size(height: 12),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _multipleChoose(TestAttemptBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: Text(
              'question_type',
              style: Styles.textRegular(size: 12, color: Colors.grey),
            ).tr(),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textRegular(size: 18),
            ),
          ),
          _size(height: 20),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.options!.length,
              (index) => Column(
                children: [
                  GestureDetector(
                    onTap: () {
                      widget._isOptionSelected = true;
                      setState(() {
                        widget._list[widget._currentQuestion].question!
                            .selectedOption
                            .clear();

                        widget._list[widget._currentQuestion].question!
                            .selectedOption
                            .add(widget._list[widget._currentQuestion].question!
                                .options![index].optionId);
                        for (var data = 0;
                            data <
                                widget._list[widget._currentQuestion].question!
                                    .options!.length;
                            data++) {
                          if (widget._list[widget._currentQuestion].question!
                                  .options![data].optionId ==
                              widget._list[widget._currentQuestion].question!
                                  .selectedOption.first) {
                            widget._list[widget._currentQuestion].question!
                                .options![data].selected = true;
                          } else {
                            widget._list[widget._currentQuestion].question!
                                .options![data].selected = false;
                          }
                        }
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: widget._list[widget._currentQuestion].question!
                                .options![index].selected
                            ? ColorConstants.SELECTED_GREEN
                            : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: widget._list[widget._currentQuestion]
                                    .question!.options![index].selected
                                ? ColorConstants.SELECTED_GREEN
                                : Colors.grey),
                      ),
                      child: Column(
                        children: [
                          Container(
                            width: MediaQuery.of(_scaffoldContext).size.width,
                            //alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 12), //singh11
                            child: Utility().isHtml(widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .options![index]
                                        .optionStatement ??
                                    "")
                                ? Utility().isMathEquation(widget
                                            ._list[widget._currentQuestion]
                                            .question!
                                            .options![index]
                                            .optionStatement ??
                                        "")
                                    ? DynamicHeightWebView(
                                        htmlContent:
                                            '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.5, user-scalable=no"><div id="masterg_content_view" style="word-wrap: break-word; font-size: 14px;">${widget._list[widget._currentQuestion].question!.options![index].optionStatement ?? ""}</div>',
                                        zoomLevel: 1.0,
                                        minHeight: 60,
                                        maxHeight: 300,
                                        onHeightChanged: (height) {
                                          setState(() {
                                            _webViewHeights[
                                                    'opt_${widget._list[widget._currentQuestion].question!.options![index].optionId}'] =
                                                height;
                                          });
                                        },
                                        loadingWidget: SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color:
                                                ColorConstants().primaryColor(),
                                          ),
                                        ),
                                      )
                                    : Html(
                                        data:
                                            """${widget._list[widget._currentQuestion].question!.options![index].optionStatement!}""",
                                        style: {
                                          "table": Style(
                                            border: Border.all(
                                                color: Colors.grey, width: 0.5),
                                            //padding: EdgeInsets.all(0),
                                            padding: HtmlPaddings.all(0),
                                          ),
                                          "td": Style(
                                            //padding: EdgeInsets.all(0),
                                            padding: HtmlPaddings.all(0),
                                            border: Border.all(
                                                color: Colors.grey, width: 0.5),
                                          ),
                                        },
                                        // customRenders: {
                                        //   tableMatcher(): tableRender(),
                                        // },
                                      )
                                : Text(
                                    widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .options![index]
                                        .optionStatement!,
                                    style: widget._list[widget._currentQuestion]
                                            .question!.options![index].selected
                                        ? Styles.boldWhite(size: 18)
                                        : Styles.textRegular(
                                            size: 14, color: Colors.black),
                                  ),
                          ),
                          widget._list[widget._currentQuestion].question!
                                      .options![index].optionImage !=
                                  ''
                              ? Padding(
                                  padding: EdgeInsets.all(0),
                                  child: widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.mp4') ||
                                          widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.mp3') ||
                                          widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.ogg') ||
                                          widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.wav')
                                      ? Container(
                                          height: 100,
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width -
                                              40,
                                          margin: EdgeInsets.only(bottom: 10),
                                          child: Center(
                                            child: InAppWebView(
                                              initialSettings:
                                                  InAppWebViewSettings(
                                                mediaPlaybackRequiresUserGesture:
                                                    true,
                                                useShouldOverrideUrlLoading:
                                                    true,
                                                allowsInlineMediaPlayback: true,
                                                allowsLinkPreview: false,
                                              ),
                                              initialUrlRequest: URLRequest(
                                                  url: WebUri(
                                                      '${widget._list[widget._currentQuestion].question!.options![index].optionImage}')),
                                              onLoadStop: (controller, url) {
                                                // Inject JavaScript to mute all video and audio elements
                                                controller.evaluateJavascript(
                                                    source: """
            document.querySelectorAll('video, audio').forEach(media => {
              media.muted = true;
              media.pause(); 
              document.body.style.backgroundColor = "#FFFFFF";
            });
          """);
                                              },
                                            ),
                                          ),
                                        )
                                      : ClipRRect(
                                          borderRadius: BorderRadius.only(
                                            bottomLeft: Radius.circular(6.0),
                                            bottomRight: Radius.circular(6.0),
                                          ),
                                          child: Image.network(
                                            '${widget._list[widget._currentQuestion].question!.options![index].optionImage}',
                                            fit: BoxFit.fill,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            height: 200,
                                          ),
                                        ),
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  ),
                  _size(height: 12),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _multipleSelection(TestAttemptBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: Text(
              'question_type',
              style: Styles.textRegular(size: 12, color: Colors.grey),
            ).tr(),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textRegular(size: 18),
            ),
          ),
          _size(height: 20),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.options!.length,
              (index) => Column(
                children: [
                  TapWidget(
                    onTap: () {
                      widget._isOptionSelected = true;
                      setState(() {
                        // Get the current question
                        final currentQuestion =
                            widget._list[widget._currentQuestion].question!;
                        final optionId =
                            currentQuestion.options![index].optionId;

                        // Check if the option is already selected
                        if (currentQuestion.selectedOption.contains(optionId)) {
                          // If already selected, deselect it
                          currentQuestion.selectedOption.remove(optionId);
                          currentQuestion.options![index].selected = false;
                        } else {
                          // If not selected, select it
                          currentQuestion.selectedOption.add(optionId!);
                          currentQuestion.options![index].selected = true;
                        }
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: widget._list[widget._currentQuestion].question!
                                .options![index].selected
                            ? ColorConstants.SELECTED_GREEN
                            : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: widget._list[widget._currentQuestion]
                                    .question!.options![index].selected
                                ? ColorConstants.SELECTED_GREEN
                                : Colors.grey),
                      ),
                      child: Column(
                        children: [
                          Container(
                            width: MediaQuery.of(_scaffoldContext).size.width,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 12),
                            child: Utility().isMathEquation(widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .options![index]
                                        .optionStatement ??
                                    "")
                                ? DynamicHeightWebView(
                                    htmlContent:
                                        '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=no"><div id="masterg_content_view" style="word-wrap: break-word; font-size: 14px;">${widget._list[widget._currentQuestion].question!.options![index].optionStatement ?? ""}</div>',
                                    zoomLevel: 1.0,
                                    minHeight: 50,
                                    maxHeight: 200,
                                    onHeightChanged: (height) {
                                      setState(() {
                                        _webViewHeights[
                                                'static_opt_${widget._list[widget._currentQuestion].question!.options![index].optionId}'] =
                                            height;
                                      });
                                    },
                                    loadingWidget: SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: ColorConstants().primaryColor(),
                                      ),
                                    ),
                                  )
                                : Text(
                                    widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .options![index]
                                        .optionStatement!,
                                    style: widget._list[widget._currentQuestion]
                                            .question!.options![index].selected
                                        ? Styles.boldWhite(size: 18)
                                        : Styles.textRegular(
                                            size: 14, color: Colors.black),
                                  ),
                          ),
                          widget._list[widget._currentQuestion].question!
                                      .options![index].optionImage !=
                                  ''
                              ? Padding(
                                  padding: EdgeInsets.all(0),
                                  child: widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.mp4') ||
                                          widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.mp3') ||
                                          widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.ogg') ||
                                          widget
                                              ._list[widget._currentQuestion]
                                              .question!
                                              .options![index]
                                              .optionImage
                                              .toString()
                                              .contains('.wav')
                                      ? Container(
                                          height: 100,
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width -
                                              40,
                                          margin: EdgeInsets.only(bottom: 10),
                                          child: Center(
                                            child: InAppWebView(
                                              initialSettings:
                                                  InAppWebViewSettings(
                                                mediaPlaybackRequiresUserGesture:
                                                    true,
                                                useShouldOverrideUrlLoading:
                                                    true,
                                                allowsInlineMediaPlayback: true,
                                                allowsLinkPreview: false,
                                              ),
                                              initialUrlRequest: URLRequest(
                                                  url: WebUri(
                                                      '${widget._list[widget._currentQuestion].question!.options![index].optionImage}')),
                                              onLoadStop: (controller, url) {
                                                // Inject JavaScript to mute all video and audio elements
                                                controller.evaluateJavascript(
                                                    source: """
            document.querySelectorAll('video, audio').forEach(media => {
              media.muted = true;
              media.pause(); 
              document.body.style.backgroundColor = "#FFFFFF";
            });
          """);
                                              },
                                            ),
                                          ),
                                        )
                                      : ClipRRect(
                                          borderRadius: BorderRadius.only(
                                            bottomLeft: Radius.circular(6.0),
                                            bottomRight: Radius.circular(6.0),
                                          ),
                                          child: Image.network(
                                            '${widget._list[widget._currentQuestion].question!.options![index].optionImage}',
                                            fit: BoxFit.fill,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            height: 200,
                                          ),
                                        ),
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  ),
                  _size(height: 12),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _chooseOne(TestAttemptBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(top: 15),
              child: Text(
                'question_type',
                style: Styles.textRegular(size: 12, color: Colors.grey),
              ).tr()),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          _size(height: 15),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.options!.length,
              (index) => Column(
                children: [
                  TapWidget(
                    onTap: () {
                      widget._isOptionSelected = true;
                      setState(() {
                        widget._list[widget._currentQuestion].question!
                            .selectedOption
                            .clear();
                        widget._list[widget._currentQuestion].question!
                            .selectedOption
                            .add(widget._list[widget._currentQuestion].question!
                                .options![index].optionId);
                        for (var data = 0;
                            data <
                                widget._list[widget._currentQuestion].question!
                                    .options!.length;
                            data++) {
                          if (widget._list[widget._currentQuestion].question!
                                  .options![data].optionId ==
                              widget._list[widget._currentQuestion].question!
                                  .selectedOption.first) {
                            widget._list[widget._currentQuestion].question!
                                .options![data].selected = true;
                          } else {
                            widget._list[widget._currentQuestion].question!
                                .options![data].selected = false;
                          }
                        }
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          color: widget._list[widget._currentQuestion].question!
                                  .options![index].selected
                              ? ColorConstants.SELECTED_GREEN
                              : Colors.grey[300],
                          borderRadius: BorderRadius.circular(5),
                          boxShadow: [
                            BoxShadow(
                                color: Color.fromRGBO(0, 0, 0, 0.05),
                                blurRadius: 16,
                                offset: Offset(0, 8))
                          ]),
                      child: Container(
                        width: MediaQuery.of(_scaffoldContext).size.width,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 20),
                        child: Text(
                          widget._list[widget._currentQuestion].question!
                              .options![index].optionStatement!,
                          style: widget._list[widget._currentQuestion].question!
                                  .options![index].selected
                              ? Styles.boldWhite(size: 18)
                              : Styles.textRegular(
                                  size: 18,
                                ),
                        ),
                      ),
                    ),
                  ),
                  _size(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _fillInTheBlanks(
      TestAttemptBean testAttemptBean, String qusType, int? indexPage) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(top: 15),
              child: Text(
                'question_type',
                style: Styles.textRegular(size: 12, color: Colors.grey),
              ).tr()),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          _size(height: 15),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            TextField(
              controller: _fillTheBlankController[indexPage!],
              maxLines: 6,
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.white,
                focusColor: Colors.white,
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                      color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                  borderRadius: BorderRadius.circular(8),
                ),
                border: OutlineInputBorder(
                  borderSide: const BorderSide(
                      color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                      color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                  borderRadius: BorderRadius.circular(8),
                ),
                hintText: '${tr('type_your_answer')}',
                hintStyle: const TextStyle(color: Colors.grey),
              ),
              textInputAction: TextInputAction.newline,
              onChanged: (value) {
                widget._isOptionSelected = true;
                widget._list[widget._currentQuestion].question!.selectedOption
                    .clear();
                widget._list[widget._currentQuestion].question!.selectedOption
                    .add(1);

                questionType = qusType;
                widget._list[widget._currentQuestion].question!.fillStringValue!
                    .clear();
                widget._list[widget._currentQuestion].question!.fillStringValue!
                    .add(value);
              },
            ),
            _size(height: 20),
          ]),
        ],
      ),
    );
  }

  _subjectiveText(
      TestAttemptBean testAttemptBean, String qusType, int? indexPage) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(top: 15),
              child: Text(
                'question_type',
                style: Styles.textRegular(size: 12, color: Colors.grey),
              ).tr()),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          _size(height: 15),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            HtmlEditor(
              controller: _htmlController[indexPage!],
              htmlToolbarOptions: HtmlToolbarOptions(
                initiallyExpanded: true,
                renderBorder: true,
                renderSeparatorWidget: false,
                toolbarType: ToolbarType.nativeExpandable,
                //dropdownBoxDecoration: BoxDecoration(border: Border.all(color: Colors.blue)),
                toolbarPosition: ToolbarPosition
                    .aboveEditor, //required to place toolbar anywhere!

                defaultToolbarButtons: [
                  StyleButtons(),
                  FontSettingButtons(),
                  FontButtons(),
                  ColorButtons(),
                  //ListButtons(),
                  //ParagraphButtons(),
                  //InsertButtons(),
                  //OtherButtons(),
                ],
              ),
              callbacks: Callbacks(onChangeContent: (String? value) {
                print(
                    "onChangeContent text: ${value.toString().replaceAll('<p>', '').replaceAll('</p>', '')}");
                widget._isOptionSelected = true;
                widget._list[widget._currentQuestion].question!.selectedOption
                    .clear();
                widget._list[widget._currentQuestion].question!.selectedOption
                    .add(1);

                questionType = qusType;
                widget._list[widget._currentQuestion].question!.fillStringValue!
                    .clear();
                widget._list[widget._currentQuestion].question!.fillStringValue!
                    .add(value
                        .toString()
                        .replaceAll('<p>', '')
                        .replaceAll('</p>', ''));
              }),
              htmlEditorOptions: HtmlEditorOptions(
                hint: "Your text here...",
                shouldEnsureVisible: true,
                //initalText: "text content initial, if any",
              ),
              otherOptions: OtherOptions(
                decoration:
                    BoxDecoration(border: Border.all(color: Colors.grey)),
                height: 350,
              ),
            ),

            //TODO: Hide for Html editor
            // TextField(
            //   controller: _subjectiveController[indexPage!],
            //   maxLines: 6,
            //   decoration: InputDecoration(
            //     filled: true,
            //     fillColor: Colors.white,
            //     focusColor: Colors.white,
            //     focusedBorder: OutlineInputBorder(
            //       borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
            //       borderRadius: BorderRadius.circular(8),
            //     ),
            //     border: OutlineInputBorder(
            //       borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
            //       borderRadius: BorderRadius.circular(8),
            //     ),
            //     enabledBorder: OutlineInputBorder(
            //       borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
            //       borderRadius: BorderRadius.circular(8),
            //     ),
            //     hintText: '${tr('type_your_answer')}',
            //     hintStyle: const TextStyle(color: Colors.grey),
            //   ),
            //   textInputAction: TextInputAction.newline,
            //   onChanged: (value){
            //     widget._isOptionSelected = true;
            //     widget._list[widget._currentQuestion].question!.selectedOption.clear();
            //     widget._list[widget._currentQuestion].question!.selectedOption.add(1);
            //
            //     questionType = qusType;
            //     widget._list[widget._currentQuestion].question!.fillStringValue!.clear();
            //     widget._list[widget._currentQuestion].question!.fillStringValue!.add(value);
            //   },
            // ),
            _size(height: 20),
          ]),
        ],
      ),
    );
  }

  _shortAnswer(
      TestAttemptBean testAttemptBean, String qusType, int? indexPage) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(top: 15),
              child: Text(
                'question_type',
                style: Styles.textRegular(size: 12, color: Colors.grey),
              ).tr()),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          _size(height: 15),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            TextField(
              controller: _subjectiveController[indexPage!],
              maxLines: 6,
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.white,
                focusColor: Colors.white,
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                      color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                  borderRadius: BorderRadius.circular(8),
                ),
                border: OutlineInputBorder(
                  borderSide: const BorderSide(
                      color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                      color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                  borderRadius: BorderRadius.circular(8),
                ),
                hintText: '${tr('type_your_answer')}',
                hintStyle: const TextStyle(color: Colors.grey),
              ),
              textInputAction: TextInputAction.newline,
              onChanged: (value) {
                widget._isOptionSelected = true;
                widget._list[widget._currentQuestion].question!.selectedOption
                    .clear();
                widget._list[widget._currentQuestion].question!.selectedOption
                    .add(1);

                questionType = qusType;
                widget._list[widget._currentQuestion].question!.fillStringValue!
                    .clear();
                widget._list[widget._currentQuestion].question!.fillStringValue!
                    .add(value);
              },
            ),
            _size(height: 20),
          ]),
        ],
      ),
    );
  }

  _multipleMCQShortAnswer(
      TestAttemptBean testAttemptBean, String qusType, int? indexPage) {
    final html = testAttemptBean.question?.blankHtml ?? "";

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: QuestionOptionWebView(
        html: html,
        onValuesChanged: (values) {
          widget._isOptionSelected = true;
          widget._list[widget._currentQuestion].question!.selectedOption
              .clear();
          widget._list[widget._currentQuestion].question!.selectedOption.add(1);

          questionType = qusType;
          widget._list[widget._currentQuestion].question!.fillStringValue!
              .clear();
          widget._list[widget._currentQuestion].question!.fillStringValue!
              .add(values.toString());

          debugPrint("Question:- $indexPage auto values: ${values.toString()}");
        },
      ),
    );
  }

  void _submitAnswers({bool? isInterviewSubmit = false}) {
    if (widget.isVideoTypeQuiz == true && isInterviewSubmit == true) {
      _authBloc.add(SubmitAnswerEvent(request: widget.contentId.toString()));
    } else
      _authBloc.add(SubmitAnswerEvent(request: widget.contentId.toString()));
  }

  void _handlePreviousButton() {
    print('back Click');
    widget._pageViewController.previousPage(
      duration: Duration(milliseconds: 100),
      curve: Curves.ease,
    );
  }

  _buildBottomAppBar() {
    if ((widget._list.length - 1) == widget._currentQuestion) {
      widget.lastSave = true;
    }
    return BottomAppBar(
      elevation: 10,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            widget._currentQuestion == 0
                ? SizedBox(
                    width: 100,
                  )
                : _disableBackTracking == 1
                    ? SizedBox(
                        width: 100,
                      )
                    : TapWidget(
                        onTap: () {
                          if (widget._isOptionSelected) {
                            AlertsWidget.alertWithOkBtn(
                                context: _scaffoldContext,
                                onOkClick: () {
                                  widget.showSubmitDialog = true;
                                },
                                text: "${tr('back_previous')}");
                            return;
                          }
                          _handlePreviousButton();
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20),
                          decoration: BoxDecoration(),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                'assets/images/prev.svg',
                                width: 15,
                                height: 15,
                                allowDrawingOutsideViewBox: true,
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              Text(
                                'previous',
                                style: Styles.textBold(
                                    size: 16, color: Colors.black),
                              ).tr(),
                            ],
                          ),
                        ),
                      ),
            Container(
              width: 100,
              decoration: BoxDecoration(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "${(widget._currentQuestion + 1).toString() + "/" + widget._list.length.toString()}",
                    style: Styles.textBold(size: 16, color: Colors.black),
                  ),
                ],
              ),
            ),
            widget.isSavedManually == false
                ? TapWidget(
                    onTap: () {
                      Log.v('object save');
                      _saveClick();
                      setState(() {
                        widget.isSavedManually = true;
                      });
                    },
                    child: Container(
                      width: 100,
                      decoration: BoxDecoration(),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            ((widget._list.length - 1) ==
                                    widget._currentQuestion)
                                ? "save"
                                : "next",
                            style:
                                Styles.textBold(size: 16, color: Colors.black),
                          ).tr(),
                          const SizedBox(
                            width: 5,
                          ),
                          SvgPicture.asset(
                            'assets/images/next.svg',
                            width: 15,
                            height: 15,
                            allowDrawingOutsideViewBox: true,
                          ),
                        ],
                      ),
                    ),
                  )
                : SizedBox(
                    width: 100,
                  )
          ],
        ),
      ),
    );
  }
}

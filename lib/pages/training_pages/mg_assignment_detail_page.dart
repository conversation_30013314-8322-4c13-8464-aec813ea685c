import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/assignment_detail_response.dart';
import 'package:masterg/data/models/response/home_response/assignment_submissions_response.dart';
import 'package:masterg/data/providers/my_assignment_detail_provider.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/announecment_pages/full_video_page.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/training_pages/assignment_submissions.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/custom_progress_indicator.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;

import 'package:url_launcher/url_launcher.dart';

import '../../data/api/api_constants.dart';
import '../../data/models/response/auth_response/user_session.dart';
import '../../utils/config.dart';
import '../auth_pages/terms_and_condition_page.dart';
import '../ghome/widget/read_more.dart';

class MgAssignmentDetailPage extends StatefulWidget {
  final int? id;
  final String? status;
  MgAssignmentDetailPage({required this.id, this.status = ''});
  @override
  _MgAssignmentDetailPageState createState() => _MgAssignmentDetailPageState();
}

class _MgAssignmentDetailPageState extends State<MgAssignmentDetailPage> {
  File? file;
  final _userNotes = TextEditingController(text: "");
  MgAssignmentDetailProvider? assignmentDetailProvider;
  bool _isLoading = false;
  AssessmentDetails? data;
  List<SubmissionDetails>? _attempts = [];
  late ApiService api = ApiService();
  int? checkPlagiarismValue;

  @override
  void initState() {
    _getData();
    _downloadListener();
    super.initState();

    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      // String id = data[0];
      // DownloadTaskStatus status = data[1];
      // int progress = data[2];
      setState(() {});
    });

    //FlutterDownloader.registerCallback(downloadCallback as DownloadCallback);
    FlutterDownloader.registerCallback(downloadCallback);
  }

  static void downloadCallback(String id, int status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, status, progress]);
  }

  ReceivePort _port = ReceivePort();

  _downloadListener() async {
    await FlutterDownloader.initialize();
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) async {
      String? id = data[0];
      DownloadTaskStatus? status = data[1];
      int? progress = data[2];
      if (status == DownloadTaskStatus.complete &&
          progress == 100 &&
          id != null) {
        String query = "SELECT * FROM task WHERE task_id='" + id + "'";
        var tasks = await FlutterDownloader.loadTasksWithRawQuery(query: query);

        if (tasks != null) {
          Future.delayed(Duration(seconds: 2), () {
            FlutterDownloader.open(taskId: tasks.first.taskId);
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    assignmentDetailProvider = Provider.of<MgAssignmentDetailProvider>(context);
    return Scaffold(
        backgroundColor: ColorConstants.WHITE,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          iconTheme: IconThemeData(
            color: Colors.black,
          ),
          title: Text(
            tr('assignment'),
            style: TextStyle(color: Colors.black),
          ),
          backgroundColor: Colors.white,
          elevation: 0,
        ),
        body: BlocManager(
            initState: (c) {},
            child: BlocListener<HomeBloc, HomeState>(
              listener: (context, state) {
                if (state is AssignmentSubmissionsState) _handleResponse(state);
                if (state is GenerateSimilarityState)
                  _handleGenerateSimilarityResponse(state);
              },
              child: assignmentDetailProvider?.assignment != null
                  ? _buildBody()
                  : CustomProgressIndicator(true, ColorConstants.WHITE),
            )));
  }

  _buildBody() {
    return Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        child: SingleChildScrollView(
            child: Column(children: [
          _belowTitle(assignmentDetailProvider!),
          _body(assignmentDetailProvider!.assignment!),
          _buildListBody(),
        ])));
  }

  _buildListBody() {
    return Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height * 0.3,
        child: _isLoading
            ? Center(
                child: CustomProgressIndicator(true, ColorConstants.WHITE),
              )
            : _attempts!.isNotEmpty
                ? SingleChildScrollView(
                    child: Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height * 0.3,
                    child: ListView.builder(
                        itemCount: _attempts?.length,
                        itemBuilder:
                            (BuildContext context, int currentIndex) => Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Container(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.6,
                                            child: Column(
                                              crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                    '${_attempts![currentIndex].file!.split('/').last}',
                                                    overflow: TextOverflow.fade,
                                                    maxLines: 2,
                                                    softWrap: true,
                                                    style: Styles.regular(
                                                        size: 14)),
                                                Text(
                                                  '${_attempts![currentIndex].createdAt != null ? Utility.convertDateFromMillis(_attempts![currentIndex].createdAt!, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}',
                                                  style: Styles.regular(
                                                      size: 10,
                                                      color: _attempts![
                                                      currentIndex]
                                                          .createdAt! >=
                                                          assignmentDetailProvider!
                                                              .assignment!
                                                              .submissionDate!
                                                          ? ColorConstants.RED
                                                          : ColorConstants
                                                          .GREY_3),
                                                  textDirection:
                                                  ui.TextDirection.ltr,
                                                ),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                                data!.checkPlagiarism != null
                                                    ? Row(
                                                  children: [
                                                    /* Generate Launch Url*/
                                                    _attempts![currentIndex].pdfReportUrl != null
                                                        ? InkWell(
                                                      onTap:
                                                          () async {
                                                        setState(
                                                                () {
                                                              _isLoading =
                                                              true;
                                                            });
                                                        Map<String,
                                                            dynamic>
                                                        data = {
                                                          'submission_id':
                                                          _attempts![currentIndex]
                                                              .id,
                                                        };
                                                        try {
                                                          final response =
                                                          await api
                                                              .dio
                                                              .post(
                                                            checkPlagiarismValue !=
                                                                1
                                                                ? ApiConstants.GENERATE_SIMILARITY
                                                                : ApiConstants.LAUNCH_URL,
                                                            data: FormData.fromMap(
                                                                data), // If your API expects FormData
                                                            options:
                                                            Options(
                                                              method:
                                                              'POST',
                                                              headers: {
                                                                "Authorization":
                                                                "Bearer ${UserSession.userToken}",
                                                                ApiConstants.API_KEY:
                                                                ApiConstants().APIKeyValue(),
                                                              },
                                                              contentType:
                                                              "application/json", // If sending JSON
                                                              responseType:
                                                              ResponseType.json,
                                                            ),
                                                          );
                                                          if (response.statusCode ==
                                                              200 ||
                                                              response.statusCode ==
                                                                  201) {
                                                            setState(
                                                                    () {
                                                                  _isLoading =
                                                                  false;
                                                                });

                                                            if (checkPlagiarismValue !=
                                                                1) {
                                                              ScaffoldMessenger.of(context)
                                                                  .showSnackBar(SnackBar(
                                                                content:
                                                                Text('${response.data['message'].toString()}'),
                                                              ));
                                                            } else {
                                                              if (response.data['data']['lunch_url'] !=
                                                                  null) {
                                                                //launchUrl(Uri.parse('${response.data['data']['lunch_url']}'!), mode: LaunchMode.externalApplication);

                                                                /*final Uri url = Uri.parse('${response.data['data']['lunch_url']}');
                                                            if (!await launchUrl(url)) {
                                                              throw Exception('Could not launch');
                                                            }*/

                                                                final String
                                                                url =
                                                                    '${response.data['data']['lunch_url']}';
                                                                if (await canLaunchUrl(Uri.parse(url))) {
                                                                  await launchUrl(Uri.parse(url));
                                                                  ;
                                                                } else {
                                                                  throw Exception('Could not launch $url');
                                                                }
                                                              }
                                                            }
                                                          } else {
                                                            setState(
                                                                    () {
                                                                  _isLoading =
                                                                  false;
                                                                });
                                                          }
                                                        } catch (e) {
                                                          setState(
                                                                  () {
                                                                _isLoading =
                                                                false;
                                                              });
                                                          Log.v(
                                                              'Error: $e');
                                                        }
                                                      },
                                                      child:
                                                      Container(
                                                          padding: EdgeInsets.symmetric(
                                                              vertical:
                                                              3,
                                                              horizontal:
                                                              6),
                                                          margin: EdgeInsets.only(
                                                              left:
                                                              1,
                                                              right:
                                                              4,
                                                              top:
                                                              8,
                                                              bottom:
                                                              8),
                                                          decoration: BoxDecoration(
                                                              color: ColorConstants.GREY_6,
                                                              borderRadius: BorderRadius.circular(4)),
                                                          child: Row(children: [
                                                            Padding(
                                                              padding: const EdgeInsets.all(2.0),
                                                              child: Text(
                                                                data!.checkPlagiarism == 1 ? 'Generate Launch Url' : 'Launch Url',
                                                                style: TextStyle(fontSize: 10),
                                                              ),
                                                            ),
                                                          ])),
                                                    )
                                                        : SizedBox(),

                                                    /*View Pdf Report*/
                                                    _attempts![currentIndex].pdfReportUrl != null
                                                        ? InkWell(
                                                      onTap:
                                                          () async {
                                                        /*final Uri url = Uri.parse('${_attempts![currentIndex].pdfReportUrl}');
                                                    if (!await launchUrl(url)) {
                                                    throw Exception('Could not launch');
                                                    }*/

                                                        final String
                                                        url =
                                                            '${_attempts![currentIndex].pdfReportUrl}';
                                                        if (await canLaunchUrl(
                                                            Uri.parse(
                                                                url))) {
                                                          await launchUrl(
                                                              Uri.parse(
                                                                  url));
                                                          ;
                                                        } else {
                                                          throw Exception(
                                                              'Could not launch $url');
                                                        }
                                                      },
                                                      child:
                                                      Container(
                                                          padding: EdgeInsets.symmetric(
                                                              vertical:
                                                              3,
                                                              horizontal:
                                                              6),
                                                          margin: EdgeInsets.only(
                                                              left:
                                                              1,
                                                              right:
                                                              4,
                                                              top:
                                                              8,
                                                              bottom:
                                                              8),
                                                          decoration: BoxDecoration(
                                                              color: ColorConstants.GREY_6,
                                                              borderRadius: BorderRadius.circular(4)),
                                                          child: Row(children: [
                                                            Padding(
                                                              padding: const EdgeInsets.all(2.0),
                                                              child: Text(
                                                                'View Pdf Report',
                                                                style: TextStyle(fontSize: 10),
                                                              ),
                                                            ),
                                                          ])),
                                                    )
                                                        : SizedBox(),

                                                  ],
                                                )
                                                    : SizedBox(),
                                              ],
                                            ),
                                          ),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  InkWell(
                                                    onTap: () async {
                                                      //singh list data showing
                                                      // _downloadSubmission(
                                                      //     _attempts![currentIndex]
                                                      //         .file);
                                                      downloadAssignment(
                                                          fileUrl: _attempts![
                                                                  currentIndex]
                                                              .file);
                                                    },
                                                    child: SvgPicture.asset(
                                                      'assets/images/download_icon.svg',
                                                      height: 20,
                                                      width: 20,
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants()
                                                                  .gradientRight(),
                                                              BlendMode.srcIn),
                                                      allowDrawingOutsideViewBox:
                                                          true,
                                                    ),
                                                  ),

                                                  SizedBox(width: 15),
                                                  InkWell(
                                                    onTap: () {
                                                      if (_attempts![currentIndex]
                                                                  .file!
                                                                  .split('.')
                                                                  .last
                                                                  .toLowerCase() ==
                                                              'zip' ||
                                                          _attempts![currentIndex]
                                                                  .file!
                                                                  .split('.')
                                                                  .last
                                                                  .toLowerCase() ==
                                                              'rar') {
                                                        ScaffoldMessenger.of(
                                                                context)
                                                            .showSnackBar(SnackBar(
                                                                content: Text(
                                                                        'view_file_on_web')
                                                                    .tr()));
                                                        return;
                                                      }
                                                      //debugPrint('userNotes Type------${_attempts![currentIndex].userNotes}');
                                                      Navigator.push(
                                                          context,
                                                          NextPageRoute(
                                                              //     PreviewSampleResume(
                                                              //   previewUrl: _attempts![
                                                              //           currentIndex]
                                                              //       .file,
                                                              //   title:
                                                              //       '${_attempts![currentIndex].title ?? ''}',
                                                              //   msg:
                                                              //       tr('file_type_msg'),
                                                              // )
                                                              FullContentPage(
                                                            //contentType: _attempts![currentIndex].userNotes,
                                                            contentType: _attempts![
                                                                    currentIndex]
                                                                .extension, //change 27-aug-2025
                                                            resourcePath:
                                                                _attempts![
                                                                        currentIndex]
                                                                    .file,
                                                          )));
                                                    },
                                                    child: SvgPicture.asset(
                                                      'assets/images/view_icon.svg',
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants()
                                                                  .gradientRight(),
                                                              BlendMode.srcIn),
                                                      height: 20,
                                                      width: 20,
                                                      allowDrawingOutsideViewBox:
                                                          true,
                                                    ),
                                                  ),

                                                  SizedBox(width: 15),
                                                  InkWell(
                                                    onTap: () {
                                                      int? submissionID = _attempts![currentIndex].id;
                                                      final String url = '${APK_DETAILS['domain_url']}assignment/mobile-view-submission/$submissionID';
                                                      Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                              builder: (context) => TermsAndCondition(
                                                                url: url,
                                                                title: tr('View Comment'),
                                                              ),
                                                              maintainState: false));

                                                      // if (await canLaunchUrl(Uri.parse(url))) {
                                                      //   await launchUrl(Uri.parse(url));
                                                      // } else {
                                                      //   throw Exception('Could not launch $url');
                                                      // }
                                                    },
                                                    child: Icon(
                                                        Icons.comment,
                                                      size: 20,
                                                      color: ColorConstants.PRIMARY_BLUE,

                                                    ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 10,
                                              ),
                                              Row(
                                                children: [
                                                  Container(
                                                    child: data
                                                                ?.submissionDetails![
                                                                    currentIndex]
                                                                .reviewStatus ==
                                                            0
                                                        ? Text(
                                                            "under_review",
                                                            style:
                                                                Styles.regular(
                                                                    size: 10),
                                                          ).tr()
                                                        : Text(
                                                            data?.isGraded == 0
                                                                ? tr(
                                                                    "non_graded")
                                                                : "${data?.submissionDetails![currentIndex].marksObtained ?? 0}/${assignmentDetailProvider?.assignments.maximumMarks}",
                                                            maxLines: 2,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            softWrap: true,
                                                            style: Styles.bold(
                                                                size: 12,
                                                                color: data?.isGraded ==
                                                                        0
                                                                    ? ColorConstants
                                                                        .BLACK
                                                                    : ColorConstants
                                                                        .GREEN),
                                                          ),
                                                  ),
                                                  SizedBox(width: 6),
                                                  SvgPicture.asset(
                                                    'assets/images/info.svg',
                                                    height: 14,
                                                    width: 14,
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                ],
                                              ),

                                            ],
                                          ),
                                        ],
                                      ),
                                      if (_attempts![currentIndex]
                                              .teacherNotes !=
                                          null)
                                        SizedBox(
                                          height: 4,
                                        ),
                                      if (_attempts![currentIndex]
                                              .teacherNotes !=
                                          null)
                                        ReadMoreText(
                                          text:
                                              '${_attempts![currentIndex].teacherNotes}',
                                          color: ColorConstants.PRIMARY_COLOR,
                                          viewMore: tr('view_more'),
                                        ),
                                      /*Text(
                                      '${_attempts![currentIndex].teacherNotes}',
                                      style: Styles.textItalic(
                                          size: 14, color: Color(0xff5A5F73)),
                                    ),*/
                                    ],
                                  ),
                                )),
                  ))
                : Center(
                    child: Text(
                      "no_assignment_submitted",
                      style: Styles.textBold(),
                    ).tr(),
                  ));
  }

  _belowTitle(MgAssignmentDetailProvider assignmentDetailProvider) {
    return Padding(
      padding: const EdgeInsets.only(left: 18, right: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${assignmentDetailProvider.assignments.title}',
              style: Styles.bold(
                  color: ColorConstants().gradientRight(), size: 16)),
          _size(height: 5),
          assignmentDetailProvider.assignment!.submissionDate != null
              ? Row(
                  children: [
                    Text('${tr('submission_date')}: ',
                        style:
                            Styles.bold(size: 12, color: ColorConstants.BLACK)),
                    assignmentDetailProvider.assignment!.submissionDate != null
                        ? Text(
                            '${DateFormat('dd-MMM-yyyy hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(assignmentDetailProvider.assignment!.submissionDate! * 1000))}',
                            style: Styles.bold(
                                size: 12, color: ColorConstants.GREY_6),
                            textDirection: ui.TextDirection.ltr,
                          )
                        : SizedBox(),
                  ],
                )
              : SizedBox(),
          SizedBox(height: 3),
          Row(
            children: [
              Text('${tr('end_date')}: ',
                  style: Styles.bold(size: 12, color: ColorConstants.BLACK)),
              Text(
                '${assignmentDetailProvider.assignment!.endDate != null ? Utility.convertDateFromMillis(
                    assignmentDetailProvider.assignment!.endDate!,
                    Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS,
                  ) : ''}',
                style: Styles.bold(size: 12, color: ColorConstants.GREY_6),
                textDirection: ui.TextDirection.ltr,
              )
            ],
          ),
          _size(height: 25),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  data?.isGraded == 0
                      ? Text(
                          "non_graded",
                          style: Styles.bold(
                              size: 14, color: ColorConstants.BLACK),
                        ).tr()
                      : Text(
                          '${assignmentDetailProvider.assignments.maximumMarks} ' +
                              tr('marks'),
                          style: Styles.bold(
                              size: 14, color: ColorConstants.BLACK),
                        ),
                  Text(
                    assignmentDetailProvider.assignments.allowMultiple != 0
                        ? ' • ' + tr('multiple_attempt')
                        : ' • 1 ' + tr('attempt'),
                    style: Styles.bold(size: 14, color: ColorConstants.BLACK),
                  ),
                ],
              ),
              _size(height: 10),
              Text('${assignmentDetailProvider.assignments.description}',
                  style: Styles.regular(size: 14)),
              _size(height: 30),
              Divider(),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Text('assignment_file',
                        style: Styles.bold(
                            color: ColorConstants().gradientRight(), size: 16))
                    .tr(),
                Row(
                  children: [
                    InkWell(
                      onTap: () async {
                        downloadAssignment(
                            fileUrl:
                                assignmentDetailProvider.assignment!.file!);
                      },
                      child: SvgPicture.asset(
                        'assets/images/download_icon.svg',
                        colorFilter: ColorFilter.mode(
                            ColorConstants().gradientRight(), BlendMode.srcIn),
                        height: 22,
                        width: 22,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ),
                    _size(width: 20),
                    InkWell(
                      onTap: () {
                        if (assignmentDetailProvider.assignments.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'zip' ||
                            assignmentDetailProvider.assignments.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'rar') {
                          ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('view_file_on_web').tr()));
                          return;
                        }
                        Navigator.push(
                            context,
                            NextPageRoute(FullContentPage(
                              contentType: assignmentDetailProvider
                                  .assignments.contentType,
                              resourcePath:
                                  assignmentDetailProvider.assignments.file,
                            )));
                      },
                      child: SvgPicture.asset(
                        'assets/images/view_icon.svg',
                        colorFilter: ColorFilter.mode(
                            ColorConstants().gradientRight(), BlendMode.srcIn),
                        height: 22,
                        width: 22,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ),
                  ],
                )
              ]),
              Divider(),
            ],
          )
        ],
      ),
    );
  }

  _size({double height = 20, double width = 0}) {
    return SizedBox(
      height: height,
      width: width,
    );
  }

  _body(Assignment assignment) {
    bool disbaleUpload = false;

    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(25), topLeft: Radius.circular(25))),
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _size(height: 20),
                Text(
                  'user_note',
                  style: Styles.textExtraBold(
                      size: 18, color: ColorConstants().gradientRight()),
                ).tr(),
                _size(height: 5),
                TextFormField(
                  maxLines: 3,
                  controller: _userNotes,
                  style: Styles.textBold(size: 16),
                  keyboardType: TextInputType.text,
                  decoration: InputDecoration(
                    fillColor: Color(0xFFFFFF),
                    hintStyle: TextStyle(
                      color: ColorConstants.GREY_OUTLINE,
                      fontSize: 16,
                    ),
                    filled: false,
                    labelStyle: TextStyle(
                      color: ColorConstants.TEXT_DARK_BLACK,
                      fontSize: 16,
                    ),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: ColorConstants.GREY_OUTLINE,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                        bottomLeft: Radius.circular(4),
                        bottomRight: Radius.circular(4),
                      ),
                    ),
                    focusColor: ColorConstants.GREY_OUTLINE,
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: ColorConstants.GREY_OUTLINE,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                    ),
                  ).copyWith(hintText: tr('write_something')),
                  validator: (value) =>
                      value!.isEmpty ? tr('enter_something') : null,
                  textInputAction: TextInputAction.done,
                  onChanged: (value) {},
                  onFieldSubmitted: (val) {},
                ),
              ],
            ),
            _size(),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  children: [
                    file == null
                        ? TapWidget(
                            onTap: () async {
                              switch (Utility.classStatus(
                                  assignmentDetailProvider!
                                      .assignment!.startDate!,
                                  assignmentDetailProvider!
                                      .assignment!.endDate!,
                                  currentIndiaTime!)) {
                                case 1:
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title:
                                          tr('assignment_not_ready_submission'),
                                      text: "",
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      showCancel: false,
                                      oKText: '${tr('ok')}',
                                      onOkClick: () async {});
                                  return;

                                case 2:
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title: tr('due_data_passed'),
                                      text: "",
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      showCancel: false,
                                      oKText: '${tr('ok')}',
                                      onOkClick: () async {});
                                  return;
                              }

                              if (!disbaleUpload) {
                                if (assignmentDetailProvider!
                                            .assignments.allowMultiple ==
                                        0 &&
                                    assignmentDetailProvider!
                                            .assignment?.totalAttempts !=
                                        0)
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title: tr('reached_maximum_attempt'),
                                      text: "",
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      showCancel: false,
                                      onOkClick: () async {});
                                else
                                  _attachFile();
                              }
                            },
                            child: Container(
                              padding: EdgeInsets.all(5),
                              width: MediaQuery.of(context).size.width * 0.65,
                              decoration: BoxDecoration(
                                  gradient: LinearGradient(colors: [
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight(),
                                  ]),
                                  color: disbaleUpload
                                      ? ColorConstants.GREY_4
                                      : ColorConstants().gradientRight(),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(5))),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 8, right: 8, top: 4, bottom: 4),
                                child: assignmentDetailProvider!.isLoading
                                    ? Center(child: CircularProgressIndicator())
                                    : Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'attach_file',
                                            style: Styles.textExtraBold(
                                                size: 14,
                                                color: ColorConstants()
                                                    .primaryForgroundColor()),
                                          ).tr(),
                                          _size(width: 10),
                                          Icon(
                                            Icons.attach_file,
                                            color: ColorConstants.WHITE,
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                          )
                        : Row(
                            children: [
                              Container(
                                width: MediaQuery.of(context).size.width * 0.8,
                                child: Text(
                                  file!.path.split("/").last,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  softWrap: true,
                                  style: TextStyle(
                                      decoration: TextDecoration.underline),
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              TapWidget(
                                onTap: () {
                                  setState(() {
                                    file = null;
                                  });
                                },
                                child: Icon(
                                  Icons.delete,
                                  size: 20,
                                ),
                              ),
                            ],
                          ),
                    _size(height: 10),
                    if (file == null)
                      Row(
                        children: [
                          SizedBox(
                            width: 200,
                            child: Text(
                              '${tr('supported_format')}: pdf, doc, docx, xls, xlsx, csv, jpg, jpeg, png, gif, mp3, mp4, avi, zip, rar',
                              style: Styles.regular(
                                  size: 12, color: ColorConstants.GREY_4),
                              maxLines: 2,
                            ),
                          ),
                        ],
                      ),
                    _size(),
                    TapWidget(
                      onTap: () {
                        if (!disbaleUpload) _submitAssignment();
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.65,
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                                colors: !disbaleUpload
                                    ? [
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight(),
                                      ]
                                    : [
                                        ColorConstants.GREY_3,
                                        ColorConstants.GREY_3,
                                      ]),
                            color: disbaleUpload
                                ? ColorConstants.GREY_4
                                : ColorConstants().gradientRight(),
                            borderRadius: BorderRadius.all(Radius.circular(5))),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8, right: 8, top: 4, bottom: 4),
                          child: assignmentDetailProvider!.isLoading
                              ? Center(child: CircularProgressIndicator())
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'upload_assignment',
                                      style: Styles.textExtraBold(
                                          size: 14,
                                          color: ColorConstants()
                                              .primaryForgroundColor()),
                                    ).tr(),
                                    _size(width: 10),
                                    Icon(Icons.file_upload_outlined,
                                        color: ColorConstants.WHITE)
                                  ],
                                ),
                        ),
                      ),
                    ),
                    _size(height: 10),
                    if (assignmentDetailProvider!.assignment?.totalAttempts !=
                        0)
                      Row(
                        children: [
                          Text(
                            '${assignmentDetailProvider?.assignment?.totalAttempts} ${assignmentDetailProvider!.assignment!.totalAttempts! > 1 ? tr('attempts') : tr('attempt')}',
                            style: Styles.regular(
                                size: 14, color: ColorConstants.RED),
                          ),
                          if (assignmentDetailProvider!
                                  .assignment?.totalAttempts !=
                              0)
                            Text(
                              'taken',
                              style: Styles.regular(
                                  size: 14, color: ColorConstants.RED),
                            ).tr(),
                        ],
                      ),
                    _size(height: 15),
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TapWidget(
                  onTap: () {
                    Navigator.push(
                        context,
                        NextPageRoute(
                            ReviewSubmissions(
                              maxMarks: assignmentDetailProvider!
                                  .assignment?.maximumMarks,
                              contentId: widget.id,
                            ),
                            isMaintainState: true));
                  },
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: ColorConstants().gradientRight())),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          'view_submission',
                          style: Styles.textRegular(
                              color: ColorConstants().gradientRight()),
                        ).tr(),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _getData() {
    BlocProvider.of<HomeBloc>(context)
        .add(AssignmentSubmissionsEvent(request: widget.id));
  }

  void _handleResponse(AssignmentSubmissionsState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          data = state.response!.data!.assessmentDetails!.first;
          checkPlagiarismValue = data!.checkPlagiarism;
          _attempts =
              state.response!.data!.assessmentDetails!.first.submissionDetails;

          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'mg_assignment_submission', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleGenerateSimilarityResponse(GenerateSimilarityState state) {
    var loginState = state;
    Log.v(loginState.apiState);
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("GenerateSimilarityResponse....................${state.state}");
          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v(
              "GenerateSimilarity Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'mg_assignment_submission', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _attachFile() async {
    FilePickerResult? result;
    if (Platform.isIOS) {
      if (await Permission.storage.request().isGranted) {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: FileType.any,
        );
      }
    } else {
      result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.any,
      );
    }
    if (result != null) {
      var fileExtension = result.files.first.name.split('.').last.toLowerCase();
      // var fileExtension = extension(result.files.first.name!).toLowerCase();
      var allowedExtensions = [
        'pdf',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'csv',
        'jpg',
        'jpeg',
        'png',
        'gif',
        'mp3',
        'mp4',
        'avi',
        'zip',
        'rar'
      ];

      if (allowedExtensions.contains(fileExtension)) {
        setState(() {
          file = File(result!.files.first.path!);
        });
      } else {
        // File type not allowed
        // Handle accordingly (e.g., show error message)
        AlertsWidget.showCustomDialog(
            context: context,
            title: "file_not_supported".tr(),
            text: "",
            icon: 'assets/images/circle_alert_fill.svg',
            showCancel: false,
            oKText: tr('ok'),
            onOkClick: () async {});
      }
    }
  }


  void downloadAssignment({String? fileUrl}) async {
    debugPrint('fileUrl:-- ${fileUrl}');
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;

    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }

    String localPath;

    final status = await Permission.storage.request();

    if (status.isGranted ||
        (android.version.sdkInt >= 30) ||
        (await Permission.storage.request()).isGranted) {
      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
        // localPath = (await getExternalStorageDirectory())!.path + "/Download";
      }

      final file = File("$localPath/${fileUrl!.split('/').last}");

      try {
        if (!file.existsSync()) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Downloading Start'),
          ));

          // final taskId =
          await FlutterDownloader.enqueue(
            url: fileUrl,
            savedDir: localPath,
            showNotification: true,
            openFileFromNotification: true,
            saveInPublicStorage: true,
          ).then((value) async {
            ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
              content: Text('Successfully Downloaded'),
            ));
            OpenFilex.open("$localPath/${fileUrl.split('/').last}");
          });
        } else {
          Utility.showSnackBar(
              scaffoldContext: context, message: 'File already exists');
          OpenFilex.open("$localPath/${fileUrl.split('/').last}");
        }
      } catch (e) {
        log("fix log $e");
      }
    } else {
      launchUrl(Uri.parse(fileUrl!), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
  }

  void _submitAssignment() async {
    if (file != null) {
      bool res = await assignmentDetailProvider!.uploadAssignment(
          notes: _userNotes.text, path: file!.path, id: widget.id);
      if (res) {
        _getData();
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text("assignment_submitted").tr()));
        setState(() {
          file = null;
        });
        _userNotes.clear();
        _getData();
      } else {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text("something_went_wrong").tr()));
      }
    } else {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text("plz_attach_file").tr()));
    }
  }
}

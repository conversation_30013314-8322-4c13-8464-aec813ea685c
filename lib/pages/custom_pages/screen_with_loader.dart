import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/styles.dart';

import 'package:masterg/utils/theme/theme_extensions.dart';

class ScreenWithLoader extends StatefulWidget {
  final bool? isLoading;
  final Color? color;
  final Widget? body;
  final bool isContainerHeight;

  const ScreenWithLoader(
      {super.key,
      this.isLoading,
      this.body,
      this.color,
      this.isContainerHeight = true});

  @override
  State<ScreenWithLoader> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<ScreenWithLoader> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        // Get theme-aware overlay color
        final overlayColor = widget.color ??
            (context.isDarkMode ? Colors.black54 : Colors.white54);

        return Stack(
          children: <Widget>[
            widget.isContainerHeight
                ? SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    child: widget.body,
                  )
                : SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: widget.body,
                  ),
            Visibility(
              visible: widget.isLoading!,
              child: widget.isContainerHeight
                  ? Container(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      color: overlayColor,
                      child: Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                              color: context.isDarkMode
                                  ? Colors.grey[800]!.withValues(alpha: 0.9)
                                  : Colors.black.withValues(alpha: 0.7),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(10))),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Center(
                                child: Text(
                                  'loading',
                                  style: Styles.boldWhite(size: 16),
                                ).tr(),
                              ),
                              CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    context.primaryForegroundColor),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                  : Container(
                      width: MediaQuery.of(context).size.width,
                      color: overlayColor,
                      child: Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                              color: context.isDarkMode
                                  ? Colors.grey[800]!.withValues(alpha: 0.9)
                                  : Colors.black.withValues(alpha: 0.7),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(10))),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Text(
                                'Screen_loading',
                                style: Styles.boldWhite(size: 16),
                              ).tr(),
                              CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    context.backgroundColor),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
            ),
          ],
        );
      },
    );
  }
}

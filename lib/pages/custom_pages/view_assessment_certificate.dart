import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/generate_certificate_resp.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:isolate';
import 'dart:ui';

class CertificateView extends StatefulWidget {
  final int certificateId;
  final String programName;
  final int contentId;

  const CertificateView(
      {super.key,
      required this.certificateId,
      required this.programName,
      required this.contentId});

  @override
  State<CertificateView> createState() => _CertificateViewState();
}

class _CertificateViewState extends State<CertificateView> {
  bool isCertificateLoading = false;
  GenerateCertificateResponse? generateCertificateResponse;
  final ReceivePort _port = ReceivePort();
  @override
  void initState() {
    BlocProvider.of<HomeBloc>(context).add((GenerateCertificateEvent(
        certificateId: widget.certificateId,
        programName: widget.programName,
        contentId: widget.contentId)));
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      setState(() {});
    });

    // FlutterDownloader.registerCallback(downloadCallback);
    super.initState();
  }

  @override
  void dispose() {
    IsolateNameServer.removePortNameMapping('downloader_send_port');
    super.dispose();
  }

  // static void downloadCallback(
  //     String id, DownloadTaskStatus status, int progress) {
  //   final SendPort send =
  //       IsolateNameServer.lookupPortByName('downloader_send_port')!;
  //   send.send([id, status, progress]);
  // }

  // static void downloadCallback(
  //     String id, int status, int progress) {
  //   final SendPort send =
  //   IsolateNameServer.lookupPortByName('downloader_send_port')!;
  //   send.send([id, status, progress]);
  // }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: ScreenWithLoader(
        isLoading: isCertificateLoading,
        body: BlocManager(
          initState: (BuildContext context) {},
          child: BlocListener<HomeBloc, HomeState>(
            listener: (BuildContext context, state) {
              if (state is GenerateCertificateState) {
                // _handelBottomNavigationBar(state);
                _handleCertificateResponse(state);
              }
            },
            child: Scaffold(
                appBar: AppBar(
                    leading: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Icon(
                        Icons.arrow_back,
                        color: ColorConstants.WHITE,
                      ),
                    ),
                    backgroundColor: ColorConstants().primaryColor(),
                    actions: [
                      IconButton(
                        onPressed: () async {
                          download('${generateCertificateResponse?.url}');
                        },
                        icon: Icon(Icons.download),
                      )
                    ]),
                body: isCertificateLoading
                    ? SizedBox()
                    : SafeArea(
                        child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        child: PDF(
                          autoSpacing: false,
                          fitPolicy: FitPolicy.BOTH,
                          enableSwipe: true,
                          gestureRecognizers: [
                            Factory(() => PanGestureRecognizer()),
                            Factory(() => VerticalDragGestureRecognizer())
                          ].toSet(),
                        ).cachedFromUrl(
                          '${generateCertificateResponse?.url}',
                          placeholder: (progress) =>
                              Center(child: Text('$progress %')),
                          errorWidget: (error) =>
                              Center(child: Text(error.toString())),
                        ),
                      ))),
          ),
        ),
      ),
    );
  }

  void download(String? usersFile) async {
    print('downloading');
    if (await Permission.storage.request().isGranted) {
      String localPath = "";
      if (Platform.isAndroid) {
        localPath = '/storage/emulated/0/Download';

        final file = File(localPath + "/" + usersFile!.split('/').last);
        if (file.existsSync()) {
          print("FILE EXISTS");

          print(localPath + "/" + usersFile.split('/').last);
          AlertsWidget.showCustomDialog(
              context: context,
              title: "File already exists!",
              text: "Do you want to open?",
              icon: 'assets/images/circle_alert_fill.svg',
              showCancel: true,
              oKText: "Yes",
              onCancelClick: () {},
              onOkClick: () async {});
          return;
        }
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }

      var savedDir = Directory(localPath);
      bool hasExisted = await savedDir.exists();
      if (!hasExisted) {
        savedDir = await savedDir.create();
      }
      download2(usersFile!, localPath);
    } else {
      Utility.showSnackBar(
          scaffoldContext: context,
          message: "Please enable storage permission");
    }
  }

  Future download2(String url, String savePath) async {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          "Download started..",
          style: Styles.boldWhite(),
        ),
        backgroundColor: ColorConstants.BLACK,
        duration: Duration(seconds: 2),
      ),
    );
    final task = await FlutterDownloader.enqueue(
      url: url,
      savedDir: savePath,
      showNotification: true,
      saveInPublicStorage: false,
      openFileFromNotification: true,
    );

    await FlutterDownloader.open(taskId: task!);
  }

  void _handleCertificateResponse(GenerateCertificateState state) {
    var generateCertificate = state;
    setState(() {
      switch (generateCertificate.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isCertificateLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "Successnm....................${generateCertificate.response?.url}");
          generateCertificateResponse = generateCertificate.response;
          isCertificateLoading = false;
          break;

        case ApiStatus.ERROR:
          isCertificateLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${generateCertificate.error}");

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}

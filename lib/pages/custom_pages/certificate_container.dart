import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class CertificateContainer extends StatelessWidget {
  const CertificateContainer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,

      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [
          ColorConstants().gradientLeft(),
          ColorConstants().gradientRight(),
        ]),
      ),
      child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
        SvgPicture.asset(
          'assets/images/certificate_icon.svg',
          fit: BoxFit.cover,
        ),
        SizedBox(width: 10),
        Text('view_your_cert',
            style: Styles.semibold(size: 14, color: ColorConstants.WHITE)).tr()
      ]),
    );
  }
}

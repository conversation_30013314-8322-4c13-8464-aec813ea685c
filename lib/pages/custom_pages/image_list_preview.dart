import 'dart:math';
import 'package:flutter/material.dart';
class ImageListPreview extends StatefulWidget {
  final List<String> images;
  final Function(int) callback;
  final Axis scrollDirection;
  const ImageListPreview(
      {Key? key,
      required this.images,
      required this.callback,
      this.scrollDirection = Axis.horizontal})
      : super(key: key);

  @override
  State<ImageListPreview> createState() => _ImageListPreviewState();
}

class _ImageListPreviewState extends State<ImageListPreview> {
  double _scale = 1.0;
  double _previousScale = 1.0;
  @override
  Widget build(BuildContext context) {
    return PageView.builder(
        itemCount: widget.images.length,
        onPageChanged: (value) => widget.callback(value),
        scrollDirection: widget.scrollDirection,
        itemBuilder: (context, index) => GestureDetector(
              onScaleStart: (ScaleStartDetails details) {
                _previousScale = _scale;
                setState(() {});
              },
              onScaleUpdate: (ScaleUpdateDetails details) {
                _scale = _previousScale * details.scale;
                setState(() {});
              },
              child: Transform.scale(
                scale: max(_scale, 1.0),
                child: Image.network(
                  widget.images[index],
                  fit: BoxFit.contain,
                  loadingBuilder: (BuildContext context, Widget child,
                      ImageChunkEvent? loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    );
                  },
                  errorBuilder: (context, url, error) {
                    return Center(
                      child: Text('No image found'),
                    );
                  },
                ),
              ),
            ));
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class AlertsWidget {
  static Future alertWithOkCancelBtn(
      {required BuildContext context,
      String? title = "",
      String? text = "",
      String? okText,
      String? cancelText,
      Function? onOkClick,
      Function? onCancelClick}) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Text('$title'),
            content: Text('$text'),
            actions: <Widget>[
              TextButton(
                child: new Text(cancelText ?? 'no').tr(),
                onPressed: () {
                  if (onCancelClick != null) onCancelClick();
                  Navigator.of(context).pop();
                },
              ),
              Container(
                decoration: BoxDecoration(
                    color: ColorConstants().primaryColorAlways(),
                    borderRadius: BorderRadius.circular(8)),
                child: TextButton(
                  child: new Text(
                    okText ?? 'yes',
                    style: Styles.regular(color: ColorConstants.WHITE),
                  ).tr(),
                  onPressed: () {
                    Navigator.pop(context);
                    if (onOkClick != null) onOkClick();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future alertWithYesNoBtn(
      {required BuildContext context,
      String title = "",
      String text = "",
      Function? onOkClick,
      Function? onCancelClick}) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // return object of type Dialog
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Text(
              title,
              style: Styles.textBold(size: 18),
              textAlign: TextAlign.center,
            ),
            content: Text(
              text,
              style: Styles.textRegular(size: 14),
              textAlign: TextAlign.center,
            ),
            actions: <Widget>[
              Padding(
                padding: const EdgeInsets.only(left: 30, right: 10),
                child: TextButton(
                  child: new Text(
                    'no',
                    style: Styles.textBold(size: 18),
                    textAlign: TextAlign.center,
                  ).tr(),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onCancelClick != null) onCancelClick();
                  },
                ),
              ),
              SizedBox(
                width: 30,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 10, right: 30),
                child: TextButton(
                  child: new Text(
                    'yes',
                    style: Styles.textBold(size: 18),
                    textAlign: TextAlign.center,
                  ).tr(),
                  onPressed: () {
                    Navigator.pop(context);
                    if (onOkClick != null) onOkClick();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future alertWithOkBtn(
      {required BuildContext context,
      String? text = "",
      Function? onOkClick,
      bool isBarrierDismissible = false}) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // return object of type Dialog
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Text(
              'alert',
              style: Styles.textBold(size: 20),
            ).tr(),
            content: Text(text ?? '', style: Styles.textBold(size: 16)),
            actions: <Widget>[
              Container(
                height: 40,
                width: 55,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                  color: ColorConstants.PRIMARY_BLUE,
                ),
                child: TextButton(
                  child: new Text('ok',
                          style: Styles.textBold(
                              size: 14, color: ColorConstants.WHITE))
                      .tr(),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onOkClick != null) onOkClick();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future showCustomDialog(
      {required BuildContext context,
      bool? showCancel = true,
      String? title = "",
      String? text = "",
      String? oKText,
      String? icon,
      Function? onOkClick,
      Function? onCancelClick,
      int? alertType = 1,
      bool? backonOk = true,
      bool isRTL = false,
      Function(BuildContext)? returnContext,
      bool? enable}) {
    String? icon;
    switch (alertType) {
      case 1:
        icon = 'assets/images/circle_alert_fill.svg';
        break;
      case 2:
        icon = 'assets/images/success_icon.svg';
        break;
      case 3:
        icon = 'assets/images/warning_icon.svg';
        break;
      default:
        icon = 'assets/images/circle_alert_fill.svg';
    }

    return showDialog(
        context: context,
        builder: (BuildContext context) {
          if (returnContext != null) {
            returnContext(context);
          }
          return Directionality(
            textDirection: Utility.setDirection(isRTL),
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)), //this right here
              child: Container(
                // height: 230,
                constraints: BoxConstraints(
                  minHeight: 0,
                  maxHeight: MediaQuery.of(context).size.height *
                      0.32, // Set maxHeight to infinity
                ),
                child: Padding(
                  padding: const EdgeInsets.all(0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        icon!,
                        allowDrawingOutsideViewBox: true,
                        width: height(context) * 0.04,
                        height: height(context) * 0.04,
                      ),
                      SizedBox(
                        height: 10.0,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Text(
                            '$title',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ).tr(),
                        ),
                      ),
                      Text(
                        text!,
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 14),
                      ).tr(),
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 15.0,
                          top: 25.0,
                          right: 15.0,
                        ),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          height: 50,
                          padding: EdgeInsets.only(bottom: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              if (showCancel == true)
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      if (onCancelClick != null)
                                        onCancelClick();
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Align(
                                          alignment: Alignment.center,
                                          child: Text(
                                            'cancel',
                                          ).tr()),
                                    ),
                                  ),
                                ),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    if (backonOk == true)
                                      Navigator.pop(context);
                                    if (onOkClick != null) onOkClick();
                                  },
                                  child: Container(
                                    height: 50.0,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.RED,
                                      borderRadius: BorderRadius.circular(10),
                                      gradient: LinearGradient(colors: [
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight(),
                                      ]),
                                    ),
                                    child: Align(
                                        alignment: Alignment.center,
                                        child: Text(
                                          oKText ?? tr('ok'),
                                          style: Styles.regular(
                                              color: ColorConstants()
                                                  .primaryForgroundColor()),
                                        )),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  static Future helpMecFutureCustomDialog(
      {required BuildContext context,
      bool? showCancel = true,
      String? title = "",
      String? text = "",
      String? oKText,
      Function? onOkClick,
      Function? onCancelClick,
      int? alertType = 1,
      bool? backonOk = true,
      bool isRTL = false,
      Function(BuildContext)? returnContext,
      bool? enable}) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          if (returnContext != null) {
            returnContext(context);
          }
          return Directionality(
            textDirection: Utility.setDirection(isRTL),
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)), //this right here
              child: Container(
                // height: 230,
                constraints: BoxConstraints(
                  minHeight: 0,
                  maxHeight: MediaQuery.of(context).size.height *
                      0.32, // Set maxHeight to infinity
                ),
                child: Padding(
                  padding: const EdgeInsets.all(0.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      /*SvgPicture.asset(
                        icon!,
                        allowDrawingOutsideViewBox: true,
                        width: height(context) * 0.04,
                        height: height(context) * 0.04,
                      ),*/
                      Icon(Icons.help),
                      SizedBox(
                        height: 10.0,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Text(
                            '$title',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          text!,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 15.0,
                          top: 25.0,
                          right: 15.0,
                        ),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          height: 50,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              if (showCancel == true)
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      if (onCancelClick != null)
                                        onCancelClick();
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Align(
                                          alignment: Alignment.center,
                                          child: Text(
                                            'skip',
                                          ).tr()),
                                    ),
                                  ),
                                ),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    if (backonOk == true)
                                      Navigator.pop(context);
                                    if (onOkClick != null) onOkClick();
                                  },
                                  child: Container(
                                    height: 50.0,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.RED,
                                      borderRadius: BorderRadius.circular(10),
                                      gradient: LinearGradient(colors: [
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight(),
                                      ]),
                                    ),
                                    child: Align(
                                        alignment: Alignment.center,
                                        child: Text(
                                          oKText ?? tr('Go'),
                                          style: Styles.regular(
                                              color: ColorConstants()
                                                  .primaryForgroundColor()),
                                        )),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  static Future outstandingFinancialDuesCustomDialog(
      {required BuildContext context,
      bool? showCancel = true,
      String? name = "",
      String? regID,
      String? title = "",
      String? text = "",
      String? oKText,
      String? icon,
   required   bool isArabic,
      Function? onOkClick,
      Function? onCancelClick,
      int? alertType = 1,
      bool? backonOk = true,
      bool isRTL = false,
      Function(BuildContext)? returnContext,
      bool? enable}) {
    String? icon;
    switch (alertType) {
      case 1:
        icon = 'assets/images/circle_alert_fill.svg';
        break;
      case 2:
        icon = 'assets/images/success_icon.svg';
        break;

      case 3:
        icon = 'assets/images/warning_icon.svg';
        break;

      default:
        icon = 'assets/images/circle_alert_fill.svg';
    }

    return showDialog(
        context: context,
        builder: (BuildContext context) {
          if (returnContext != null) {
            returnContext(context);
          }

          final screenHeight = height(context);
          final screenWidth = width(context);
          final isTablet = screenWidth > 600;

          final dialogWidth = isTablet ? screenWidth * 0.5 : screenWidth * 0.85;
          final maxDialogHeight = screenHeight * 0.8;
          final iconSize = isTablet ? screenHeight * 0.06 : screenHeight * 0.04;
          final titleFontSize = isTablet ? 20.0 : 16.0;
          final textFontSize = isTablet ? 16.0 : 12.0;
          final buttonHeight = isTablet ? 60.0 : 50.0;
          final horizontalPadding = isTablet ? 24.0 : 16.0;
          final verticalPadding = isTablet ? 32.0 : 24.0;

          return Directionality(
            textDirection: Utility.setDirection(isRTL),
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)),
              child: Container(
                width: dialogWidth,
                constraints: BoxConstraints(
                  minHeight: 0,
                  maxHeight: maxDialogHeight,
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: horizontalPadding,
                    vertical: verticalPadding,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(
                            icon!,
                            allowDrawingOutsideViewBox: true,
                            width: iconSize,
                            height: iconSize,
                          ),
                          SizedBox(height: isTablet ? 16.0 : 12.0),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: isTablet ? 16.0 : 8.0,
                            ),
                            child: Text(
                              '$title',
                              textAlign: TextAlign.center,
                              style: Styles.bold(
                                size: titleFontSize,
                              ),
                            ).tr(),
                          ),
                          SizedBox(height: isTablet ? 16.0 : 12.0),
                        ],
                      ),

                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: isTablet ? 16.0 : 8.0,
                        ),
                        child: Wrap(
                          alignment: WrapAlignment.center,
                          crossAxisAlignment: WrapCrossAlignment.center,

                          // mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                             isArabic
                                  ? tr('declaration_sub_title1')+' $name '
                                   + tr('declaration_sub_title2') +
                                      ' $regID ' +
                                      tr('declaration_sub_title3') 
                                      
                                     
                                  :
                              tr('declaration_sub_title1') +
                                  ' $name ' +
                                  tr('declaration_sub_title2') +
                                  ' $regID ' +
                                  tr('declaration_sub_title3'),
                              style: Styles.regular(size: 12),
                              textDirection: isArabic
                                  ? Utility.setDirection(true)
                                  : Utility.setDirection(false), // <-- Add this line

                            ),
                            // Text(' $name ', style: Styles.bold(size: 12),),
                            // Text('declaration_sub_title2', style: Styles.regular(size: 12),).tr(),
                            // Text(' $regID ', style: Styles.bold(size: 12),).tr(),
                            // Text('declaration_sub_title3', style: Styles.regular(size: 12),).tr(),
                          ],
                        ),
                      ),
                      // Padding(
                      //   padding: EdgeInsets.symmetric(
                      //     horizontal: isTablet ? 16.0 : 8.0,
                      //   ),
                      //   child: Wrap(
                      //     alignment: WrapAlignment.center,
                      //     crossAxisAlignment: WrapCrossAlignment.center,
                      //
                      //     // mainAxisAlignment: MainAxisAlignment.center,
                      //     children: [
                      //       Text('${declaration_sub_title1.tr()}', style: Styles.regular(size: 12),),
                      //
                      //     ],
                      //
                      //   ),
                      // ),
                      // Padding(
                      //   padding: EdgeInsets.all(0),
                      //
                      //   child: Text.rich(TextSpan(children: [
                      //     TextSpan(text: tr('declaration_sub_title1'), style: Styles.regular(size: textFontSize)),
                      //     TextSpan(text: ' $name ', style: Styles.bold(size: textFontSize)),
                      //     TextSpan(text: tr('declaration_sub_title2'), style: Styles.regular(size: textFontSize)),
                      //     TextSpan(text: ' $studentID ', style: Styles.bold(size: textFontSize)),
                      //     TextSpan(text: tr('declaration_sub_title3'), style: Styles.regular(size: textFontSize)),
                      //   ])),
                      // ),

                      Divider(),
                      Flexible(
                        child: Container(
                          constraints: BoxConstraints(
                            maxHeight:
                                maxDialogHeight * 0.5, // Limit scrollable area
                          ),
                          child: Scrollbar(
                            child: SingleChildScrollView(
                              physics: const BouncingScrollPhysics(),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 16.0 : 8.0,
                                ),
                                child: Text(
                                  text!,
                                  textAlign: TextAlign.left,
                                  textDirection: Utility.setDirection(isRTL),
                                  style: Styles.regular(
                                    size: textFontSize,
                                  ),
                                ).tr(),
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: isTablet ? 24.0 : 16.0),

                      Container(
                        width: double.infinity,
                        height: buttonHeight,
                        child: Row(
                          children: [
                            if (showCancel == true) ...[
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    if (onCancelClick != null) {
                                      onCancelClick();
                                    }
                                    Navigator.of(context).pop();
                                  },
                                  borderRadius: BorderRadius.circular(10),
                                  child: Container(
                                    height: buttonHeight,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.black,
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: ColorConstants.black,
                                        width: 1,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        'cancel',
                                        style: Styles.regular(
                                            size: isTablet ? 16.0 : 14.0,
                                            color: ColorConstants.white),
                                      ).tr(),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: isTablet ? 16.0 : 12.0),
                            ],

                            // OK button
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  if (backonOk == true) {
                                    Navigator.pop(context);
                                  }
                                  if (onOkClick != null) onOkClick();
                                },
                                borderRadius: BorderRadius.circular(10),
                                child: Container(
                                  height: buttonHeight,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    gradient: LinearGradient(colors: [
                                      ColorConstants.PRIMARY_BLUE,
                                      ColorConstants.PRIMARY_BLUE,
                                    ]),
                                  ),
                                  child: Center(
                                    child: Text(
                                      oKText ?? tr('ok'),
                                      style: Styles.regular(
                                        color: ColorConstants.white,
                                        size: isTablet ? 16.0 : 14.0,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  static Future AcceptFeeAgreementAlertDialogue(
      {required BuildContext context,
      bool? showCancel = true,
      String? title = "",
      String? text = "",
      String? oKText,
      String? icon,
      Function? onOkClick,
      Function? onCancelClick,
      int? alertType = 1,
      bool? backonOk = true,
      bool isRTL = false,
      Function(BuildContext)? returnContext,
      bool? enable}) {
    String? icon;
    switch (alertType) {
      case 1:
        icon = 'assets/images/circle_alert_fill.svg';
        break;
      case 2:
        icon = 'assets/images/success_icon.svg';
        break;

      case 3:
        icon = 'assets/images/warning_icon.svg';
        break;

      default:
        icon = 'assets/images/circle_alert_fill.svg';
    }

    return showDialog(
        context: context,
        builder: (BuildContext context) {
          if (returnContext != null) {
            returnContext(context);
          }

          final screenHeight = height(context);
          final screenWidth = width(context);
          final isTablet = screenWidth > 600;

          final dialogWidth = isTablet ? screenWidth * 0.5 : screenWidth * 0.85;
          final maxDialogHeight = screenHeight * 0.8;
          final iconSize = isTablet ? screenHeight * 0.06 : screenHeight * 0.04;
          final titleFontSize = isTablet ? 20.0 : 16.0;
          final textFontSize = isTablet ? 16.0 : 12.0;
          final buttonHeight = isTablet ? 60.0 : 50.0;
          final horizontalPadding = isTablet ? 24.0 : 16.0;
          final verticalPadding = isTablet ? 32.0 : 24.0;

          return Directionality(
            textDirection: Utility.setDirection(isRTL),
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)),
              child: Container(
                width: dialogWidth,
                constraints: BoxConstraints(
                  minHeight: 0,
                  maxHeight: maxDialogHeight,
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: horizontalPadding,
                    vertical: verticalPadding,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(
                            icon!,
                            allowDrawingOutsideViewBox: true,
                            width: iconSize,
                            height: iconSize,
                          ),
                          SizedBox(height: isTablet ? 16.0 : 12.0),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: isTablet ? 16.0 : 8.0,
                            ),
                            child: Text(
                              '$title',
                              textAlign: TextAlign.center,
                              style: Styles.bold(
                                size: titleFontSize,
                              ),
                            ).tr(),
                          ),
                          SizedBox(height: isTablet ? 16.0 : 12.0),
                        ],
                      ),
                      SizedBox(height: isTablet ? 26.0 : 20.0),
                      Flexible(
                        child: Container(
                          constraints: BoxConstraints(
                            maxHeight:
                                maxDialogHeight * 0.5, // Limit scrollable area
                          ),
                          child: SingleChildScrollView(
                            physics: const BouncingScrollPhysics(),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: isTablet ? 16.0 : 8.0,
                              ),
                              child: Text(
                                text!,
                                textAlign: TextAlign.center,
                                textDirection: Utility.setDirection(isRTL),
                                style: Styles.regular(
                                  size: textFontSize,
                                ),
                              ).tr(),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: isTablet ? 42.0 : 32.0),
                      Container(
                        width: double.infinity,
                        height: buttonHeight,
                        child: Row(
                          children: [
                            if (showCancel == true) ...[
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    if (onCancelClick != null) {
                                      onCancelClick();
                                    }
                                    Navigator.of(context).pop();
                                  },
                                  borderRadius: BorderRadius.circular(10),
                                  child: Container(
                                    height: buttonHeight,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.black,
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: ColorConstants.grey,
                                        width: 1,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        'cancel',
                                        style: Styles.regular(
                                          color: ColorConstants.white,
                                          size: isTablet ? 16.0 : 14.0,
                                        ),
                                      ).tr(),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: isTablet ? 16.0 : 12.0),
                            ],

                            // OK button
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  if (backonOk == true) {
                                    Navigator.pop(context);
                                  }
                                  if (onOkClick != null) onOkClick();
                                },
                                borderRadius: BorderRadius.circular(10),
                                child: Container(
                                  height: buttonHeight,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    gradient: LinearGradient(colors: [
                                      ColorConstants.PRIMARY_BLUE,
                                      ColorConstants.PRIMARY_BLUE,
                                    ]),
                                  ),
                                  child: Center(
                                    child: Text(
                                      oKText ?? tr('ok'),
                                      style: Styles.regular(
                                        color: ColorConstants.white,
                                        size: isTablet ? 16.0 : 14.0,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }
}

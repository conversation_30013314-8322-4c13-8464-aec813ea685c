import 'dart:developer';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:camera/camera.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/user_profile_page/mobile_ui_helper.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart' as Thumbnail;

import '../../../utils/click_picker.dart';

import 'package:flutter/services.dart' show rootBundle;
import 'package:path_provider/path_provider.dart';

class UploadProfile extends StatefulWidget {
  final bool? editVideo;
  final bool? playVideo;
  final Function onBack;

  const UploadProfile(
      {Key? key,
      this.editVideo = true,
      this.playVideo = false,
      required this.onBack})
      : super(key: key);

  @override
  State<UploadProfile> createState() => _UploadProfileState();
}

class _UploadProfileState extends State<UploadProfile> {
  String? selectedImage;
  File? pickedFile;
  List<File> pickedList = [];
  Thumbnail.ImageFormat format = Thumbnail.ImageFormat.JPEG;
  int quality = 10;
  String? tempDir;
  String? filePath;
  bool? profileLoading = false;
  late VideoPlayerController controller;
  bool isVideoLoading = true;

  @override
  void initState() {
    initVideo();
    super.initState();
  }

  void initVideo() async {
    if (widget.editVideo == true || widget.playVideo == true) {
      controller = VideoPlayerController.networkUrl(
          Uri.parse('${Preference.getString(Preference.PROFILE_VIDEO)}'));
      controller.addListener(() {
        setState(() {});
      });
      controller.setLooping(true);
      controller.initialize().then((_) => setState(() {
            isVideoLoading = false;
          }));
      controller.play();
    }
  }

  @override
  void dispose() {
    if (widget.editVideo == true || widget.playVideo == true) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          try {
            widget.onBack();
          } catch (e) {}
        }
      },
      child: Scaffold(
        backgroundColor: ColorConstants.BLACK,
        appBar: AppBar(
            elevation: 0,
            title:
                Text(widget.editVideo == true ? 'profile_video' : 'profile_pic')
                    .tr()),
        body: SafeArea(
          child: ScreenWithLoader(
            isLoading: profileLoading,
            body: BlocListener<HomeBloc, HomeState>(
              listener: (context, state) {
                if (state is UploadProfileState) {
                  handleUploadProfile(state);
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.editVideo == true || widget.playVideo == true) ...[
                    Preference.getString(Preference.PROFILE_VIDEO) != null &&
                            Preference.getString(Preference.PROFILE_VIDEO) !=
                                "" &&
                            !isVideoLoading
                        ? SizedBox(
                            height: widget.playVideo == false
                                ? height(context) * 0.76
                                : height(context),
                            child: AspectRatio(
                                aspectRatio: controller.value.aspectRatio,
                                child: VideoPlayer(controller)),
                          )
                        : Preference.getString(Preference.PROFILE_VIDEO) !=
                                    null &&
                                Preference.getString(
                                        Preference.PROFILE_VIDEO) !=
                                    ""
                            ? Container(
                                margin:
                                    EdgeInsets.only(top: height(context) * 0.4),
                                child: CircularProgressIndicator(
                                  color: ColorConstants.WHITE,
                                ))
                            : SizedBox(),
                    Spacer(),
                    if (widget.playVideo == false)
                      Container(
                        padding: EdgeInsets.zero,
                        color: ColorConstants.BLACK,
                        width: width(context),
                        height: height(context) * 0.1,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            icon(
                                tr('upload'),
                                widget.editVideo == true
                                    ? 'assets/images/video.svg'
                                    : 'assets/images/image.svg', () async {
                              _initFilePiker()?.then((value) async {
                                if (value != null) {
                                  Map<String, dynamic> data = Map();
                                  data['video'] = await MultipartFile.fromFile(
                                      '${value.path}',
                                      filename:
                                          '${value.path.split('/').last}');
                                  print('api request $data');

                                  BlocProvider.of<HomeBloc>(context)
                                      .add(UploadProfileEvent(data: data));
                                }
                              });
                            }),
                            Preference.getString(Preference.PROFILE_VIDEO) !=
                                        null &&
                                    Preference.getString(
                                            Preference.PROFILE_VIDEO) !=
                                        ""
                                ? icon(tr('remove'), 'assets/images/delete.svg',
                                    () {
                                    Map<String, dynamic> data = Map();
                                    data['delete'] = 'video';

                                    setState(() {
                                      profileLoading = true;
                                    });
                                    Preference.setString(
                                        Preference.PROFILE_VIDEO, '');
                                    BlocProvider.of<HomeBloc>(context)
                                        .add(UploadProfileEvent(data: data));
                                  })
                                : SizedBox(),
                          ],
                        ),
                      ),
                  ] else ...[
                    Expanded(
                      child: Column(
                        children: [
                          CachedNetworkImage(
                            imageUrl:
                                '${Preference.getString(Preference.PROFILE_IMAGE)}',
                            filterQuality: FilterQuality.low,
                            errorWidget: (context, url, widget) {
                              return SizedBox();
                            },
                            width: width(context),
                            height: height(context) * 0.764,
                            fit: BoxFit.contain,
                          ),
                          Spacer(),
                          Container(
                            padding: EdgeInsets.zero,
                            color: ColorConstants.BLACK,
                            width: width(context),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                icon(tr('upload'), 'assets/images/camera.svg',
                                    () async {
                                  showBottomSheet(context);

                                  // FilePickerResult? result;
                                  // try {
                                  //   if (Platform.isIOS) {
                                  //     result = await FilePicker.platform
                                  //         .pickFiles(
                                  //             allowMultiple: false,
                                  //             type: FileType.image,
                                  //             allowedExtensions: []);
                                  //   } else {
                                  //     result = await FilePicker.platform
                                  //         .pickFiles(
                                  //             allowMultiple: false,
                                  //             type: FileType.custom,
                                  //             allowedExtensions: [
                                  //           'jpg',
                                  //           'png',
                                  //           'jpeg'
                                  //         ]);
                                  //   }
                                  //   // }
                                  // } catch (e) {
                                  //   Log.v('the expection is $e');
                                  // }

                                  // String? value = result?.paths.first;
                                  // if (value != null) {
                                  //   selectedImage = value;
                                  //   selectedImage =
                                  //       await _cropImage(value);
                                  // }
                                  // Map<String, dynamic> data = Map();
                                  // data['image'] =
                                  //     await MultipartFile.fromFile(
                                  //         '$selectedImage',
                                  //         filename: selectedImage);
                                  // BlocProvider.of<HomeBloc>(context).add(
                                  //     UploadProfileEvent(data: data));
                                }),
                                Preference.getString(
                                                Preference.PROFILE_IMAGE) !=
                                            "" &&
                                        Preference.getString(
                                                Preference.PROFILE_IMAGE) !=
                                            null &&
                                        Preference.getString(
                                                Preference.PROFILE_IMAGE) !=
                                            'null'
                                    ? icon(tr('remove'),
                                        'assets/images/delete.svg', () {
                                        Map<String, dynamic> data = Map();
                                        data['delete'] = 'image';
                                        Preference.setString(
                                            Preference.PROFILE_IMAGE, '');
                                        BlocProvider.of<HomeBloc>(context).add(
                                            UploadProfileEvent(data: data));
                                      })
                                    : SizedBox(),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  ]
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget icon(String title, String img, Function action) {
    return Expanded(
      child: InkWell(
        onTap: () {
          action();
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                '$img',
                colorFilter:
                    ColorFilter.mode(ColorConstants.WHITE, BlendMode.srcIn),
              ),
              Text(
                '$title',
                style: Styles.regular(
                  color: ColorConstants.WHITE,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<String> _cropImage(_pickedFile) async {
    if (_pickedFile != null) {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: _pickedFile,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        uiSettings: buildUiSettings(context),
        aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1),
      );
      if (croppedFile != null) {
        return croppedFile.path;
      }
    }
    return "";
  }

  List<String> avatarList = [
    "assets/avatar/hero.jpg",
    "assets/avatar/simple.avif",
    "assets/avatar/cowboy.jpg",
    "assets/avatar/devilboy.png",
    "assets/avatar/smart.webp",
    "assets/avatar/benboy.jpg",
    "assets/avatar/download.jpeg",
    "assets/avatar/girls3.png",
    "assets/avatar/girls4.png",
    "assets/avatar/girls5.png",
  ];

  Future<void> _openCustomDialog(context) async {
    await showGeneralDialog(
        barrierColor: Colors.black.withValues(alpha: 0.5),
        transitionBuilder: (context, a1, a2, widget) {
          return Transform.scale(
            scale: a1.value,
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                shape: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16.0)),
                // shape: RoundedRectangleBorder(
                //     borderRadius: BorderRadius.all(Radius.circular(10.0))),

                content: SingleChildScrollView(
                  child: Column(
                    children: [
                      Text('lbl_choose_avtr').tr(),
                      // SizedBox(height: 10),
                      Container(
                        height: 300,
                        width: 300,
                        child: GridView.builder(
                          itemCount: avatarList.length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 8.0,
                            mainAxisSpacing: 8.0,
                          ),
                          itemBuilder: (context, index) {
                            return Card(
                              child: Center(
                                child: Container(
                                  padding: EdgeInsets.all(8),
                                  // decoration: BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                                  child: ClipOval(
                                    child: SizedBox.fromSize(
                                        size: Size.fromRadius(40),
                                        child: InkWell(
                                          onTap: () async {
                                            Navigator.pop(context);

                                            try {
                                              String filePath =
                                                  avatarList[index].replaceAll(
                                                      'assets/', '');
                                              final byteData = await rootBundle
                                                  .load('assets/$filePath');

                                              final file = File(
                                                  '${(await getTemporaryDirectory()).path}/$filePath');
                                              await file.create(
                                                  recursive: true);
                                              await file.writeAsBytes(
                                                  byteData.buffer.asUint8List(
                                                      byteData.offsetInBytes,
                                                      byteData.lengthInBytes));
                                              log("file path is ${file.path}");
                                              selectedImage = file.path;
                                            } catch (e) {
                                              log("the exception is hgere $e");
                                            }
                                          },
                                          child: Image.asset(
                                            '${avatarList[index]}',
                                          ),
                                        )),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        transitionDuration: Duration(milliseconds: 200),
        barrierDismissible: true,
        barrierLabel: '',
        context: context,
        pageBuilder: (context, animation1, animation2) {
          return Container();
        });
  }

  void showBottomSheet(context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.black,
        builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Center(
                child: Container(
                  padding: EdgeInsets.all(10),
                  margin: EdgeInsets.only(top: 10),
                  height: 4,
                  width: 70,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
              Container(
                child: ListTile(
                  leading: new ShaderMask(
                    blendMode: BlendMode.srcIn,
                    shaderCallback: (Rect bounds) {
                      return LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: <Color>[
                            ColorConstants.WHITE,
                            ColorConstants.WHITE
                          ]).createShader(bounds);
                    },
                    child: Icon(
                      Icons.image,
                    ),
                  ),
                  title: new Text(
                    'gallery',
                    style: TextStyle(color: Colors.white),
                  ).tr(),
                  onTap: () async {
                    FilePickerResult? result;
                    try {
                      if (Platform.isIOS) {
                        result = await FilePicker.platform.pickFiles(
                            allowMultiple: false,
                            type: FileType.image,
                            allowedExtensions: []);
                      } else {
                        result = await FilePicker.platform.pickFiles(
                            allowMultiple: false,
                            type: FileType.custom,
                            allowedExtensions: ['jpg', 'png', 'jpeg']);
                      }
                    } catch (e) {
                      Log.v('the expection is $e');
                    }

                    String? value = result?.paths.first;
                    if (value != null) {
                      selectedImage = value;
                      selectedImage = await _cropImage(value);
                    }
                    setState(() {});
                    Navigator.pop(context);

                    Map<String, dynamic> data = Map();
                    data['image'] = await MultipartFile.fromFile(
                        '$selectedImage',
                        filename: selectedImage);
                    BlocProvider.of<HomeBloc>(context)
                        .add(UploadProfileEvent(data: data));
                  },
                ),
              ),
              Container(
                height: 0.5,
                color: Colors.grey[100],
              ),
              Container(
                child: ListTile(
                  leading: new ShaderMask(
                    blendMode: BlendMode.srcIn,
                    shaderCallback: (Rect bounds) {
                      return LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: <Color>[
                            ColorConstants.WHITE,
                            ColorConstants.WHITE
                          ]).createShader(bounds);
                    },
                    child: Icon(
                      Icons.camera_alt_outlined,
                    ),
                  ),
                  title: new Text(
                    'camera',
                    style: TextStyle(color: Colors.white),
                  ).tr(),
                  onTap: () async {
                    // checkAndRequestCameraPermission();
                    final cameras = await availableCameras();
                    final firstCamera = cameras.first;

                    showDialog(
                        context: context,
                        builder: (context) => TakePictureScreen(
                              camera: firstCamera,
                              cameras: cameras,
                              frontCamera: true,
                            )).then((value) async {
                      if (value != null) {
                        selectedImage = value;
                        selectedImage = await _cropImage(value);
                      }
                      setState(() {});
                      Navigator.pop(context);
                      Map<String, dynamic> data = Map();
                      data['image'] = await MultipartFile.fromFile(
                          '$selectedImage',
                          filename: selectedImage);
                      BlocProvider.of<HomeBloc>(context)
                          .add(UploadProfileEvent(data: data));
                    });
                  },
                ),
              ),
              Container(
                height: 0.5,
                color: Colors.grey[100],
              ),
              Container(
                child: ListTile(
                  leading: new ShaderMask(
                    blendMode: BlendMode.srcIn,
                    shaderCallback: (Rect bounds) {
                      return LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: <Color>[
                            ColorConstants.WHITE,
                            ColorConstants.WHITE
                          ]).createShader(bounds);
                    },
                    child: Icon(
                      Icons.people,
                    ),
                  ),
                  title: new Text(
                    'lbl_avatar_img',
                    style: TextStyle(color: Colors.white),
                  ).tr(),
                  onTap: () async {
                    await _openCustomDialog(context);
                    await Future.delayed(Duration(milliseconds: 400));
                    Navigator.pop(context);
                    try {
                      Map<String, dynamic> data = Map();
                      data['image'] = await MultipartFile.fromFile(
                          '$selectedImage',
                          filename: selectedImage);
                      BlocProvider.of<HomeBloc>(context)
                          .add(UploadProfileEvent(data: data));
                    } catch (e) {
                      log("the exception is $e");
                    }
                  },
                ),
              ),
            ],
          );
        });
  }

  Future<File?>? _initFilePiker() async {
    FilePickerResult? result;

    bool isVideosPermission = true;
    bool isPhotosPermission = true;

    if (Platform.isIOS) {
      try {
        result = await FilePicker.platform.pickFiles(
            allowMultiple: false,
            type: FileType.custom,
            allowedExtensions: ['mov', 'mp4']);
      } catch (e) {
        Log.v('some is not right $e');
      }
    } else {
      DeviceInfoPlugin deviceInfo;
      late AndroidDeviceInfo androidInfo;
      deviceInfo = DeviceInfoPlugin();
      androidInfo = await deviceInfo.androidInfo;

      if (androidInfo.version.sdkInt >= 33) {
        isVideosPermission = await Permission.videos.status.isGranted;
        isPhotosPermission = await Permission.photos.status.isGranted;
      }
      result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.video,
        //allowedExtensions: ['mp4']
      );
    }
    setState(() {
      profileLoading = true;
    });

    if (result != null) {
      if (File(result.paths[0]!).lengthSync() / 1000000 > 50.0) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('${tr('content_file_size_larger_than')} 50MB'),
        ));
        setState(() {
          profileLoading = false;
        });
        return null;
      } else {
        pickedList.add(File(result.paths[0]!));
      }
      pickedFile = pickedList.first;
    } else {
      setState(() {
        profileLoading = false;
      });
      return null;
    }

    if (isVideosPermission && isPhotosPermission) {
      Log.v('Your app cresh ========');
    } else {
      if (Platform.isIOS) {
        result = await FilePicker.platform.pickFiles(
            allowMultiple: false, type: FileType.video, allowedExtensions: []);
      } else {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: FileType.video,
        );
      }
      setState(() {
        profileLoading = true;
      });

      if (result != null) {
        if (File(result.paths[0]!).lengthSync() / 1000000 > 500.0) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${tr('content_file_size_larger_than')} 500MB'),
          ));
        } else {
          pickedList.add(File(result.paths[0]!));
        }
        pickedFile = pickedList.first;
      }
    }

    return pickedFile;
  }

  void handleUploadProfile(UploadProfileState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Upload Profile Loading....................");
          profileLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Upload Profile Success....................");

          profileLoading = false;
          ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('profile_updated_successfully').tr()));
          try {
            widget.onBack();
          } catch (e) {}
          Navigator.pop(context);
          break;

        case ApiStatus.ERROR:
          profileLoading = false;
          Log.v("Upload Profile Error..........................");
          Log.v(
              "Upload Profile Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'edit_profile_page', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}

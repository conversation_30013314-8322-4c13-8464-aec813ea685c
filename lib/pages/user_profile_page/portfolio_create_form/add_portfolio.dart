import 'dart:io';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';

class AddPortfolio extends StatefulWidget {
  final bool? editMode;
  final Portfolio? portfolio;
  final String? baseUrl;
  const AddPortfolio({
    Key? key,
    this.editMode = false,
    this.portfolio,
    this.baseUrl = "",
  }) : super(key: key);

  @override
  State<AddPortfolio> createState() => _AddPortfolioState();
}

class _AddPortfolioState extends State<AddPortfolio> {
  TextEditingController titleController = TextEditingController();
  TextEditingController descController = TextEditingController();
  TextEditingController linkController = TextEditingController();
  File? uploadImg;
  File? file;
  List<File?>? files = [];

  bool? isAddPortfolioLoading = false;

  final _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    updateValue();
    super.initState();
  }

  void updateValue() {
    if (widget.editMode == true) {
      titleController =
          TextEditingController(text: widget.portfolio?.portfolioTitle);
      descController = TextEditingController(text: widget.portfolio?.desc);
      linkController =
          TextEditingController(text: widget.portfolio?.portfolioLink);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (value) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) async {
          if (state is AddPortfolioState) handleAddPortfolio(state);
        },
        child: Scaffold(
            backgroundColor: ColorConstants.WHITE,
            appBar: AppBar(
              backgroundColor: ColorConstants.WHITE,
              elevation: 0,
              leading: SizedBox(),
              centerTitle: true,
              title: Text(
                widget.editMode == true ? "edit_portfolio" : "add_portfolio",
                style: Styles.bold(size: 14, color: Color(0xff0E1638)),
              ).tr(),
              actions: [
                IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(Icons.close_outlined, color: Color(0xff0E1638))),
              ],
            ),
            body: SafeArea(
              child: ScreenWithLoader(
                isLoading: isAddPortfolioLoading,
                body: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                  child: SingleChildScrollView(
                      child: Form(
                    key: _formKey,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${tr('project_title')}*",
                            style: Styles.regular(
                                size: 14, color: Color(0xff0E1638)),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                            validate: true,
                            validationString: tr('please_enter_title'),
                            controller: titleController,
                            maxChar: 50,
                            hintText: tr('project_placeholder'),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            tr('project_description') + "*",
                            style: Styles.regular(
                                size: 14, color: Color(0xff0E1638)),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                            validate: true,
                            validationString: tr('write_your_post'),
                            controller: descController,
                            hintText: tr('type_proj_des_here'),
                            maxLine: 8,
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            "${tr('featured_image')}*",
                            style: Styles.regular(
                                size: 14, color: Color(0xff0E1638)),
                          ),
                          SizedBox(height: 8),
                          InkWell(
                            onTap: () async {
                              FilePickerResult? result;

                              if (Platform.isIOS) {
                                result = await FilePicker.platform.pickFiles(
                                  allowMultiple: false,
                                  type: FileType.image,
                                );
                                if (result != null)
                                  setState(() {
                                    uploadImg = File(result!.files.first.path!);
                                  });
                              } else {
                                final pickedFileC =
                                    await ImagePicker().pickImage(
                                  source: ImageSource.gallery,
                                  imageQuality: 100,
                                );
                                if (pickedFileC != null) {
                                  setState(() {
                                    uploadImg = File(pickedFileC.path);
                                  });
                                }
                              }
                            },
                            child: Row(
                              children: [
                                ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            ColorConstants().gradientLeft(),
                                            ColorConstants().gradientRight()
                                          ]).createShader(bounds);
                                    },
                                    child: Row(
                                      children: [
                                        SvgPicture.asset(
                                            'assets/images/upload_icon.svg'),
                                        Text(
                                          'upload_image',
                                          style: Styles.bold(size: 12),
                                        ).tr(),
                                      ],
                                    )),
                                SizedBox(
                                  width: 4,
                                ),
                                SizedBox(
                                  width: width(context) * 0.6,
                                  child: Text(
                                      uploadImg != null
                                          ? '${uploadImg?.path.split('/').last}'
                                          : widget.portfolio?.imageName ??
                                              "   ${tr('upload_only_image')}",
                                      softWrap: true,
                                      maxLines: 3,
                                      style: Styles.regular(
                                          size: 12, color: Color(0xff929BA3))),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 18),
                          Text(
                            'associate_link',
                            style: Styles.regular(
                                size: 14, color: Color(0xff0E1638)),
                          ).tr(),
                          SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                            controller: linkController,
                            hintText: 'https://www.google.com',
                          ),
                          const SizedBox(
                            height: 40,
                          ),
                          //  if (files?.length != 0)
                          //       Center(
                          //         child: Container(
                          //           margin: const EdgeInsets.all(10),
                          //           padding: const EdgeInsets.all(10),
                          //           decoration: BoxDecoration(
                          //             borderRadius: BorderRadius.circular(12),
                          //             color: ColorConstants.WHITE,
                          //             boxShadow: [
                          //               BoxShadow(
                          //                   color: Color(0xff898989)
                          //                       .withValues(alpha: 0.4),
                          //                   offset: Offset(0, 4.0),
                          //                   blurRadius: 11)
                          //             ],
                          //           ),
                          //           child: ListView.builder(
                          //               shrinkWrap: true,
                          //               itemCount: files?.length,
                          //               physics: NeverScrollableScrollPhysics(),
                          //               itemBuilder: (BuildContext context,
                          //                       int index) =>
                          //                   SizedBox(
                          //                     height: height(context) * 0.042,
                          //                     child: Row(
                          //                       children: [
                          //                         Image.file(files![index]!),

                          //                         SizedBox(
                          //                           width: width(context) * 0.7,
                          //                           child: Text(
                          //                             '${files?[index]?.path.split('/').last}',
                          //                             style: Styles.regular(
                          //                                 size: 12),
                          //                             overflow:
                          //                                 TextOverflow.ellipsis,
                          //                           ),
                          //                         ),
                          //                         IconButton(
                          //                             onPressed: () {
                          //                               setState(() {
                          //                                 files
                          //                                     ?.removeAt(index);
                          //                               });
                          //                             },
                          //                             icon: Icon(Icons.delete,
                          //                                 size: 18))
                          //                       ],
                          //                     ),
                          //                   )),
                          //         ),
                          //       ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8),
                                child: CustomUpload(
                                  onClick: () async {
                                    FilePickerResult? result;

                                    if (Platform.isIOS) {
                                      result = await FilePicker.platform
                                          .pickFiles(
                                              allowMultiple: false,
                                              type: FileType.custom,
                                              allowedExtensions: [
                                            'jpg',
                                            'jpeg',
                                            'png',
                                            'pdf',
                                            'mp4'
                                          ]);
                                      if (result != null) {
                                        // for (var file in result.files) {
                                        //   if (files!.length >= 5) {
                                        //     break;
                                        //   } else{
                                        //     files?.add(File(file.path!));
                                        //   }
                                        // }
                                        file =
                                            File('${result.files.first.path}');
                                        setState(() {});
                                      }
                                    } else {
                                      FilePickerResult? pickedFileC =
                                          await FilePicker.platform.pickFiles(
                                              type: FileType.custom,
                                              allowedExtensions: [
                                                'jpg',
                                                'jpeg',
                                                'png',
                                                'pdf',
                                                'mp4'
                                              ],
                                              allowMultiple: false);

                                      if (pickedFileC != null) {
                                        // for (var file in pickedFileC.files) {
                                        //   if (files!.length >= 5) {
                                        //     break;
                                        //   } else{
                                        //     files?.add(File(file.path!));
                                        //   }
                                        // }
                                        file = File(
                                            '${pickedFileC.files.first.path}');
                                        setState(() {});
                                      }
                                    }
                                  },
                                  uploadText: '${tr('upload_file')}*',
                                ),
                              ),
                              SizedBox(
                                width: width(context) * 0.9,
                                child: Text(
                                    file != null
                                        ? '${file?.path.split('/').last}'
                                        : widget.portfolio?.portfolioFile ??
                                            tr('upload_all_format'),
                                    maxLines: 3,
                                    textAlign: TextAlign.center,
                                    style: Styles.regular(
                                        size: 12, color: Color(0xff929BA3))),
                              ),
                            ],
                          ),
                          PortfolioCustomButton(clickAction: () async {
                            if (_formKey.currentState!.validate()) {
                              Map<String, dynamic> data = Map();
                              setState(() {
                                isAddPortfolioLoading = true;
                              });
                              try {
                                if (widget.editMode == true) {
                                  if (uploadImg?.path != null) {
                                    String? portfolioImage =
                                        uploadImg?.path.split('/').last;
                                    data['portfolio_image'] =
                                        await MultipartFile.fromFile(
                                            '${uploadImg?.path}',
                                            filename: portfolioImage);
                                    // await Utility()
                                    //     .s3UploadFile('${uploadImg?.path}')
                                    //     .then((value) =>
                                    //         data['portfolio_image_cdn']);
                                  }
                                  if (file?.path != null) {
                                    String? portfolioFile =
                                        file?.path.split('/').last;
                                    data['portfolio_file'] =
                                        await MultipartFile.fromFile(
                                            '${file?.path}',
                                            filename: portfolioFile);
                                    // await Utility()
                                    //     .s3UploadFile('${file?.path}')
                                    //     .then((value) =>
                                    //         data['portfolio_file_cdn']);
                                  }
                                } else {
                                  String? portfolioImage =
                                      uploadImg?.path.split('/').last;
                                  String? portfolioFile =
                                      file?.path.split('/').last;
                                  data['portfolio_image'] =
                                      await MultipartFile.fromFile(
                                          '${uploadImg?.path}',
                                          filename: portfolioImage);

                                  data['portfolio_file'] =
                                      await MultipartFile.fromFile(
                                          '${file?.path}',
                                          filename: portfolioFile);

                                  // await Utility()
                                  //     .s3UploadFile('${uploadImg?.path}')
                                  //     .then((value) =>
                                  //         data['portfolio_image_cdn']);
                                  // await Utility()
                                  //     .s3UploadFile('${file?.path}')
                                  //     .then((value) =>
                                  //         data['portfolio_file_cdn']);
                                }
                              } catch (e) {
                                setState(() {
                                  isAddPortfolioLoading = false;
                                });
                              }

                              if (widget.editMode == false &&
                                  (file?.path == null ||
                                      uploadImg?.path == null)) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content: Text(file?.path == null
                                                ? 'please_upload_file'
                                                : 'plz_add_feature_img')
                                            .tr()));
                                setState(() {
                                  isAddPortfolioLoading = false;
                                });
                              } else {
                                data['portfolio_title'] =
                                    titleController.value.text;
                                data['portfolio_link'] =
                                    linkController.value.text;
                                data['portfolio_key'] = widget.editMode == true
                                    ? "portfolio_${widget.portfolio?.id}"
                                    : 'new_portfolio';
                                data['edit_file_portfolio'] =
                                    '${widget.portfolio?.portfolioFile}';
                                data['edit_url_portfolio'] =
                                    '${widget.portfolio?.imageName}';

                                data['desc'] = descController.value.text;
                                addPortfolio(data);
                              }
                            }
                          })
                        ]),
                  )),
                ),
              ),
            )),
      ),
    );
  }

  void addPortfolio(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context).add(AddPortfolioEvent(data: data));
  }

  void handleAddPortfolio(AddPortfolioState state) {
    var addPortfolioState = state;
    setState(() {
      switch (addPortfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add Portfolio....................");
          isAddPortfolioLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add Portfolio....................");
          isAddPortfolioLoading = false;
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(widget.editMode == true
                    ? 'portfolio_updated_successfully'
                    : 'portfolio_added_successfully')
                .tr(),
          ));
          Navigator.pop(context);
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Portfolio....................");
          isAddPortfolioLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'add_portfolio', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}

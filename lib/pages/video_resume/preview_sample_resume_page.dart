import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class PreviewSampleResume extends StatefulWidget {
  final String? previewUrl;
  final String title;
  final String? msg;

  const PreviewSampleResume({
    super.key,
    required this.previewUrl,
    required this.title,
    this.msg,
  });

  @override
  State<PreviewSampleResume> createState() => _PreviewSampleResumeState();
}

class _PreviewSampleResumeState extends State<PreviewSampleResume> {
  bool? isLoading = false;
  TrainingModuleResponse? trainingModuleResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorConstants.GREY,
        appBar: AppBar(
          title: Text(widget.title, style: Styles.bold(size: 14)).tr(),
          backgroundColor: Colors.white,
          elevation: 0,
          leading: Transform.scale(
            scale: 0.7,
            child: IconButton(
              icon: Icon(
                Icons.arrow_back,
                size: 30,
                color: ColorConstants.BLACK,
              ),
              onPressed: () {
                try {
                  Navigator.pop(context);
                } catch (e) {
                  print('exc $e');
                }
              },
            ),
          ),
        ),
        body: Center(
          child: Container(
              //height: MediaQuery.of(context).size.height * 0.5,
              height: MediaQuery.of(context).size.height,
              width: double.infinity,
              child: _pageItem()),
        ));
  }

  Widget _pageItem() {
    if (widget.previewUrl!.toLowerCase().endsWith('.pdf')) {
      return RotatedBox(
        quarterTurns: 0,
        child: PDF(
          swipeHorizontal: false,
          autoSpacing: true,
          enableSwipe: true,
          gestureRecognizers: [
            Factory(() => PanGestureRecognizer()),
            Factory(() => VerticalDragGestureRecognizer())
          ].toSet(),
          //onPageChanged: ,
        ).cachedFromUrl(
          maxAgeCacheObject: Duration(seconds: 10),
          widget.previewUrl!,
          placeholder: (progress) => Center(child: Text('$progress %')),
          errorWidget: (error) => Center(
              child: Text(widget.msg!, style: Styles.bold(size: 16)).tr()),
        ),
      );
    } else if (widget.previewUrl!.toLowerCase().endsWith('.png') ||
        widget.previewUrl!.toLowerCase().endsWith('.jpeg') ||
        widget.previewUrl!.toLowerCase().endsWith('.png')) {
      return Center(
        child: InteractiveViewer(
          minScale: 0.2,
          maxScale: 5.0,
          child: Image.network(
            widget.previewUrl!,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) => Center(
                child:
                    Text('Failed to load image', style: Styles.bold(size: 16))),
          ),
        ),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.only(top: 50.0),
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          Center(
            child: Text(
              widget.msg!,
              style: Styles.bold(size: 16),
            ),
          )
        ]),
      );
    }
  }
}

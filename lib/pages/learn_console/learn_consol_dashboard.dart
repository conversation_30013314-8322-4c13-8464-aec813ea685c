import 'dart:convert';
import 'dart:ui' as ui;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/Attendance_percentage_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_class_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_details_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/mark_attendance_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/program_completion_resp.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/rounded_appbar.dart';
import 'package:masterg/pages/learn_console/attendance_view.dart';
import 'package:masterg/pages/learn_console/faculty_assessment.dart';
import 'package:masterg/pages/learn_console/faculty_assignment.dart';
import 'package:masterg/pages/learn_console/faculty_classess.dart';
import 'package:masterg/pages/learn_console/faculty_console_program_details.dart';
import 'package:masterg/pages/learn_console/faculty_program_list.dart';
import 'package:masterg/pages/learn_console/module_leader_program_list.dart';
import 'package:masterg/pages/learn_console/view_recording.dart';
import 'package:masterg/pages/singularis/app_drawer_page.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/extensions/widget_extensions.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'leader_console_program_details.dart';

class LearnConsolDashboard extends StatefulWidget {
  const LearnConsolDashboard({Key? key}) : super(key: key);

  @override
  _LearnConsolDashboardState createState() => _LearnConsolDashboardState();
}

class _LearnConsolDashboardState extends State<LearnConsolDashboard>
    with TickerProviderStateMixin {
  int? selectedSwiperIndex = 0;
  int? selectedFacultySwiper = 0;
  bool? selectedVewAttendance = true;
  bool? selectedVewRecording = false;

  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  MenuListProvider? menuProvider;
  FacultyBatchDetailsResponse? programModuleLead;
  FacultyBatchClassResponse? facultyClass;
  ProgramCompletionResponse? programCompletion;
  ProgramCompletionResponse? programCompletionFaculty;
  AttendancePercentageResponse? attendancePercentage;
  SwiperController? controller = SwiperController();
  // SwiperController? _controller = SwiperController();
  List<LiveClassUser>? liveClassUsers;

  String? currentZoomUrl;
  String? currentOpenUrl;
  bool? isFacultyDetails = false;
  bool? isLoading = false;
  bool? isModuleLead = false;
  bool? isFacultyLead = false;
  int? selectedProgram;
  Box? box;

  bool isModuleExpanded = false;
  var formatter = NumberFormat("#0.00");
  int? selectedIndex;
  int? selectedIndexFaculty;
  int? apiCallRoot;

  @override
  void initState() {
    super.initState();
    _getFacultyBatchDetails();
    _getFacultyBatchClass();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget build(BuildContext context) {
    return BlocManager(
      initState: (context) {},
      child: Consumer<MenuListProvider>(
        builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is FacultyBatchDetailsState) {
                _handleFacultyBatchDetailsState(state);
              }
              if (state is FacultyBatchClassState) {
                _handleFacultyBatchClassState(state);
              }
              if (state is ProgramCompletionState) {
                _handleProgramCompletionState(state);
              }
              if (state is AttendancePercentageState) {
                _handleAttendancePercentageState(state);
              }
              if (state is MarkAttendanceState) {
                _handleMarkAttendanceState(state);
              }
            },
            child: Scaffold(
              backgroundColor: ColorConstants.GREY,
              key: _scaffoldKey,
              endDrawer: new AppDrawer(),
              body: SafeArea(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // _customAppBar(),
                      RoundedAppBar(
                          appBarHeight: height(context) *
                              (Utility().isRTL(context) ? 0.18 : 0.16),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        InkWell(
                                          onTap: () async {
                                            if (Preference.getString(
                                                    Preference.ROLE) ==
                                                'Learner') {
                                              Navigator.push(
                                                      context,
                                                      NextPageRoute(
                                                          NewPortfolioPage()))
                                                  .then((value) {
                                                if (value != null)
                                                  menuProvider
                                                      ?.updateCurrentIndex(
                                                          value);
                                              });
                                            } else {
                                              Navigator.push(
                                                  context,
                                                  NextPageRoute(
                                                      NewPortfolioPage(
                                                    expJobResume: false,
                                                  ))).then((value) {
                                                if (value != null)
                                                  menuProvider
                                                      ?.updateCurrentIndex(
                                                          value);
                                              });
                                            }
                                          },
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(200),
                                            child: SizedBox(
                                              width: 50,
                                              height: 50,
                                              child: CachedNetworkImage(
                                                imageUrl:
                                                    '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                                fit: BoxFit.cover,
                                                placeholder: (context, url) =>
                                                    SvgPicture.asset(
                                                  'assets/images/default_user.svg',
                                                  width: 50,
                                                  height: 50,
                                                ),
                                                errorWidget:
                                                    (context, url, error) =>
                                                        SvgPicture.asset(
                                                  'assets/images/default_user.svg',
                                                  width: 50,
                                                  height: 50,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: width(context) * 0.6,
                                              child: Text('welcome_text',
                                                      style: Styles.regular(
                                                          color: ColorConstants
                                                              .WHITE,
                                                          size: 12))
                                                  .tr(),
                                            ),
                                            SizedBox(
                                              width: width(context) * 0.6,
                                              child: Text(
                                                Utility().decrypted128(
                                                    '${Preference.getString(Preference.FIRST_NAME)}'),
                                                //Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)?[0].toUpperCase()} ${Preference.getString(Preference.FIRST_NAME)?.substring(1).toLowerCase()}'),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: Styles.bold(
                                                    color: ColorConstants.WHITE,
                                                    size: 22),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: Align(
                                        alignment: Utility().isRTL(context)
                                            ? Alignment.topLeft
                                            : Alignment.topRight,
                                        child: InkWell(
                                          onTap: () {
                                            _scaffoldKey.currentState
                                                ?.openEndDrawer();
                                          },
                                          child: SvgPicture.asset(
                                              'assets/images/hamburger_menu.svg'),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 12),
                              ],
                            ),
                          )),
                      //TODO: Filter Section
                      SizedBox(
                        height: 5,
                      ),
                      selectedProgram != null
                          ? GestureDetector(
                              onTap: () {
                                setState(() {
                                  programModuleLead = null;
                                  selectedProgram = null;
                                });
                                _getFacultyBatchDetails();
                                _getFacultyBatchClass();
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Icon(
                                    Icons.refresh,
                                    color: Colors.lightBlue,
                                    size: 15,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(5.0),
                                    child: Text(
                                      'reset_button',
                                      style: Styles.bold(
                                          size: 14, color: Colors.lightBlue),
                                    ).tr(),
                                  ),
                                ],
                              ),
                            )
                          : SizedBox(),
                      programModuleLead?.data?.facultyCourses!.length != 0
                          ? Container(
                              height: MediaQuery.of(context).size.height * 0.05,
                              margin: const EdgeInsets.symmetric(
                                  vertical: 3, horizontal: 10),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 8),
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                shadows: const [],
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton2<int>(
                                  isExpanded: true,
                                  hint: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          'select_course',
                                          style: Styles.regular(size: 14),
                                          overflow: TextOverflow.ellipsis,
                                        ).tr(),
                                      ),
                                    ],
                                  ),
                                  items: programModuleLead
                                                  ?.data?.facultyCourses ==
                                              null &&
                                          programModuleLead?.data
                                                  ?.facultyCourses?.isEmpty ==
                                              true
                                      ? []
                                      : programModuleLead?.data?.facultyCourses
                                              ?.map((value) {
                                            return DropdownMenuItem<int>(
                                              value: value.courseId,
                                              child: SizedBox(
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.7,
                                                child: Text(value.courseName!,
                                                    maxLines: 2,
                                                    style: const TextStyle(
                                                        color: Colors.black,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: 14)),
                                              ),
                                            );
                                          }).toList() ??
                                          [],
                                  value: selectedProgram,
                                  onChanged: (int? value) {
                                    setState(() {
                                      if (value != null) {
                                        _getFacultyBatchDetails(
                                            courseId: value);
                                        selectedProgram = value;
                                      }
                                    });
                                  },
                                ),
                              ),
                            )
                          : SizedBox(),
                      SizedBox(
                        height: 15,
                      ),

                      if (programModuleLead?.data?.programsModuleLead != null &&
                          programModuleLead?.data?.programsModuleLead?.length !=
                              0) ...[
                        Container(
                          color: ColorConstants.GREY,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 14, vertical: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Row(children: [
                                  Text(
                                    'module_leader_console',
                                    style: Styles.bold(size: 16),
                                  ).tr(),
                                  Spacer(),
                                  InkWell(
                                      onTap: () {
                                        Navigator.push(
                                            context,
                                            NextPageRoute(
                                                ModuleLeaderProgramList(
                                              // programModuleLead:
                                              //     programModuleLead,
                                              index: 0,
                                              programCompletion:
                                                  programCompletion,
                                              attendancePercentage:
                                                  attendancePercentage,
                                              onExpension: () {
                                                _getProgramCompletion(
                                                    programCompId:
                                                        programModuleLead
                                                            ?.data
                                                            ?.programsModuleLead?[
                                                                0]
                                                            .id);
                                                _getAttendancePercentage(
                                                    prgramCompletionId:
                                                        programModuleLead
                                                            ?.data
                                                            ?.programsModuleLead?[
                                                                0]
                                                            .id);
                                              },
                                            )));
                                      },
                                      child: Text('view_all',
                                              style: Styles.regular(size: 12))
                                          .tr())
                                ]),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Divider(thickness: 1),
                        ),
                        programModuleLead?.data?.programsModuleLead != null
                            ? moduleLeaderConsol()
                            : Container(
                                width: width(context),
                                height: width(context) * 0.5251870324189526,
                                child: Shimmer.fromColors(
                                  baseColor: Colors.grey[300]!,
                                  highlightColor: Colors.grey[100]!,
                                  enabled: true,
                                  child: Container(
                                    margin: const EdgeInsets.all(0),
                                    decoration: BoxDecoration(
                                        color: ColorConstants.WHITE,
                                        borderRadius:
                                            BorderRadius.circular(20)),
                                    width: 100,
                                    height: 13,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.horizontal(
                                        left: Radius.circular(10),
                                        right: Radius.circular(10),
                                      ),
                                      child: LinearProgressIndicator(
                                        minHeight: 10,
                                        backgroundColor: Colors.grey[300],
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                ColorConstants.GREY),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                        SizedBox(height: 10),
                      ] else ...[
                        isFacultyDetails == true ? BlankPage() : SizedBox(),
                      ],

                      SizedBox(height: 10),
                      if (programModuleLead?.data?.programsModuleFaculty !=
                              null &&
                          programModuleLead
                                  ?.data?.programsModuleFaculty?.length !=
                              0) ...[
                        Container(
                          color: ColorConstants.GREY,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 14, vertical: 8),
                            child: Row(children: [
                              Text(
                                'faculty_console',
                                style: Styles.bold(size: 16),
                              ).tr(),
                              Spacer(),
                              InkWell(
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        NextPageRoute(FacultyProgramList(
                                          index: 0,
                                          programCompletion: programCompletion,
                                          attendancePercentage:
                                              attendancePercentage,
                                          onExpension: () {
                                            _getProgramCompletion(
                                                programCompId: programModuleLead
                                                    ?.data
                                                    ?.programsModuleFaculty?[0]
                                                    .id);
                                            _getAttendancePercentage(
                                                prgramCompletionId:
                                                    programModuleLead
                                                        ?.data
                                                        ?.programsModuleFaculty?[
                                                            0]
                                                        .id);
                                          },
                                        )));
                                  },
                                  child: Text('view_all',
                                          style: Styles.regular(size: 12))
                                      .tr())
                            ]),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Divider(thickness: 1),
                        ),
                        facultyConsol(),
                        SizedBox(height: 10),
                      ] else ...[
                        isFacultyDetails == true ? BlankPage() : SizedBox(),
                      ],

                      if (programModuleLead?.data?.ongoingLiveClass != null &&
                          programModuleLead?.data?.ongoingLiveClass?.length !=
                              0) ...[
                        Container(
                          color: ColorConstants.GREY,
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 14.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'scheduled_sessions',
                                  style: Styles.bold(size: 16),
                                ).tr(),
                                Text(
                                  'live_and_scheduled',
                                  style: Styles.textRegular(size: 12),
                                ).tr()
                              ],
                            ),
                          ),
                        ),
                        todayClasses(),
                        SizedBox(
                          height: 10,
                        ),
                      ],

                      if (programModuleLead?.data?.concludedLiveClass != null &&
                          programModuleLead?.data?.concludedLiveClass?.length !=
                              0) ...[
                        Container(
                          color: ColorConstants.GREY,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 14, vertical: 4),
                            child: Text(
                              'concluded',
                              style: Styles.regular(size: 16),
                            ).tr(),
                          ),
                        ),
                        concludedClassess(),
                      ],

                      SizedBox(height: 10),
                      Container(
                        color: ColorConstants.GREY,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 4),
                          child: Text(
                            'summary',
                            style: Styles.regular(size: 16),
                          ).tr(),
                        ),
                      ),
                      summary(),
                    ],
                  ),
                ),
              ),
            )),
      ),
    );
  }

  moduleLeaderConsol() {
    return SizedBox(
      height: height(context) * 0.3,
      child: programModuleLead?.data?.programsModuleLead != null &&
              programModuleLead?.data?.programsModuleLead?.length != 0
          ? ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount:
                  programModuleLead?.data?.programsModuleLead?.length ?? 0,
              itemBuilder: (context, index) {
                //singh

                /*return ModuleLeaderConsoleCard(
                  programModuleLead: programModuleLead,
                  index: index,
                  programCompletion: programCompletion,
                  attendancePercentage: attendancePercentage,
                  onExpension: () {
                    print('ModuleLeaderConsoleCard---------');
                    _getProgramCompletion(
                        programCompId: programModuleLead?.data?.programsModuleLead?[index].id);
                    _getAttendancePercentage(
                        prgramCompletionId: programModuleLead?.data?.programsModuleLead?[index].id);
                  },
                );*/

                return Stack(children: [
                  InkWell(
                    onTap: () {
                      Navigator.push(
                          context,
                          NextPageRoute(
                              LeaderProgramDetailsPage(
                                image:
                                    '${programModuleLead?.data?.programsModuleLead?[index].image}',
                                programName: programModuleLead
                                    ?.data?.programsModuleLead?[index].name,
                                description: programModuleLead?.data
                                    ?.programsModuleLead?[index].description,
                                startDate:
                                    '${programModuleLead?.data?.programsModuleLead?[index].startDate}',
                                endDate:
                                    '${programModuleLead?.data?.programsModuleLead?[index].endDate}',
                                facultyNames:
                                    '${programModuleLead?.data?.programsModuleLead?[index].facultyNames}',
                                totLearners:
                                    '${programModuleLead?.data?.programsModuleLead?[index].totLearners}',
                                programId: programModuleLead
                                    ?.data?.programsModuleLead?[index].id,
                                status:
                                    '${programModuleLead?.data?.programsModuleLead?[index].status}',
                                category:
                                    '${programModuleLead?.data?.programsModuleLead?[index].createdBy}',
                              ),
                              isMaintainState: true));
                    },
                    child: Container(
                      height: MediaQuery.of(context).size.height * 0.35,
                      margin:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      width:
                          programModuleLead?.data?.programsModuleLead?.length ==
                                  1
                              ? MediaQuery.of(context).size.width * 0.95
                              : MediaQuery.of(context).size.width * 0.88,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: 85,
                                  height: 90,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: CachedNetworkImage(
                                      imageUrl:
                                          '${programModuleLead?.data?.programsModuleLead?[index].image ?? ''}',
                                      width: 100,
                                      height: 120,
                                      errorWidget: (context, url, error) =>
                                          SvgPicture.asset(
                                        'assets/images/gscore_postnow_bg.svg',
                                      ),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Container(
                                            width: width(context) * 0.38,
                                            margin: EdgeInsets.only(
                                                left: 9, top: 3),
                                            child: Text(
                                                '${programModuleLead?.data?.programsModuleLead?[index].name ?? ''}',
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                softWrap: false,
                                                style:
                                                    Styles.semibold(size: 16))),
                                        programModuleLead
                                                    ?.data
                                                    ?.programsModuleLead?[index]
                                                    .batchName ==
                                                null
                                            ? SizedBox()
                                            : Container(
                                                width: width(context) * 0.20,
                                                decoration: BoxDecoration(
                                                    color: Color(0xffF0F1FA),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                    border: Border.all(
                                                        color:
                                                            Color(0xffF0F1FA),
                                                        width: 2)),
                                                margin: EdgeInsets.only(
                                                    left: 5, top: 3),
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(4.0),
                                                  child: Center(
                                                    child: ShaderMask(
                                                      blendMode:
                                                          BlendMode.srcIn,
                                                      shaderCallback:
                                                          (Rect bounds) {
                                                        return LinearGradient(
                                                            begin: Alignment
                                                                .centerLeft,
                                                            end: Alignment
                                                                .centerRight,
                                                            colors: <Color>[
                                                              ColorConstants()
                                                                  .gradientLeft(),
                                                              ColorConstants()
                                                                  .gradientRight()
                                                            ]).createShader(
                                                            bounds);
                                                      },
                                                      child: Text(
                                                          '${programModuleLead?.data?.programsModuleLead?[index].batchName ?? ''}',
                                                          maxLines: 2,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          softWrap: false,
                                                          style:
                                                              Styles.semibold(
                                                            size: 10,
                                                          )),
                                                    ),
                                                  ),
                                                )),
                                      ],
                                    ),
                                    Container(
                                        width: width(context) * 0.5,
                                        margin:
                                            EdgeInsets.only(left: 0, top: 3),
                                        child: Utility().isHtml(
                                                '${programModuleLead?.data?.programsModuleLead?[index].description ?? ''}')
                                            ? Html(
                                                data:
                                                    '${programModuleLead?.data?.programsModuleLead?[index].description ?? ''}',
                                              )
                                            : Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10.0),
                                                child: Text(
                                                    '${programModuleLead?.data?.programsModuleLead?[index].description ?? ''}',
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    softWrap: false,
                                                    style: Styles.regular(
                                                        size: 12)),
                                              )),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Container(
                              width: width(context) * 0.6,
                              margin: EdgeInsets.only(left: 9, top: 3),
                              child: Row(
                                children: [
                                  Icon(Icons.calendar_month_outlined, size: 20),
                                  SizedBox(width: 10),
                                  SizedBox(
                                    child: Text(
                                      overflow: TextOverflow.ellipsis,
                                      softWrap: false,
                                      '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.programsModuleLead?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} To  ${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.programsModuleLead?[index].endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                                      style: Styles.semibold(
                                          size: 12, color: Color(0xff0E1638)),
                                      textDirection: ui.TextDirection.ltr,
                                    ),
                                  ),
                                ],
                              )),
                          Container(
                            // width: width(context),
                            margin: EdgeInsets.only(left: 9, top: 9, right: 9),
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/images/person.svg',
                                  height: 20.0,
                                  width: 20.0,
                                  allowDrawingOutsideViewBox: true,
                                ),
                                SizedBox(width: 10),
                                Text('${programModuleLead?.data?.programsModuleLead?[index].facultyNames ?? ''}',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        softWrap: false,
                                        style: Styles.regular(size: 12))
                                    .expanded(),
                                // Spacer(),
                                Row(
                                  children: [
                                    SvgPicture.asset(
                                      'assets/images/local_library.svg',
                                      height: 20.0,
                                      width: 20.0,
                                      allowDrawingOutsideViewBox: true,
                                    ),
                                    SizedBox(width: 5),
                                    Text(
                                        '${programModuleLead?.data?.programsModuleLead?[index].totLearners ?? '0'}',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        softWrap: false,
                                        style: Styles.regular(size: 12)),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                        height:
                            selectedIndex == index && isModuleExpanded == true
                                ? 130
                                : 60, //isModuleExpanded == true
                        //height: 130,
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                            color: Color(0xffF1FBFF),
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            border: Border.all(color: Color(0xffF1FBFF))),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            InkWell(
                              onTap: () {
                                apiCallRoot = 0;
                                programCompletion?.programCompletion = 0;
                                if (selectedIndex != index) {
                                  _getProgramCompletion(
                                      programCompId: programModuleLead?.data
                                          ?.programsModuleLead?[index].id);
                                  setState(() {
                                    selectedIndex = index;
                                    isModuleExpanded = true;
                                  });
                                } else {
                                  setState(() {
                                    selectedIndex = null;
                                    isModuleExpanded = false;
                                  });
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(top: 15.0),
                                child: Row(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          left: 16.0, top: 0.0, bottom: 0.0),
                                      child: Text(
                                        'batch_completion',
                                        style: Styles.bold(),
                                      ).tr(),
                                    ),
                                    Spacer(),
                                    Padding(
                                      padding:
                                          const EdgeInsets.only(right: 10.0),
                                      child: selectedIndex == index &&
                                              isModuleExpanded == true
                                          ? Icon(
                                              Icons.keyboard_arrow_up,
                                              size: 20,
                                            )
                                          : Icon(
                                              Icons.keyboard_arrow_down,
                                              size: 20,
                                            ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            selectedIndex == index && isModuleExpanded == true
                                ? Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${tr('program_completion')} ${formatter.format(programCompletion?.programCompletion ?? 0.0 ?? 0)}%',
                                          style: Styles.regular(),
                                        ).tr(),
                                        SizedBox(height: 5),
                                        ClipRRect(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(30)),
                                          child: LinearProgressIndicator(
                                            minHeight: 6,
                                            value: (programCompletion
                                                        ?.programCompletion ??
                                                    0) /
                                                100,
                                            backgroundColor: Colors.grey[300],
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              ColorConstants().gradientLeft(),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : SizedBox(),
                          ],
                        )
                        //
                        ),
                  ),
                ]);
              },
            )
          : SizedBox(),
    );
  }

  facultyConsol() {
    return Container(
        height: height(context) * 0.34,
        width: width(context),
        child: programModuleLead != null
            ? ListView.builder(
                scrollDirection: Axis.horizontal,
                itemBuilder: (BuildContext context, int index) {
                  return Stack(children: [
                    InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            NextPageRoute(
                                FacultyProgramDetailsPage(
                                  image:
                                      '${programModuleLead?.data?.programsModuleFaculty?[index].image}',
                                  programName: programModuleLead?.data
                                      ?.programsModuleFaculty?[index].name,
                                  description: programModuleLead
                                      ?.data
                                      ?.programsModuleFaculty?[index]
                                      .description,
                                  startDate:
                                      '${programModuleLead?.data?.programsModuleFaculty?[index].startDate}',
                                  endDate:
                                      '${programModuleLead?.data?.programsModuleFaculty?[index].endDate}',
                                  facultyNames:
                                      '${programModuleLead?.data?.programsModuleFaculty?[index].facultyNames}',
                                  totLearners:
                                      '${programModuleLead?.data?.programsModuleFaculty?[index].totLearners}',
                                  programId: programModuleLead
                                      ?.data?.programsModuleFaculty?[index].id,
                                  status:
                                      '${programModuleLead?.data?.programsModuleFaculty?[index].status}',
                                  category:
                                      '${programModuleLead?.data?.programsModuleFaculty?[index].createdBy}',
                                ),
                                isMaintainState: true));
                      },
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.3,
                        margin:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                        width: MediaQuery.of(context).size.width * 0.87,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 85,
                                    height: 90,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: CachedNetworkImage(
                                        imageUrl:
                                            '${programModuleLead?.data?.programsModuleFaculty?[index].image ?? ''}',
                                        width: 100,
                                        height: 120,
                                        errorWidget: (context, url, error) =>
                                            SvgPicture.asset(
                                          'assets/images/gscore_postnow_bg.svg',
                                        ),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                              width: width(context) * 0.38,
                                              margin: EdgeInsets.only(
                                                  left: 9, top: 3),
                                              child: Text(
                                                  '${programModuleLead?.data?.programsModuleFaculty?[index].name ?? ''}',
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  softWrap: false,
                                                  style: Styles.semibold(
                                                      size: 16))),
                                          // SizedBox(width: 80),
                                          programModuleLead
                                                      ?.data
                                                      ?.programsModuleFaculty?[
                                                          index]
                                                      .batchName !=
                                                  null
                                              ? Container(
                                                  width: width(context) * 0.20,
                                                  decoration: BoxDecoration(
                                                      color: Color(0xffF0F1FA),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      border: Border.all(
                                                          color:
                                                              Color(0xffF0F1FA),
                                                          width: 2)),
                                                  margin: EdgeInsets.only(
                                                      left: 1,
                                                      top: 3,
                                                      right: 0),
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            4.0),
                                                    child: Center(
                                                      child: ShaderMask(
                                                        blendMode:
                                                            BlendMode.srcIn,
                                                        shaderCallback:
                                                            (Rect bounds) {
                                                          return LinearGradient(
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                              colors: <Color>[
                                                                ColorConstants()
                                                                    .gradientLeft(),
                                                                ColorConstants()
                                                                    .gradientRight()
                                                              ]).createShader(
                                                              bounds);
                                                        },
                                                        child: Text(
                                                            '${programModuleLead?.data?.programsModuleFaculty?[index].batchName ?? ''}',
                                                            maxLines: 2,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            softWrap: false,
                                                            style:
                                                                Styles.semibold(
                                                              size: 10,
                                                            )),
                                                      ),
                                                    ),
                                                  ))
                                              : SizedBox(),
                                        ],
                                      ),
                                      Container(
                                          width: width(context) * 0.5,
                                          margin:
                                              EdgeInsets.only(left: 9, top: 3),
                                          child: Utility().isHtml(
                                                  '${programModuleLead?.data?.programsModuleFaculty?[index].description ?? ''}')
                                              ? Html(
                                                  data:
                                                      '${programModuleLead?.data?.programsModuleFaculty?[index].description ?? ''}',
                                                )
                                              : Text(
                                                  '${programModuleLead?.data?.programsModuleFaculty?[index].description ?? ''}',
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  softWrap: false,
                                                  style: Styles.regular(
                                                      size: 12))),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Container(
                                width: width(context) * 0.6,
                                margin: EdgeInsets.only(left: 9, top: 3),
                                child: Row(
                                  children: [
                                    Icon(Icons.calendar_month_outlined,
                                        size: 20),
                                    SizedBox(width: 5),
                                    SizedBox(
                                      child: Text(
                                        overflow: TextOverflow.ellipsis,
                                        softWrap: false,
                                        '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.programsModuleFaculty?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} To  ${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.programsModuleFaculty?[index].endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                                        style: Styles.semibold(
                                            size: 12, color: Color(0xff0E1638)),
                                        textDirection: ui.TextDirection.ltr,
                                      ),
                                    ),
                                  ],
                                )),
                            Container(
                              margin:
                                  EdgeInsets.only(left: 9, top: 3, right: 9),
                              child: Row(
                                children: [
                                  if (programModuleLead
                                          ?.data
                                          ?.programsModuleFaculty?[index]
                                          .facultyNames
                                          ?.isNotEmpty ==
                                      true) ...[
                                    SvgPicture.asset(
                                      'assets/images/person.svg',
                                      height: 20.0,
                                      width: 20.0,
                                      allowDrawingOutsideViewBox: true,
                                    ),
                                    SizedBox(width: 5),
                                    Text(
                                        '${programModuleLead?.data?.programsModuleFaculty?[index].facultyNames ?? ''}',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        softWrap: false,
                                        style: Styles.regular(size: 12)),
                                  ],
                                  Spacer(),
                                  Row(
                                    children: [
                                      SvgPicture.asset(
                                        'assets/images/local_library.svg',
                                        height: 20.0,
                                        width: 20.0,
                                        allowDrawingOutsideViewBox: true,
                                      ),
                                      SizedBox(width: 5),
                                      Text(
                                          '${programModuleLead?.data?.programsModuleFaculty?[index].totLearners ?? 0}',
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          softWrap: false,
                                          style: Styles.regular(size: 12)),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                          height: selectedIndexFaculty == index &&
                                  isFacultyLead == true
                              ? 130
                              : 60, //isModuleExpanded == true
                          //height: 130,
                          margin: EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                              color: Color(0xffF1FBFF),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10)),
                              border: Border.all(color: Color(0xffF1FBFF))),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                  apiCallRoot = 1;
                                  programCompletionFaculty?.programCompletion =
                                      0;
                                  attendancePercentage?.attendanceCompletion =
                                      0;
                                  if (selectedIndexFaculty != index) {
                                    _getProgramCompletion(
                                        programCompId: programModuleLead?.data
                                            ?.programsModuleFaculty?[index].id);
                                    _getAttendancePercentage(
                                        prgramCompletionId: programModuleLead
                                            ?.data
                                            ?.programsModuleFaculty?[index]
                                            .id);

                                    setState(() {
                                      selectedIndexFaculty = index;
                                      isFacultyLead = true;
                                    });
                                  } else {
                                    setState(() {
                                      selectedIndexFaculty = null;
                                      isFacultyLead = false;
                                    });
                                  }
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 15.0),
                                  child: Row(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            left: 16.0, top: 0.0, bottom: 0.0),
                                        child: Text(
                                          'module_completion',
                                          style: Styles.bold(),
                                        ).tr(),
                                      ),
                                      Spacer(),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(right: 10.0),
                                        child: selectedIndexFaculty == index &&
                                                isFacultyLead == true
                                            ? Icon(
                                                Icons.keyboard_arrow_up,
                                                size: 20,
                                              )
                                            : Icon(
                                                Icons.keyboard_arrow_down,
                                                size: 20,
                                              ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              selectedIndexFaculty == index &&
                                      isFacultyLead == true
                                  ? Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${tr('program_completion')} ${formatter.format(programCompletionFaculty?.programCompletion ?? 0.0 ?? 0)}%',
                                            style: Styles.regular(),
                                          ).tr(),
                                          SizedBox(height: 5),
                                          ClipRRect(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(30)),
                                            child: LinearProgressIndicator(
                                              minHeight: 6,
                                              value: (programCompletionFaculty
                                                          ?.programCompletion ??
                                                      0) /
                                                  100,
                                              backgroundColor: Colors.grey[300],
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                ColorConstants().gradientLeft(),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : SizedBox(),
                              selectedIndexFaculty == index &&
                                      isFacultyLead == true
                                  ? SizedBox(height: 10)
                                  : SizedBox(),
                              selectedIndexFaculty == index &&
                                      isFacultyLead == true
                                  ? Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16.0),
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              Text(
                                                '${tr('attendance_percentage')} ${formatter.format(attendancePercentage?.attendanceCompletion ?? 0.0 ?? 0)}%',
                                                style: Styles.regular(),
                                              ).tr()
                                            ],
                                          ),
                                          SizedBox(height: 5),
                                          ClipRRect(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(30)),
                                            child: LinearProgressIndicator(
                                              minHeight: 6,
                                              value: (attendancePercentage
                                                          ?.attendanceCompletion ??
                                                      0) /
                                                  100,
                                              backgroundColor: Colors.grey[300],
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                ColorConstants().gradientLeft(),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : SizedBox(),
                            ],
                          )
                          //
                          ),
                    ),

                    /*Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: isFacultyLead == true && selectedIndexFaculty == index ? 160 : 62,
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                            color: Color(0xffF1FBFF),
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            border: Border.all(color: Color(0xffF1FBFF))),
                        child: AnimatedContainer(
                          duration: Duration(milliseconds: 300),
                          decoration: BoxDecoration(
                            color: Color(0xffF1FBFF),
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            border: Border.all(color: Color(0xffF1FBFF)),
                          ),
                          child: ExpansionTile(
                              maintainState: false,
                              key: Key(index.toString()),
                              collapsedTextColor: ColorConstants.BLACK,
                              initiallyExpanded: false,
                              title: Text(
                                'module_completion11',
                                style: Styles.bold(),
                              ).tr(),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            '${tr('program_completion')}-${programCompletion?.programCompletion ?? 0.0 ?? 0}%',
                                            style: Styles.regular(),
                                          )
                                        ],
                                      ),
                                      SizedBox(height: 5),
                                      ClipRRect(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(30)),
                                        child: LinearProgressIndicator(
                                          minHeight: 6,
                                          value: (programCompletion
                                                      ?.programCompletion ??
                                                  0) /
                                              100,
                                          backgroundColor: Colors.grey[300],
                                          valueColor: AlwaysStoppedAnimation<
                                                  Color>(
                                              ColorConstants().gradientLeft()),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 10),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            '${tr('attendance_percentage')}-${attendancePercentage?.attendanceCompletion ?? 0.0}%',
                                            style: Styles.regular(),
                                          ).tr()
                                        ],
                                      ),
                                      SizedBox(height: 5),
                                      ClipRRect(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(30)),
                                        child: LinearProgressIndicator(
                                          minHeight: 6,
                                          value: (attendancePercentage
                                                      ?.attendanceCompletion ??
                                                  0) /
                                              100,
                                          backgroundColor: Colors.grey[300],
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            ColorConstants().gradientLeft(),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              onExpansionChanged: ((newState) {
                                attendancePercentage?.attendanceCompletion = 0;
                                if(selectedIndexFaculty != index){
                                  setState(() {
                                    isFacultyLead = false;
                                  });
                                }
                                selectedIndexFaculty = index;
                                if (isFacultyLead == false) {
                                  _getProgramCompletion(programCompId: programModuleLead?.data?.programsModuleFaculty?[index].id);
                                  _getAttendancePercentage(prgramCompletionId: programModuleLead?.data?.programsModuleFaculty?[index].id);

                                  setState(() {
                                    isFacultyLead = true;
                                  });
                                } else {
                                  setState(() {
                                    isFacultyLead = false;
                                  });
                                }
                              })),
                        ),
                      ),
                    ),*/
                  ]);
                },
                itemCount:
                    programModuleLead?.data?.programsModuleFaculty?.length ?? 0,
              )
            : SizedBox());
  }

  todayClasses() {
    bool? isInProgress;

    return Container(
      child: ListView.builder(
        shrinkWrap: true,
        physics: BouncingScrollPhysics(),
        itemCount: programModuleLead?.data?.ongoingLiveClass?.length,
        itemBuilder: (BuildContext context, int index) {
          // DateTime? currentIndiaTime;

          return Container(
            // height: MediaQuery.of(context).size.height * 0.27,
            margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            width: MediaQuery.of(context).size.width * 0.9,
            decoration: BoxDecoration(
                color: ColorConstants.ACCENT_COLOR,
                borderRadius: BorderRadius.circular(6)),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                        '${programModuleLead?.data?.ongoingLiveClass?[index].programName ?? ''}',
                        style: Styles.regular(size: 14)),
                  ),
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 1)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: ShaderMask(
                            blendMode: BlendMode.srcIn,
                            shaderCallback: (Rect bounds) {
                              return LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ]).createShader(bounds);
                            },
                            child: Text(
                              jsonDecode('${programModuleLead?.data?.ongoingLiveClass?[index].batch ?? ''}')[
                                      0]['batch_name']
                                  .toString(),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              softWrap: false,
                              style: Styles.semibold(
                                  size: 12, color: Color(0xff4F5AED)),
                            ),
                          ),
                        ),
                      ),
                      Spacer(),
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 1)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/live_icon.svg',
                                height: 20.0,
                                width: 20.0,
                                allowDrawingOutsideViewBox: true,
                              ),
                              SizedBox(
                                  width:
                                      4), // Added spacing between icon and text
                              Text(
                                '${programModuleLead?.data?.ongoingLiveClass?[index].classStatus ?? ''}',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.semibold(
                                    size: 12, color: Color(0xffEA575E)),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 9, top: 3),
                    child: Text(
                        '${programModuleLead?.data?.ongoingLiveClass?[index].title ?? ''}',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.bold(size: 16)),
                  ),
                  SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      children: [
                        SizedBox(
                          child: Row(
                            children: [
                              Icon(Icons.alarm, size: 20),
                              SizedBox(
                                  width:
                                      4), // Added spacing between icon and text
                              Text(
                                '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.ongoingLiveClass?[index].startDate ?? ''}'), '' + ' hh:mm aa')} To ${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.ongoingLiveClass?[index].endDate ?? ''}'), '' + ' hh:mm a')}',
                                style: Styles.regular(size: 12),
                                textDirection: ui.TextDirection.ltr,
                              ),
                            ],
                          ),
                        ),
                        Spacer(),
                        SizedBox(
                          child: Row(
                            children: [
                              Icon(Icons.calendar_month_outlined, size: 20),
                              SizedBox(
                                  width:
                                      4), // Added spacing between icon and text
                              Text(
                                '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.ongoingLiveClass?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ',
                                style: Styles.regular(size: 12),
                                textDirection: ui.TextDirection.ltr,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Divider(),
                  if (programModuleLead
                          ?.data?.ongoingLiveClass?[index].contentType
                          ?.toLowerCase() ==
                      'offlineclass') ...[
                    Center(
                      child: InkWell(
                        onTap: () {
                          try {
                            Navigator.push(
                                context,
                                NextPageRoute(AttendanceViewScreen(
                                    recordUrl: programModuleLead?.data
                                        ?.ongoingLiveClass?[index].recordUrl,
                                    classId: programModuleLead?.data
                                            ?.ongoingLiveClass?[index].id ??
                                        0,
                                    title: programModuleLead
                                        ?.data?.ongoingLiveClass?[index].title
                                        .toString(),
                                    batch:
                                        '${programModuleLead?.data?.ongoingLiveClass?[index].batch ?? ''}',
                                    startDate:
                                        '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.ongoingLiveClass?[index].startDate ?? ''}'), '' + ' hh:mm aa')} To ${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.ongoingLiveClass?[index].endDate ?? ''}'), '' + ' hh:mm a')}',
                                    classStatus:
                                        '${programModuleLead?.data?.ongoingLiveClass?[index].classStatus}',
                                    endDate:
                                        '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.ongoingLiveClass?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ')));
                          } catch (e, stackTrace) {
                            print('$stackTrace');
                          }
                        },
                        child: Center(
                          child: Container(
                            height: height(context) * 0.05,
                            width: width(context) * 0.6,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(colors: [
                                ColorConstants().gradientLeft(),
                                ColorConstants().gradientRight()
                              ]),

                              borderRadius: BorderRadius.circular(10),
                              // border: Border.all(color: Color(0xffF0F1FA), width: 2)
                            ),
                            margin: EdgeInsets.only(left: 9, top: 3),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                  child: Text('mark_attendance',
                                          style: Styles.textBold(
                                              size: 12,
                                              color:
                                                  ColorConstants.ACCENT_COLOR))
                                      .tr()),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                  if (programModuleLead
                              ?.data?.ongoingLiveClass?[index].classStatus
                              ?.toLowerCase() ==
                          'live' ||
                      isInProgress != null && isInProgress)
                    InkWell(
                      onTap: () {
                        if (programModuleLead
                                ?.data?.ongoingLiveClass?[index].contentType!
                                .toLowerCase() ==
                            "offlineclass") return;

                        if (programModuleLead?.data?.ongoingLiveClass?[index]
                                    .contentType!
                                    .toLowerCase() ==
                                "zoomclass" ||
                            programModuleLead?.data?.ongoingLiveClass?[index]
                                    .contentType!
                                    .toLowerCase() ==
                                'teamsclass' ||
                            programModuleLead?.data?.ongoingLiveClass?[index]
                                    .contentType!
                                    .toLowerCase() ==
                                'otherclass') {
                          setState(() {
                            currentZoomUrl = programModuleLead
                                ?.data?.ongoingLiveClass?[index].recordUrl;
                          });

                          if (currentZoomUrl != null) {
                            BlocProvider.of<HomeBloc>(context)
                                .add(ZoomOpenUrlEvent(
                              contentId: programModuleLead
                                  ?.data?.ongoingLiveClass?[index].id,
                            ));
                            launchUrl(Uri.parse('$currentZoomUrl'),
                                mode: LaunchMode.externalApplication);
                          } else {
                            BlocProvider.of<HomeBloc>(context).add(
                                ZoomOpenUrlEvent(
                                    contentId: programModuleLead
                                        ?.data?.ongoingLiveClass?[index].id));
                          }
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                            content: Text("coming_soon").tr(),
                          ));
                        }
                      },
                      child: Center(
                        child: Container(
                          height: height(context) * 0.05,
                          width: width(context) * 0.6,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(colors: [
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight()
                            ]),

                            borderRadius: BorderRadius.circular(10),
                            // border: Border.all(color: Color(0xffF0F1FA), width: 2)
                          ),
                          margin: EdgeInsets.only(left: 9, top: 3),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(
                                child: Text(
                                        programModuleLead
                                                        ?.data
                                                        ?.ongoingLiveClass?[
                                                            index]
                                                        .contentType!
                                                        .toLowerCase() ==
                                                    "liveclass" ||
                                                programModuleLead
                                                        ?.data
                                                        ?.ongoingLiveClass?[
                                                            index]
                                                        .contentType!
                                                        .toLowerCase() ==
                                                    "zoomclass" ||
                                                programModuleLead
                                                        ?.data
                                                        ?.ongoingLiveClass?[
                                                            index]
                                                        .contentType!
                                                        .toLowerCase() ==
                                                    'teamsclass' ||
                                                programModuleLead
                                                        ?.data
                                                        ?.ongoingLiveClass?[
                                                            index]
                                                        .contentType!
                                                        .toLowerCase() ==
                                                    'otherclass'
                                            ? "join_class"
                                            : 'in_progress',
                                        style: Styles.textBold(
                                            size: 12,
                                            color: ColorConstants.ACCENT_COLOR))
                                    .tr()),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  concludedClassess() {
    return Container(
      child: ListView.builder(
        shrinkWrap: true,
        physics: BouncingScrollPhysics(),
        itemCount: programModuleLead?.data?.concludedLiveClass?.length ?? 0,
        itemBuilder: (BuildContext context, int index) {
          return Container(
            // height: MediaQuery.of(context).size.height * 0.27,
            margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            width: MediaQuery.of(context).size.width * 0.9,
            decoration: BoxDecoration(
                color: ColorConstants.ACCENT_COLOR,
                borderRadius: BorderRadius.circular(6)),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                        '${programModuleLead?.data?.concludedLiveClass?[index].programName ?? ''}',
                        style: Styles.regular(size: 14)),
                  ),
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: Color(0xffF0F1FA),
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 2)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: SizedBox(
                              height: 20,
                              // width: 100,
                              child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  shrinkWrap: true,
                                  itemCount: jsonDecode(
                                          '${programModuleLead?.data?.concludedLiveClass?[index].batch}')
                                      .length,
                                  itemBuilder: (context, index) => Padding(
                                        padding:
                                            const EdgeInsets.only(right: 4.0),
                                        child: ShaderMask(
                                          blendMode: BlendMode.srcIn,
                                          shaderCallback: (Rect bounds) {
                                            return LinearGradient(
                                                begin: Alignment.centerLeft,
                                                end: Alignment.centerRight,
                                                colors: <Color>[
                                                  ColorConstants()
                                                      .gradientLeft(),
                                                  ColorConstants()
                                                      .gradientRight()
                                                ]).createShader(bounds);
                                          },
                                          child: Text(
                                              jsonDecode('${programModuleLead?.data?.concludedLiveClass?[index].batch}')[
                                                      index]['batch_name']
                                                  .toString(),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              softWrap: false,
                                              style: Styles.semibold(
                                                  size: 12,
                                                  color: Color(0xff4F5AED))),
                                        ),
                                      )),
                            )
                            //  Text(
                            //   // jsonDecode('${programModuleLead?.data?.concludedLiveClass?[index].batch}')[
                            //   //         0]['batch_name']
                            //   //     .toString(),

                            //        jsonDecode('${programModuleLead?.data?.concludedLiveClass?[index].batch}').length
                            //       .toString(),
                            //   maxLines: 2,
                            //   overflow: TextOverflow.ellipsis,
                            //   softWrap: false,
                            //   style: Styles.semibold(
                            //       size: 12, color: Color(0xff4F5AED)),
                            // ),
                            ),
                      ),
                      Spacer(),
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 1)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/live_icon.svg',
                                height: 20.0,
                                width: 20.0,
                                allowDrawingOutsideViewBox: true,
                              ),
                              SizedBox(width: 4),
                              Text(
                                '${programModuleLead?.data?.concludedLiveClass?[index].classStatus ?? ''}',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.semibold(
                                    size: 12, color: Color(0xffEA575E)),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 9, top: 3),
                    child: Text(
                        '${programModuleLead?.data?.concludedLiveClass?[index].title ?? ''}',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.bold(size: 16)),
                  ),
                  SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      children: [
                        SizedBox(
                          child: Row(
                            children: [
                              Icon(Icons.alarm, size: 20),
                              SizedBox(width: 4), //
                              Text(
                                '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.concludedLiveClass?[index].startDate ?? ''}'), '' + ' hh:mm aa')} To ${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.concludedLiveClass?[index].endDate ?? ''}'), '' + ' hh:mm a')}',
                                style: Styles.regular(size: 12),
                                textDirection: ui.TextDirection.ltr,
                              ),
                            ],
                          ),
                        ),
                        Spacer(),
                        SizedBox(
                          child: Row(
                            children: [
                              Icon(Icons.calendar_month_outlined, size: 18),
                              SizedBox(width: 4),
                              Text(
                                '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.concludedLiveClass?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ',
                                style: Styles.regular(size: 12),
                                textDirection: ui.TextDirection.ltr,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  if (programModuleLead
                          ?.data?.concludedLiveClass?[index].contentType
                          ?.toLowerCase() ==
                      'offlineclass') ...[Divider()],
                  // SizedBox(height: 10),
                  if (programModuleLead
                          ?.data?.concludedLiveClass?[index].contentType
                          ?.toLowerCase() ==
                      'offlineclass') ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (programModuleLead
                                ?.data?.concludedLiveClass?[index].recordUrl !=
                            null)
                          InkWell(
                            onTap: () {
                              setState(() {
                                selectedVewAttendance = false;
                                selectedVewRecording = true;
                              });
                              print(
                                  'record url is ${programModuleLead?.data?.concludedLiveClass?[index].recordUrl}');

                              Navigator.push(
                                  context,
                                  NextPageRoute(VideoPlayerScreen(
                                    recordUrl:
                                        '${programModuleLead?.data?.concludedLiveClass?[index].recordUrl}',
                                  )));
                            },
                            child: Container(
                              height: height(context) * 0.05,
                              width: width(context) * 0.4,
                              decoration: BoxDecoration(
                                  gradient: selectedVewRecording == true
                                      ? LinearGradient(colors: [
                                          ColorConstants().gradientLeft(),
                                          ColorConstants().gradientRight(),
                                        ])
                                      : LinearGradient(colors: [
                                          ColorConstants.WHITE,
                                          ColorConstants.WHITE,
                                        ]),
                                  // color: selectedVewRecording == true
                                  //     ? Color(0xff3CA4D2)
                                  //     : ColorConstants.WHITE,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      color: selectedVewRecording == true
                                          ? ColorConstants().gradientLeft()
                                          : Color(0xffF0F1FA),
                                      width: 2)),
                              margin: EdgeInsets.only(left: 9, top: 3),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Center(
                                    child: Text('view_recording',
                                            style: Styles.textBold(
                                                size: 12,
                                                color:
                                                    selectedVewRecording == true
                                                        ? ColorConstants.WHITE
                                                        : ColorConstants()
                                                            .gradientLeft()))
                                        .tr()),
                              ),
                            ),
                          ),
                        Spacer(),
                        InkWell(
                          onTap: () {
                            setState(() {
                              selectedVewAttendance = true;
                              selectedVewRecording = false;
                            });

                            try {
                              Navigator.push(
                                  context,
                                  NextPageRoute(AttendanceViewScreen(
                                      recordUrl: programModuleLead
                                          ?.data
                                          ?.concludedLiveClass?[index]
                                          .recordUrl,
                                      classId: programModuleLead?.data
                                              ?.concludedLiveClass?[index].id ??
                                          0,
                                      title: programModuleLead?.data
                                          ?.concludedLiveClass?[index].title
                                          .toString(),
                                      batch:
                                          '${programModuleLead?.data?.concludedLiveClass?[index].batch ?? ''}',
                                      startDate:
                                          '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.concludedLiveClass?[index].startDate ?? ''}'), '' + ' hh:mm aa')} To ${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.concludedLiveClass?[index].endDate ?? ''}'), '' + ' hh:mm a')}',
                                      classStatus:
                                          '${programModuleLead?.data?.concludedLiveClass?[index].classStatus}',
                                      endDate:
                                          '${Utility.convertDateFromMillis(int.parse('${programModuleLead?.data?.concludedLiveClass?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ')));
                            } catch (e, stackTrace) {
                              print('$stackTrace');
                            }
                          },
                          child: Container(
                            height: height(context) * 0.05,
                            width: width(context) * 0.4,
                            decoration: BoxDecoration(
                                gradient: selectedVewAttendance == true
                                    ? LinearGradient(colors: [
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight(),
                                      ])
                                    : LinearGradient(colors: [
                                        ColorConstants.WHITE,
                                        ColorConstants.WHITE,
                                      ]),
                                // color: selectedVewAttendance == true
                                //     ? Color(0xff3CA4D2)
                                //     : ColorConstants.WHITE,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                    color: selectedVewAttendance == true
                                        ? ColorConstants().gradientLeft()
                                        : Color(0xffF0F1FA),
                                    width: 2)),
                            margin: EdgeInsets.only(left: 9, top: 3),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                  child: Text('view_attendance',
                                          style: Styles.textBold(
                                              size: 12,
                                              color:
                                                  selectedVewAttendance == true
                                                      ? ColorConstants.WHITE
                                                      : ColorConstants()
                                                          .gradientLeft()))
                                      .tr()),
                            ),
                          ),
                        ),
                      ],
                    )
                  ]
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  summary() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.56,
      margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
          color: ColorConstants.ACCENT_COLOR,
          borderRadius: BorderRadius.circular(6)),
      child: Column(
        children: [
          Container(
              height: MediaQuery.of(context).size.height * 0.16,
              margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                  color: ColorConstants.GREY,
                  borderRadius: BorderRadius.circular(6)),
              child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 100,
                              width: 10,
                              margin: EdgeInsets.only(left: 9, top: 3),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                gradient: LinearGradient(colors: [
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ]),
                                // color: Color(0xff3CA4D2),
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(left: 9, top: 3),
                                  child: Text('total_classes',
                                          style: Styles.regular(size: 18))
                                      .tr(),
                                ),
                                // SizedBox(height: 20),
                                SizedBox(
                                  width: 280,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      InkWell(
                                          onTap: () {
                                            if (programModuleLead
                                                    ?.data?.totalLiveClass !=
                                                0) {
                                              Navigator.push(
                                                  context,
                                                  NextPageRoute(
                                                      FacultyClassPage(),
                                                      isMaintainState: true));
                                            } else {
                                              var snackBar = SnackBar(
                                                  content:
                                                      Text('classes_not_found')
                                                          .tr());
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(snackBar);
                                            }
                                          },
                                          child: ShaderMask(
                                            blendMode: BlendMode.srcIn,
                                            shaderCallback: (Rect bounds) {
                                              return LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: <Color>[
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight()
                                                  ]).createShader(bounds);
                                            },
                                            child: Icon(Icons
                                                .arrow_forward_ios_outlined),
                                          )),
                                    ],
                                  ),
                                ),
                                Container(
                                    margin: EdgeInsets.only(left: 9, top: 3),
                                    child: Text(
                                        '${programModuleLead?.data?.totalLiveClass ?? '0'}',
                                        style: Styles.bold(size: 36)))
                              ],
                            ),
                          ],
                        ),
                      ]))),
          Container(
              height: MediaQuery.of(context).size.height * 0.16,
              margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                  color: ColorConstants.GREY,
                  borderRadius: BorderRadius.circular(6)),
              child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 110,
                              width: 10,
                              margin: EdgeInsets.only(left: 9, top: 3),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                gradient: LinearGradient(colors: [
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ]),
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(left: 9, top: 3),
                                  child: Text('total_assessment',
                                          style: Styles.regular(size: 18))
                                      .tr(),
                                ),
                                // SizedBox(height: 10),
                                SizedBox(
                                  width: 280,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      InkWell(
                                          onTap: () {
                                            bool hasCourseId = false;
                                            if (programModuleLead
                                                    ?.data?.facultyCourses !=
                                                null) {
                                              hasCourseId = programModuleLead!
                                                  .data!.facultyCourses!
                                                  .any((element) =>
                                                      element.courseId != null);
                                            }

                                            if (programModuleLead
                                                    ?.data?.totalLiveClass !=
                                                0) {
                                              Navigator.push(
                                                  context,
                                                  NextPageRoute(
                                                      FacultyAssessmentPage(
                                                          courseId: hasCourseId
                                                              .toString()),
                                                      isMaintainState: true));
                                            } else {
                                              var snackBar = SnackBar(
                                                  content: Text(
                                                          'assessment_not_found')
                                                      .tr());
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(snackBar);
                                            }
                                          },
                                          child: ShaderMask(
                                            blendMode: BlendMode.srcIn,
                                            shaderCallback: (Rect bounds) {
                                              return LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: <Color>[
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight()
                                                  ]).createShader(bounds);
                                            },
                                            child: Icon(Icons
                                                .arrow_forward_ios_outlined),
                                          )),
                                    ],
                                  ),
                                ),
                                Container(
                                    margin: EdgeInsets.only(left: 9, top: 3),
                                    child: Text(
                                        '${programModuleLead?.data?.totalAssessment ?? '0'}',
                                        style: Styles.bold(size: 36)))
                              ],
                            ),
                          ],
                        ),
                      ]))),
          Container(
              height: MediaQuery.of(context).size.height * 0.16,
              margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                  color: ColorConstants.GREY,
                  borderRadius: BorderRadius.circular(6)),
              child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 100,
                              width: 10,
                              margin: EdgeInsets.only(left: 9, top: 3),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                gradient: LinearGradient(colors: [
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ]),
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(left: 9, top: 3),
                                  child: Text('total_assignment',
                                          style: Styles.regular(size: 18))
                                      .tr(),
                                ),
                                SizedBox(
                                  width: 280,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      InkWell(
                                          onTap: () {
                                            if (programModuleLead
                                                    ?.data?.totalLiveClass !=
                                                0) {
                                              Navigator.push(
                                                  context,
                                                  NextPageRoute(
                                                      FacultyAssignmentPage(),
                                                      isMaintainState: true));
                                            } else {
                                              var snackBar = SnackBar(
                                                  content: Text(
                                                          'assignment_not_found')
                                                      .tr());
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(snackBar);
                                            }
                                          },
                                          child: ShaderMask(
                                            blendMode: BlendMode.srcIn,
                                            shaderCallback: (Rect bounds) {
                                              return LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: <Color>[
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight()
                                                  ]).createShader(bounds);
                                            },
                                            child: Icon(Icons
                                                .arrow_forward_ios_outlined),
                                          )),
                                    ],
                                  ),
                                ),
                                Container(
                                    margin: EdgeInsets.only(left: 9, top: 3),
                                    child: Text(
                                        '${programModuleLead?.data?.totalAssignment ?? '0'}',
                                        style: Styles.bold(size: 36)))
                              ],
                            ),
                          ],
                        ),
                      ]))),
        ],
      ),
    );
  }

  void _handleFacultyBatchDetailsState(FacultyBatchDetailsState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchDetailsState.");
            isFacultyDetails = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................FacultyBatchDetailsState ");
            programModuleLead = state.response;

            isFacultyDetails = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchDetailsState");
            isFacultyDetails = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isFacultyDetails = false;
      });
    }
  }

  void _handleFacultyBatchClassState(FacultyBatchClassState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchClassState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................FacultyBatchClassState");
            facultyClass = state.response;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchClassState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleProgramCompletionState(ProgramCompletionState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................ProgramCompletionState");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................ProgramCompletionState");

            if (apiCallRoot == 1) {
              programCompletionFaculty = state.response;
            } else {
              programCompletion = state.response;
            }
            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................ProgramCompletionState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleAttendancePercentageState(AttendancePercentageState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................AttendancePercentageState");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................AttendancePercentageState");
            attendancePercentage = state.response;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................AttendancePercentageState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _getFacultyBatchDetails({int? courseId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(FacultyBatchDetailsEvent(courseId: courseId));
  }

  void _getFacultyBatchClass() {
    BlocProvider.of<HomeBloc>(context).add(FacultyBatchClassEvent());
  }

  void _getProgramCompletion({dynamic programCompId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(ProgramCompletionEvent(programCompletionId: programCompId));
  }

  void _getAttendancePercentage({int? prgramCompletionId}) {
    BlocProvider.of<HomeBloc>(context).add(
        AttendancePercentageEvent(programCompletionId: prgramCompletionId));
  }

  void getAttendanceView({int? classId, int? batchId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(MarkAttendanceEvent(classId: classId, batchId: batchId));
  }

  void _handleMarkAttendanceState(MarkAttendanceState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchDetailsState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v(
                "Success....................FacultyBatchDetailsState ${state.response?.data?.liveClassUser?.length}");

            liveClassUsers = state.response?.data?.liveClassUser;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchDetailsState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  // Widget _customAppBar() {
  //   return RoundedAppBar(
  //       appBarHeight: height(context) * 0.1,
  //       child: Padding(
  //           padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 12),
  //           child: Column(
  //               mainAxisAlignment: MainAxisAlignment.start,
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Row(
  //                   crossAxisAlignment: CrossAxisAlignment.center,
  //                   mainAxisAlignment: MainAxisAlignment.start,
  //                   children: [
  //                     InkWell(
  //                       onTap: () {
  //                         Navigator.push(
  //                                 context,
  //                                 MaterialPageRoute(
  //                                     builder: (context) => NewPortfolioPage()))
  //                             .then((value) {
  //                           if (value != null)
  //                             menuProvider?.updateCurrentIndex(value);
  //                         });
  //                       },
  //                       child: ClipRRect(
  //                         borderRadius: BorderRadius.circular(200),
  //                         child: SizedBox(
  //                             width: 50,
  //                             child: CachedNetworkImage(
  //                               imageUrl:
  //                                   '${Preference.getString(Preference.PROFILE_IMAGE)}',
  //                               height: 50,
  //                               width: 50,
  //                               fit: BoxFit.cover,
  //                               placeholder: (context, url) => SvgPicture.asset(
  //                                 'assets/images/default_user.svg',
  //                                 width: 50,
  //                               ),
  //                               errorWidget: (context, url, error) =>
  //                                   SvgPicture.asset(
  //                                 'assets/images/default_user.svg',
  //                                 width: 50,
  //                               ),
  //                             )),
  //                       ),
  //                     ),
  //                     Spacer(),
  //                     InkWell(
  //                       onTap: () {
  //                         _scaffoldKey.currentState?.openEndDrawer();
  //                       },
  //                       child: SvgPicture.asset(
  //                           'assets/images/hamburger_menu.svg'),
  //                     )
  //                   ],
  //                 ),
  //               ])));
  // }

  sectionLoader() {
    return Shimmer.fromColors(
      baseColor: Color(0xffe6e4e6),
      highlightColor: Color(0xffeaf0f3),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.07,
        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(6)),
      ),
    );
  }
}

class BlankPage extends StatelessWidget {
  const BlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(left: 0, right: 0, top: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 250,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class EmptyPage extends StatelessWidget {
  const EmptyPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/something_went_wrong.png',
                      height: 180,
                      width: 180,
                    ),
                    Text(
                      'something_went_wrong_v1',
                      style: Styles.bold(size: 18),
                    ).tr(),
                    Padding(
                      padding: const EdgeInsets.only(top: 5.0),
                      child: Text(
                        'please_try_again',
                        style: Styles.regular(size: 12),
                      ).tr(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_details_resp.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/pages/learn_console/course_curriculum.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';

class LeaderProgramDetailsPage extends StatefulWidget {
  final String? image;
  final String? programName;
  final String? description;
  final String? startDate;
  final String? endDate;
  final String? facultyNames;
  final String? totLearners;
  final int? programId;
  final String? status;
  final String? category;

  const LeaderProgramDetailsPage(
      {super.key,
      required this.image,
      this.programName,
      this.description,
      this.startDate,
      this.endDate,
      this.facultyNames,
      this.totLearners,
      this.programId,
      required this.status,
      required this.category});

  @override
  State<LeaderProgramDetailsPage> createState() =>
      _LeaderProgramDetailsPageState();
}

class _LeaderProgramDetailsPageState extends State<LeaderProgramDetailsPage> {
  // var _scaffoldKey = new GlobalKey<ScaffoldState>();
  MenuListProvider? menuProvider;
  FacultyBatchDetailsResponse? programModuleLead;
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    // var ui;
    return Scaffold(
        appBar: AppBar(
            elevation: 0,
            backgroundColor: ColorConstants.WHITE,
            leading: BackButton(color: ColorConstants.BLACK),
            title:
                Text('programme_details', style: Styles.bold(size: 16)).tr()),
        backgroundColor: ColorConstants.GREY,
        body: BlocManager(
            initState: (context) {},
            child: Consumer<MenuListProvider>(
              builder: (context, mp, child) => BlocListener<HomeBloc,
                      HomeState>(
                  listener: (context, state) async {
                    if (state is FacultyBatchDetailsState) {
                      _handleFacultyBatchDetailsState(state);
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.only(top: 12.0),
                    color: ColorConstants.WHITE,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          /*Container(
                              color: ColorConstants.WHITE,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 14, vertical: 8),
                                child: Row(children: [
                                  Text(
                                    'programme_details',
                                    style: Styles.bold(size: 16),
                                  ).tr(),
                                ]),
                              ),
                            ),*/
                          /*Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12.0),
                              child: Divider(thickness: 2),
                            ),*/
                          SizedBox(height: 0),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: SizedBox(
                              width: double.infinity,
                              height: 200,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: CachedNetworkImage(
                                  imageUrl: '${widget.image ?? ''}',
                                  width: 100,
                                  height: 120,
                                  errorWidget: (context, url, error) =>
                                      SvgPicture.asset(
                                    'assets/images/gscore_postnow_bg.svg',
                                  ),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                          Container(
                              // margin: EdgeInsets.only(left: 9, top: 3),
                              padding:
                                  const EdgeInsets.only(left: 14.0, top: 16),
                              child: Text('${widget.programName ?? ''}',
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: false,
                                  style: Styles.semibold(size: 16))),
                          // SizedBox(width: 80),
                          Container(
                              margin: EdgeInsets.only(left: 0, top: 3),
                              child: Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: Utility()
                                        .isHtml('${widget.description ?? ''}')
                                    ? Html(
                                        data: '${widget.description ?? ''}',
                                      )
                                    : Padding(
                                        padding:
                                            const EdgeInsets.only(left: 9.0),
                                        child:
                                            Text('${widget.description ?? ''}',
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                softWrap: false,
                                                style: Styles.regular(
                                                  size: 12,
                                                )),
                                      ),
                              )),
                          SizedBox(height: 5),
                          Container(
                              width: width(context) * 0.6,
                              margin: EdgeInsets.only(left: 14, top: 3),
                              child: Row(
                                children: [
                                  Icon(Icons.calendar_month_outlined, size: 20),
                                  SizedBox(width: 10),
                                  SizedBox(
                                    child: Text(
                                      overflow: TextOverflow.ellipsis,
                                      softWrap: false,
                                      '${Utility.convertDateFromMillis(int.parse('${widget.startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} To  ${Utility.convertDateFromMillis(int.parse('${widget.endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                                      style: Styles.semibold(
                                          size: 12, color: Color(0xff0E1638)),
                                      // textDirection: ui.TextDirection.ltr,
                                    ),
                                  ),
                                ],
                              )),
                          Container(
                            width: width(context),
                            margin: EdgeInsets.only(left: 14, top: 3, right: 9),
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/images/person.svg',
                                  height: 20.0,
                                  width: 20.0,
                                  allowDrawingOutsideViewBox: true,
                                ),
                                SizedBox(width: 10),
                                Text('${widget.facultyNames ?? ''}',
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    softWrap: false,
                                    style: Styles.regular(size: 12)),
                                Spacer(),
                                Row(
                                  children: [
                                    SvgPicture.asset(
                                      'assets/images/local_library.svg',
                                      height: 20.0,
                                      width: 20.0,
                                      allowDrawingOutsideViewBox: true,
                                    ),
                                    SizedBox(width: 10),
                                    Text(
                                        '${widget.totLearners != 'null' ? widget.totLearners ?? '' : '0'}',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        softWrap: false,
                                        style: Styles.regular(size: 12)),
                                  ],
                                )
                              ],
                            ),
                          ),
                          SizedBox(height: 5),
                          Container(
                              margin:
                                  EdgeInsets.only(left: 14, top: 3, right: 9),
                              child: Row(
                                children: [
                                  Text('Status - ',
                                      style: Styles.regular(size: 12)),
                                  Text('${widget.status}',
                                      style: Styles.bold(
                                          size: 12,
                                          color: ColorConstants.GREEN)),
                                ],
                              )),
                          SizedBox(height: 5),
                          Container(
                              margin:
                                  EdgeInsets.only(left: 14, top: 3, right: 9),
                              child: Row(
                                children: [
                                  Text('Category - ',
                                      style: Styles.regular(size: 12)),
                                  Text('Popular Courses',
                                      style: Styles.regular(
                                        size: 12,
                                      )),
                                ],
                              )),
                          SizedBox(height: 30),

                          // Padding(
                          //   padding:
                          //       const EdgeInsets.symmetric(vertical: 8.0),
                          //   child: Center(
                          //     child: Container(
                          //       height: height(context) * 0.05,
                          //       width: width(context) * 0.4,
                          //       decoration: BoxDecoration(
                          //           color: Color(0xff3CA4D2),
                          //           borderRadius:
                          //               BorderRadius.circular(10),
                          //           border: Border.all(
                          //               color: Color(0xffF0F1FA),
                          //               width: 2)),
                          //       margin: EdgeInsets.only(left: 9, top: 3),
                          //       child: Padding(
                          //         padding: const EdgeInsets.all(8.0),
                          //         child: Center(
                          //             child: Text('View Leaderboard',
                          //                 style: Styles.textBold(
                          //                     size: 12,
                          //                     color:
                          //                         ColorConstants.WHITE))),
                          //       ),
                          //     ),
                          //   ),
                          // ),

                          Container(
                              margin: const EdgeInsets.all(10),
                              child: ChangeNotifierProvider<
                                      TrainingDetailProvider>(
                                  create: (context) => TrainingDetailProvider(
                                      TrainingService(ApiService()),
                                      MProgram(id: widget.programId ?? 0)),
                                  child: CourseCurriculum(
                                      programId: widget.programId ?? 0)))
                        ],
                      ),
                    ),
                  )),
            )));
  }

  // Widget _customAppBar() {
  //   return RoundedAppBar(
  //       appBarHeight: height(context) * 0.1,
  //       child: Padding(
  //           padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 12),
  //           child: Column(
  //               mainAxisAlignment: MainAxisAlignment.start,
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Row(
  //                   crossAxisAlignment: CrossAxisAlignment.center,
  //                   mainAxisAlignment: MainAxisAlignment.start,
  //                   children: [
  //                     InkWell(
  //                       onTap: () {
  //                         Navigator.push(
  //                                 context,
  //                                 MaterialPageRoute(
  //                                     builder: (context) => NewPortfolioPage()))
  //                             .then((value) {
  //                           if (value != null)
  //                             menuProvider?.updateCurrentIndex(value);
  //                         });
  //                       },
  //                       child: ClipRRect(
  //                         borderRadius: BorderRadius.circular(200),
  //                         child: SizedBox(
  //                             width: 50,
  //                             child: CachedNetworkImage(
  //                               imageUrl:
  //                                   '${Preference.getString(Preference.PROFILE_IMAGE)}',
  //                               height: 50,
  //                               width: 50,
  //                               fit: BoxFit.cover,
  //                               placeholder: (context, url) => SvgPicture.asset(
  //                                 'assets/images/default_user.svg',
  //                                 width: 50,
  //                               ),
  //                               errorWidget: (context, url, error) =>
  //                                   SvgPicture.asset(
  //                                 'assets/images/default_user.svg',
  //                                 width: 50,
  //                               ),
  //                             )),
  //                       ),
  //                     ),
  //                     Spacer(),
  //                     InkWell(
  //                       onTap: () {
  //                         _scaffoldKey.currentState?.openEndDrawer();
  //                       },
  //                       child: SvgPicture.asset(
  //                           'assets/images/hamburger_menu.svg'),
  //                     )
  //                   ],
  //                 ),
  //               ])));
  // }

  void _handleFacultyBatchDetailsState(FacultyBatchDetailsState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchDetailsState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................FacultyBatchDetailsState");
            programModuleLead = state.response;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchDetailsState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }
}

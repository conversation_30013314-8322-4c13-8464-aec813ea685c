import 'package:cached_network_image/cached_network_image.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/Attendance_percentage_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/module_leader_program_list_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/program_completion_resp.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/learn_console/faculty_console_program_details.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/extensions/widget_extensions.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;

class ModuleLeaderProgramList extends StatefulWidget {
  final ProgramCompletionResponse? programCompletion;
  final AttendancePercentageResponse? attendancePercentage;
  // final FacultyBatchDetailsResponse? programModuleLead;
  final Function onExpension;
  final int index;
  const ModuleLeaderProgramList({
    super.key,
    this.programCompletion,
    this.attendancePercentage,
    required this.onExpension,
    required this.index,
    // this.programModuleLead
  });

  @override
  State<ModuleLeaderProgramList> createState() =>
      _ModuleLeaderProgramListState();
}

class _ModuleLeaderProgramListState extends State<ModuleLeaderProgramList> {
  ModuleLeaderProgramListResponse? moduleLeaderProgramList;
  ProgramCompletionResponse? programCompletion;
  bool isModuleExpanded = false;
  bool isLoading = false;
  int? selectedProgram;
  var formatter = NumberFormat("#0.00");
  int? selectedIndex;

  @override
  void initState() {
    getModuleLeaderProgramList();
    super.initState();
  }

  // void _getFacultyBatchDetails({int? courseId}) {
  //   BlocProvider.of<HomeBloc>(context)
  //       .add(FacultyBatchDetailsEvent(courseId: courseId));
  // }

  void _getProgramCompletion({dynamic programCompId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(ProgramCompletionEvent(programCompletionId: programCompId));
  }

  // void _getAttendancePercentage({int? prgramCompletionId}) {
  //   BlocProvider.of<HomeBloc>(context)
  //       .add(ProgramCompletionEvent(programCompletionId: prgramCompletionId));
  // }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
                listener: (context, state) async {
                  if (state is ModuleLeaderProgramListState) {
                    _handleModuleLeaderProgramList(state);
                  }
                  if (state is ProgramCompletionState) {
                    _handleProgramCompletionState(state);
                  }
                },
                child: Scaffold(
                  appBar: AppBar(
                    elevation: 0,
                    backgroundColor: ColorConstants.WHITE,
                    leading: BackButton(color: ColorConstants.BLACK),
                    title: Text('module_leader_console',
                            style: Styles.bold(size: 16))
                        .tr(),
                  ),
                  body: ScreenWithLoader(
                    isLoading: isLoading,
                    body: Container(
                        height: height(context),
                        width: width(context),
                        child: moduleLeaderProgramList != null
                            ? SingleChildScrollView(
                                child: Column(
                                  children: [
                                    selectedProgram != null
                                        ? GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                moduleLeaderProgramList = null;
                                                selectedProgram = null;
                                              });
                                              getModuleLeaderProgramList();
                                            },
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                Icon(
                                                  Icons.refresh,
                                                  color: Colors.lightBlue,
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.all(5.0),
                                                  child: Text(
                                                    'reset_button',
                                                    style: Styles.bold(
                                                        size: 16,
                                                        color:
                                                            Colors.lightBlue),
                                                  ).tr(),
                                                ),
                                              ],
                                            ),
                                          )
                                        : SizedBox(),
                                    Container(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.05,
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 8),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 4, vertical: 8),
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        shadows: const [],
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton2<int>(
                                          isExpanded: true,
                                          hint: Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  'select_course',
                                                  style:
                                                      Styles.regular(size: 14),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ).tr(),
                                              ),
                                            ],
                                          ),
                                          items: moduleLeaderProgramList?.data
                                                          ?.facultyCourses ==
                                                      null &&
                                                  moduleLeaderProgramList
                                                          ?.data
                                                          ?.facultyCourses
                                                          ?.isEmpty ==
                                                      true
                                              ? []
                                              : moduleLeaderProgramList
                                                      ?.data?.facultyCourses
                                                      ?.map((value) {
                                                    return DropdownMenuItem<
                                                        int>(
                                                      value: value.courseId,
                                                      child: SizedBox(
                                                        width: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            0.7,
                                                        child: Text(
                                                            value.courseName!,
                                                            maxLines: 2,
                                                            style: const TextStyle(
                                                                color: Colors
                                                                    .black,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500)),
                                                      ),
                                                    );
                                                  }).toList() ??
                                                  [],
                                          value: selectedProgram,
                                          onChanged: (int? value) {
                                            setState(() {
                                              if (value != null) {
                                                getModuleLeaderProgramList(
                                                    courseId: value);
                                                selectedProgram = value;
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                    ),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      itemCount: moduleLeaderProgramList
                                              ?.data?.programs?.length ??
                                          0,
                                      physics: BouncingScrollPhysics(),
                                      scrollDirection: Axis.vertical,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return Stack(children: [
                                          InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                  context,
                                                  NextPageRoute(
                                                      FacultyProgramDetailsPage(
                                                        image:
                                                            '${moduleLeaderProgramList?.data?.programs?[index].image}',
                                                        programName:
                                                            moduleLeaderProgramList
                                                                ?.data
                                                                ?.programs?[
                                                                    index]
                                                                .name,
                                                        description:
                                                            moduleLeaderProgramList
                                                                ?.data
                                                                ?.programs?[
                                                                    index]
                                                                .description,
                                                        startDate:
                                                            '${moduleLeaderProgramList?.data?.programs?[index].startDate}',
                                                        endDate:
                                                            '${moduleLeaderProgramList?.data?.programs?[index].endDate}',
                                                        facultyNames:
                                                            '${moduleLeaderProgramList?.data?.programs?[index].facultyNames}',
                                                        totLearners:
                                                            '${moduleLeaderProgramList?.data?.programs?[index].totLearners}',
                                                        programId:
                                                            moduleLeaderProgramList
                                                                ?.data
                                                                ?.programs?[
                                                                    index]
                                                                .id,
                                                        status:
                                                            '${moduleLeaderProgramList?.data?.programs?[index].status}',
                                                        category:
                                                            '${moduleLeaderProgramList?.data?.programs?[index].createdBy}',
                                                      ),
                                                      isMaintainState: true));
                                            },
                                            child: Container(
                                              height: MediaQuery.of(context)
                                                      .size
                                                      .height *
                                                  0.3,
                                              margin: EdgeInsets.symmetric(
                                                  horizontal: 10, vertical: 10),
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.95,
                                              decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            8.0),
                                                    child: Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        SizedBox(
                                                          width: 85,
                                                          height: 90,
                                                          child: ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8),
                                                            child:
                                                                CachedNetworkImage(
                                                              imageUrl:
                                                                  '${moduleLeaderProgramList?.data?.programs?[index].image ?? ''}',
                                                              width: 100,
                                                              height: 120,
                                                              errorWidget: (context,
                                                                      url,
                                                                      error) =>
                                                                  SvgPicture
                                                                      .asset(
                                                                'assets/images/gscore_postnow_bg.svg',
                                                              ),
                                                              fit: BoxFit.cover,
                                                            ),
                                                          ),
                                                        ),
                                                        Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Row(
                                                              children: [
                                                                Container(
                                                                    width: width(
                                                                            context) *
                                                                        0.38,
                                                                    margin: EdgeInsets.only(
                                                                        left: 9,
                                                                        top: 3),
                                                                    child: Text(
                                                                        '${moduleLeaderProgramList?.data?.programs?[index].name ?? ''}',
                                                                        maxLines:
                                                                            2,
                                                                        overflow:
                                                                            TextOverflow
                                                                                .ellipsis,
                                                                        softWrap:
                                                                            false,
                                                                        style: Styles.semibold(
                                                                            size:
                                                                                16))),
                                                                // SizedBox(width: 80),
                                                                moduleLeaderProgramList
                                                                            ?.data
                                                                            ?.programs?[
                                                                                index]
                                                                            .batchName !=
                                                                        null
                                                                    ? Container(
                                                                        width: width(context) *
                                                                            0.25,
                                                                        decoration: BoxDecoration(
                                                                            color: Color(
                                                                                0xffF0F1FA),
                                                                            borderRadius: BorderRadius.circular(
                                                                                10),
                                                                            border: Border.all(
                                                                                color: Color(
                                                                                    0xffF0F1FA),
                                                                                width:
                                                                                    2)),
                                                                        margin: EdgeInsets.only(
                                                                            left:
                                                                                9,
                                                                            top:
                                                                                3),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsets
                                                                              .all(
                                                                              4.0),
                                                                          child:
                                                                              Center(
                                                                            child:
                                                                                ShaderMask(
                                                                              blendMode: BlendMode.srcIn,
                                                                              shaderCallback: (Rect bounds) {
                                                                                return LinearGradient(begin: Alignment.centerLeft, end: Alignment.centerRight, colors: <Color>[
                                                                                  ColorConstants().gradientLeft(),
                                                                                  ColorConstants().gradientRight()
                                                                                ]).createShader(bounds);
                                                                              },
                                                                              child: Text('${moduleLeaderProgramList?.data?.programs?[index].batchName ?? ''}', maxLines: 2, overflow: TextOverflow.ellipsis, softWrap: false, style: Styles.semibold(size: 12, color: Color(0xff4F5AED))),
                                                                            ),
                                                                          ),
                                                                        ))
                                                                    : SizedBox(),
                                                              ],
                                                            ),
                                                            Container(
                                                                width: width(
                                                                        context) *
                                                                    0.5,
                                                                margin: EdgeInsets
                                                                    .only(
                                                                        left: 0,
                                                                        top: 3),
                                                                child: Utility()
                                                                        .isHtml(
                                                                            '${moduleLeaderProgramList?.data?.programs?[index].description ?? ''}')
                                                                    ? Html(
                                                                        data:
                                                                            '${moduleLeaderProgramList?.data?.programs?[index].description ?? ''}',
                                                                      )
                                                                    : Padding(
                                                                        padding: const EdgeInsets
                                                                            .only(
                                                                            left:
                                                                                9.0),
                                                                        child: Text(
                                                                            '${moduleLeaderProgramList?.data?.programs?[index].description ?? ''}',
                                                                            maxLines:
                                                                                2,
                                                                            overflow: TextOverflow
                                                                                .ellipsis,
                                                                            softWrap:
                                                                                false,
                                                                            style:
                                                                                Styles.regular(size: 12)),
                                                                      )),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Container(
                                                      width:
                                                          width(context) * 0.6,
                                                      margin: EdgeInsets.only(
                                                          left: 9, top: 3),
                                                      child: Row(
                                                        children: [
                                                          Icon(
                                                              Icons
                                                                  .calendar_month_outlined,
                                                              size: 20),
                                                          SizedBox(width: 10),
                                                          SizedBox(
                                                            child: Text(
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              softWrap: false,
                                                              '${Utility.convertDateFromMillis(int.parse('${moduleLeaderProgramList?.data?.programs?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} To  ${Utility.convertDateFromMillis(int.parse('${moduleLeaderProgramList?.data?.programs?[index].endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                                                              style: Styles.semibold(
                                                                  size: 12,
                                                                  color: Color(
                                                                      0xff0E1638)),
                                                              textDirection: ui
                                                                  .TextDirection
                                                                  .ltr,
                                                            ),
                                                          ),
                                                        ],
                                                      )),
                                                  Container(
                                                    // width: width(context),
                                                    margin: EdgeInsets.only(
                                                        left: 9,
                                                        top: 3,
                                                        right: 9),
                                                    child: Row(
                                                      children: [
                                                        SvgPicture.asset(
                                                          'assets/images/person.svg',
                                                          height: 20.0,
                                                          width: 20.0,
                                                          allowDrawingOutsideViewBox:
                                                              true,
                                                        ),
                                                        SizedBox(width: 10),
                                                        Text('${moduleLeaderProgramList?.data?.programs?[index].facultyNames ?? ''}',
                                                                maxLines: 2,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                                softWrap: false,
                                                                style: Styles
                                                                    .regular(
                                                                        size:
                                                                            12))
                                                            .expanded(),
                                                        Row(
                                                          children: [
                                                            SvgPicture.asset(
                                                              'assets/images/local_library.svg',
                                                              height: 20.0,
                                                              width: 20.0,
                                                              allowDrawingOutsideViewBox:
                                                                  true,
                                                            ),
                                                            SizedBox(width: 10),
                                                            Text(
                                                                '${moduleLeaderProgramList?.data?.programs?[index].totLearners ?? '0'}',
                                                                maxLines: 2,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                                softWrap: false,
                                                                style: Styles
                                                                    .regular(
                                                                        size:
                                                                            12)),
                                                          ],
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            child: Container(
                                                height: selectedIndex ==
                                                            index &&
                                                        isModuleExpanded == true
                                                    ? 130
                                                    : 60, //isModuleExpanded == true
                                                //height: 130,
                                                margin: EdgeInsets.symmetric(
                                                    horizontal: 8),
                                                decoration: BoxDecoration(
                                                    color: Color(0xffF1FBFF),
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                10)),
                                                    border: Border.all(
                                                        color:
                                                            Color(0xffF1FBFF))),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    InkWell(
                                                      onTap: () {
                                                        programCompletion
                                                            ?.programCompletion = 0;
                                                        if (selectedIndex !=
                                                            index) {
                                                          _getProgramCompletion(
                                                              programCompId:
                                                                  moduleLeaderProgramList
                                                                      ?.data
                                                                      ?.programs?[
                                                                          index]
                                                                      .id);

                                                          setState(() {
                                                            selectedIndex =
                                                                index;
                                                            isModuleExpanded =
                                                                true;
                                                          });
                                                        } else {
                                                          setState(() {
                                                            selectedIndex =
                                                                null;
                                                            isModuleExpanded =
                                                                false;
                                                          });
                                                        }
                                                      },
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                top: 15.0),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      left:
                                                                          16.0,
                                                                      top: 0.0,
                                                                      bottom:
                                                                          0.0),
                                                              child: Text(
                                                                'batch_completion',
                                                                style: Styles
                                                                    .bold(),
                                                              ).tr(),
                                                            ),
                                                            Spacer(),
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right:
                                                                          10.0),
                                                              child: selectedIndex ==
                                                                          index &&
                                                                      isModuleExpanded ==
                                                                          true
                                                                  ? Icon(
                                                                      Icons
                                                                          .keyboard_arrow_up,
                                                                      size: 20,
                                                                    )
                                                                  : Icon(
                                                                      Icons
                                                                          .keyboard_arrow_down,
                                                                      size: 20,
                                                                    ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    selectedIndex == index &&
                                                            isModuleExpanded ==
                                                                true
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        16.0),
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                  '${tr('program_completion')} ${formatter.format(programCompletion?.programCompletion ?? 0.0 ?? 0)}%',
                                                                  style: Styles
                                                                      .regular(),
                                                                ).tr(),
                                                                SizedBox(
                                                                    height: 5),
                                                                ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius.all(
                                                                          Radius.circular(
                                                                              30)),
                                                                  child:
                                                                      LinearProgressIndicator(
                                                                    minHeight:
                                                                        6,
                                                                    value: (programCompletion?.programCompletion ??
                                                                            0) /
                                                                        100,
                                                                    backgroundColor:
                                                                        Colors.grey[
                                                                            300],
                                                                    valueColor:
                                                                        AlwaysStoppedAnimation<
                                                                            Color>(
                                                                      ColorConstants()
                                                                          .gradientLeft(),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          )
                                                        : SizedBox(),
                                                  ],
                                                )
                                                //
                                                ),
                                          ),

                                          /*Positioned(
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            child: Container(
                                              margin: EdgeInsets.symmetric(
                                                  horizontal: 8),
                                              decoration: BoxDecoration(
                                                  color: Color(0xffF1FBFF),
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(10)),
                                                  border: Border.all(
                                                      color:
                                                          Color(0xffF1FBFF))),
                                              child: AnimatedContainer(
                                                duration:
                                                    Duration(milliseconds: 300),
                                                // height: isFacultyLead == true ? 100 : 60,
                                                margin: EdgeInsets.symmetric(
                                                    horizontal: 8),
                                                decoration: BoxDecoration(
                                                  color: Color(0xffF1FBFF),
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(10)),
                                                  border: Border.all(
                                                      color: Color(0xffF1FBFF)),
                                                ),
                                                child: ExpansionTile(
                                                    maintainState: false,
                                                    key: Key(index.toString()),
                                                    collapsedTextColor:
                                                        ColorConstants.BLACK,
                                                    initiallyExpanded: false,
                                                    title: Text(
                                                      'batch_completion',
                                                      style: Styles.bold(),
                                                    ).tr(),
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                    .symmetric(
                                                                horizontal:
                                                                    16.0),
                                                        child: Column(
                                                          children: [
                                                            Row(
                                                              children: [
                                                                Text(
                                                                  '${tr('program_completion')}-${formatter.format(programCompletion?.programCompletion ?? 0.0 ?? 0)}%',
                                                                  style: Styles
                                                                      .regular(),
                                                                )
                                                              ],
                                                            ),
                                                            SizedBox(height: 5),
                                                            ClipRRect(
                                                              borderRadius: BorderRadius
                                                                  .all(Radius
                                                                      .circular(
                                                                          30)),
                                                              child:
                                                                  LinearProgressIndicator(
                                                                minHeight: 6,
                                                                value: (programCompletion
                                                                            ?.programCompletion ??
                                                                        0) /
                                                                    100,
                                                                backgroundColor:
                                                                    Colors.grey[
                                                                        300],
                                                                valueColor:
                                                                    AlwaysStoppedAnimation<
                                                                        Color>(
                                                                  ColorConstants()
                                                                      .gradientLeft(),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      SizedBox(height: 5),
                                                    ],
                                                    onExpansionChanged:
                                                        ((newState) {
                                                      if (isModuleExpanded == false) {
                                                        programCompletion = null;
                                                        _getProgramCompletion(
                                                            programCompId: widget
                                                                .programModuleLead
                                                                ?.data
                                                                ?.programsModuleLead?[
                                                                    index]
                                                                .id);

                                                        setState(() {
                                                          isModuleExpanded =
                                                              true;
                                                        });
                                                      } else {
                                                        setState(() {
                                                          isModuleExpanded =
                                                              false;
                                                        });
                                                      }
                                                    })),
                                              ),
                                            ),
                                          ),*/
                                        ]);
                                      },
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox()),
                  ),
                ))));
  }

  void _handleModuleLeaderProgramList(ModuleLeaderProgramListState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................ModuleLeaderProgramListState");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................ModuleLeaderProgramListState");
            moduleLeaderProgramList = state.response;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v(
                "Error..........................ModuleLeaderProgramListState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleProgramCompletionState(ProgramCompletionState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................ProgramCompletionState");
            // isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................ProgramCompletionState");
            programCompletion = state.response;

            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................ProgramCompletionState");
            // isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void getModuleLeaderProgramList({int? courseId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(ModuleLeaderProgramListEvent(courseId: courseId));
  }
}

import 'package:flutter/material.dart';
import 'package:masterg/pages/ebook/pages/widgets/logo.dart';
import 'package:masterg/utils/constant.dart';
import '../../colors/colors.dart';
import 'register_profile.dart';

class EBookLogin extends StatelessWidget {
  const EBookLogin({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(child: LoginForm()),
    );
  }
}

class LoginForm extends StatefulWidget {
  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController otpController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Logo(),
          SizedBox(
            height: height(context) * 0.04,
          ),
          Text('Sign-in'),
          Text(
              'Every step you take brings you closer to achieving your goals and realizing your full potential.'),
          SizedBox(
            height: height(context) * 0.05,
          ),
          RoundedTextField(
            controller: phoneNumberController,
            hintText: 'Phone Number',
          ),
          SizedBox(height: 20.0),
          RoundedTextField(
            controller: otpController,
            hintText: 'OTP',
          ),
          SizedBox(height: 20.0),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // Handle sign-in logic
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => RegisterProfile()));
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorUtils.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(1.0),
                ),
              ),
              child: Text(
                'Sign In',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
          SizedBox(height: 10.0),
          RichText(
            text: TextSpan(
              text: "Don't have an account? ",
              style: DefaultTextStyle.of(context).style,
              children: [
                TextSpan(
                  text: 'Create one',
                  style: TextStyle(
                    color: ColorUtils.link,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class RoundedTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;

  RoundedTextField({required this.controller, required this.hintText});

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        hintText: hintText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(2.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(2.0),
          borderSide: BorderSide(color: Colors.grey, width: 1.4),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(2.0),
          borderSide: BorderSide(color: Colors.grey, width: 1.4),
        ),
      ),
    );
  }
}

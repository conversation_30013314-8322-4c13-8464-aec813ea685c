import 'package:flutter/material.dart';
class CreatePostProvider extends ChangeNotifier {
  bool? isUploadingPost;
  List<String?>? files = [];
  int totalMBSize = 0;
CreatePostProvider(List<String?>? files, bool? isUploading) {
   if(isUploadingPost != null)   this.isUploadingPost = isUploading;

    if (files != null) {
      this.files = files;
    }

    notifyListeners();
  }
   void addSize(int size) {
    this.totalMBSize += size;
    notifyListeners();
  }

  void subSize(int size) {
    this.totalMBSize -= size;
    notifyListeners();
  }

  void addToList(String? path) {
    this.files!.insert(0, path);
    notifyListeners();
  }

  void updateList(List<String?>? list){
    this.files  = list;
    notifyListeners();
  }

  List<String?>? getFiles(){
    return this.files;
  }

  void updateAtIndex(String? path, int index){
    this.files![index] = path;
    notifyListeners();
  }

  void removeFromList(int index) {
    
    this.files!.removeAt(index);
    notifyListeners();
  }

  void clearList() {
    this.files!.clear();
    notifyListeners();
  }

  void postStatus(bool isUploading ){
    this.isUploadingPost = isUploading;
    notifyListeners();
  }

  bool? getPostStatus(){
    return this.isUploadingPost;
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import 'package:provider/provider.dart';

import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/gcarvaan_post_reponse.dart';
import '../../../utils/Log.dart';
import '../../../utils/Styles.dart';
import '../../../utils/resource/colors.dart';
import '../components/gcarvaan_card_post.dart';

class GcarvaanPostShortByCommunity extends StatefulWidget {
  final int? postId;
  final String? title;

  GcarvaanPostShortByCommunity({
    Key? key,
    this.postId,
    this.title,
  }) : super(key: key);


  @override
  State<GcarvaanPostShortByCommunity> createState() => _GcarvaanPostShortByCommunityState();
}

class _GcarvaanPostShortByCommunityState extends State<GcarvaanPostShortByCommunity> {
  Box? box;
  GCarvaanPostElement? postResponse;
  bool isPostedLoading = true;
  late GCarvaanListModel provider;
  int callCount = 0;

  @override
  void initState() {
    super.initState();
    provider = Provider.of<GCarvaanListModel>(context, listen: false);
    _getPosts(++callCount, postId: widget.postId);
  }

  @override
  void dispose() {
    log("clear the data");
    provider.clearData();
    super.dispose();
  }

  void _getPosts(callCount, {postId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(SinlgePostGCarvaanEvent(postId: postId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) async {
        if (state is SinlgePostGCarvaanState) {
          _handleGCarvaanPostResponse(state, provider);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          iconTheme: IconThemeData(color: ColorConstants.BLACK),
          backgroundColor: ColorConstants.WHITE,
          elevation: 0,
          title: Text('${widget.title}', style: Styles.bold(
              color: ColorConstants.BLACK,
              size: 14)),
        ),
        body: isPostedLoading
            ? Center(
          child: CircularProgressIndicator(),
        )
            : SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            width: double.infinity,
            child: GCarvaanCardPost(
                index: 0,
                value: provider,
                userStatus: postResponse?.userStatus ?? '',
                image_path: postResponse?.resourcePath,
                date: '${postResponse?.createdAt}',
                description: postResponse?.description,
                commentCount: postResponse?.commentCount ?? 0,
                user_name: postResponse?.name,
                profile_path: postResponse?.profileImage,
                likeCount: postResponse?.likeCount ?? 0,
                viewCount: postResponse?.viewCount ?? 0,
                islikedPost: postResponse?.userLiked == 1 ? true : false,
                contentId: postResponse?.id,
                fileList: postResponse?.multiFileUploads,
                comment_visible: false,
                height: postResponse?.dimension?.height,
                // dimension: postResponse?.multiFileUploadsDimension,
                width: postResponse?.dimension?.width,
                resourceType: postResponse?.resourceType,
                userID: postResponse?.userId,
                fromUserActivity: false,
                aspectRatio: postResponse?.multiFileUploadsDimension?.first?.aspectRatio,
                flagCommunity: false,),

          ),
        ),
      ),
    );
  }

  void _handleGCarvaanPostResponse(
      SinlgePostGCarvaanState state, GCarvaanListModel model) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isPostedLoading = true;
          break;
        case ApiStatus.SUCCESS:
          isPostedLoading = false;
          Log.v(
              "Loading.................... ${state.response!.data!.list?.first.toJson()}");
          try {
            postResponse = state.response?.data?.list?.first;
            model.updateList([postResponse!]);
          } catch (e, stackTrace) {
            log('Error getting post reponse $e ');
            log('Error getting post reponse $stackTrace ');
          }
          break;

        case ApiStatus.ERROR:
          isPostedLoading = false;

          Log.v(
            "Error..........................",
          );
          Log.v("ErrorHome..........................${loginState.error}");

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}


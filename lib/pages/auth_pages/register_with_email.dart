import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:masterg/pages/auth_pages/self_details_page.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import '../../blocs/bloc_manager.dart';
import '../../blocs/home_bloc.dart';
import '../../data/api/api_service.dart';
import '../../utils/Log.dart';
import '../../utils/utility.dart';
import '../custom_pages/ScreenWithLoader.dart';
import '../custom_pages/TapWidget.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  bool isFill = false;
  TextEditingController emailController = TextEditingController();
  TextEditingController otpController = TextEditingController();
  TextEditingController newPassController = TextEditingController();
  TextEditingController confPassController = TextEditingController();

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool isCodeSent = false;
  bool codeVerified = false;
  bool _isLoading = false;
  bool _emailTrue = false;
  bool _codeVerifiedTrue = false;
  int endTime = 0;
  bool verifiedAutoFocus = true;
  var _isObscurePass = true;
  var _isObscureConfPass = true;

  void fieldValidation() {
    if (!_formKey.currentState!.validate()) return;

    if (newPassController.text.isEmpty || confPassController.text.isEmpty) {
      Utility.showSnackBar(
          scaffoldContext: context, message: tr('password_confirm_password'));
    } else {
      if (newPassController.text.toString().length > 7) {
        if (newPassController.text == confPassController.text) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => SelfDetailsPage(
                        email: emailController.text.toString().trim(),
                        password: confPassController.text.toString().trim(),
                        loginWithEmail: true,
                      )));
        } else {
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('password_notmatched'));
        }
      } else {
        Utility.showSnackBar(
            scaffoldContext: context, message: tr('security_password_length'));
      }
    }
  }

  void sendEmailVerificationCode(String email) {
    BlocProvider.of<HomeBloc>(context)
        .add(EmailCodeSendEvent(email: email, isSignup: 1));
  }

  void verifyOtp(String email, String otp) {
    BlocProvider.of<HomeBloc>(context)
        .add(VerifyEmailCodeEvent(email: email, code: otp));
  }

  void _handleEmailCodeSendResponse(EmailCodeSendState state) {
    var emailCodeSendState = state;
    setState(() {
      switch (emailCodeSendState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("EmailCodeSend Suuuuuuuus111111111....................");
          setState(() {
            isCodeSent = true;
            _isLoading = false;
          });
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('verify_code_on_email'));
          endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error emailCodeSendState ..........................${emailCodeSendState.error}");
          if (emailCodeSendState.error == null) {
            Utility.showSnackBar(
                scaffoldContext: context, message: tr('mail_server_issue'));
          } else {
            Utility.showSnackBar(
                scaffoldContext: context,
                message: '${emailCodeSendState.error}');
          }
          setState(() {
            _isLoading = false;
          });
          FirebaseAnalytics.instance
              .logEvent(name: 'register_page', parameters: {
            "ERROR": '${emailCodeSendState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleVerifyOtp(VerifyEmailCodeState state) {
    var emailCodeSendState = state;
    setState(() {
      switch (emailCodeSendState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          codeVerified = false;
          break;
        case ApiStatus.SUCCESS:
          Log.v("VerifyOtp Suuuuuuuus....................");
          codeVerified = true;
          _codeVerifiedTrue = true;
          _isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error handleVerifyOtp ..........................${emailCodeSendState.error}");
          _isLoading = false;
          codeVerified = false;
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('invalid_code'));
          FirebaseAnalytics.instance
              .logEvent(name: 'register_page', parameters: {
            "ERROR": '${emailCodeSendState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) async {
            if (state is EmailCodeSendState) {
              _handleEmailCodeSendResponse(state);
            }
            if (state is VerifyEmailCodeState) {
              handleVerifyOtp(state);
            }
          },
          child: Scaffold(
            appBar: AppBar(
              centerTitle: true,
              backgroundColor: Colors.transparent,
              title: Text(
                "register_with_email",
                style: Styles.semibold(color: ColorConstants.BLACK, size: 16),
              ).tr(),
              leading: InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Icon(
                  Utility().isRTL(context)
                      ? Icons.arrow_back_ios_sharp
                      : Icons.arrow_back_ios_new_outlined,
                  color: ColorConstants.BLACK,
                ),
              ),
              elevation: 0.0,
            ),
            //body: SafeArea(),

            body: ScreenWithLoader(
              isLoading: _isLoading,
              body: Container(
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Padding(
                          padding: Utility().isRTL(context)
                              ? EdgeInsets.only(right: 20.0, top: 20.0)
                              : EdgeInsets.only(left: 20.0, top: 20.0),
                          child: Row(
                            children: [
                              ShaderMask(
                                  blendMode: BlendMode.srcIn,
                                  shaderCallback: (Rect bounds) {
                                    return LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: <Color>[
                                          ColorConstants().gradientLeft(),
                                          ColorConstants().gradientRight()
                                        ]).createShader(bounds);
                                  },
                                  child: Icon(
                                    Icons.email_outlined,
                                    size: 20,
                                  )),
                              SizedBox(
                                width: 8,
                              ),
                              Text(
                                "${tr('email')} ",
                                style: Styles.textRegular(),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: Utility().isRTL(context)
                              ? EdgeInsets.only(top: 8.0, right: 16, left: 16)
                              : EdgeInsets.only(top: 8.0, left: 16, right: 16),
                          child: Container(
                            height: height(context) * 0.1,
                            child: TextFormField(
                              cursorColor: ColorConstants().gradientRight(),
                              autofocus: true,
                              controller: emailController,
                              style: Styles.regular(
                                color: Color(0xff0E1638),
                                size: 14,
                              ),
                              decoration: InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                  borderSide: BorderSide(
                                    color: Color(0xffE5E5E5),
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                  borderSide: BorderSide(
                                    color: Color(0xffE5E5E5),
                                    width: 1.5,
                                  ),
                                ),
                                suffix: isCodeSent
                                    ? Container(
                                        padding: EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: ColorConstants.GREEN,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.done,
                                          size: 12,
                                          color: Colors.white,
                                        ),
                                      )
                                    : GestureDetector(
                                        onTap: () {
                                          codeVerified = false;
                                          _codeVerifiedTrue = false;
                                          otpController.clear();
                                          newPassController.clear();
                                          confPassController.clear();
                                          if (emailController
                                              .value.text.isEmpty) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(SnackBar(
                                              content:
                                                  Text('plz_enter_email').tr(),
                                            ));
                                          } else if (_emailTrue == false) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(SnackBar(
                                              content: Text('enter_valid_email')
                                                  .tr(),
                                            ));
                                          } else {
                                            sendEmailVerificationCode(
                                                emailController.value.text);
                                          }
                                        },
                                        child: GradientText(
                                          tr('verify_code'),
                                          style: Styles.regular(size: 14),
                                          colors: _emailTrue == true
                                              ? [
                                                  ColorConstants()
                                                      .gradientLeft(),
                                                  ColorConstants()
                                                      .gradientRight()
                                                ]
                                              : [
                                                  ColorConstants
                                                      .UNSELECTED_BUTTON,
                                                  ColorConstants
                                                      .UNSELECTED_BUTTON
                                                ],
                                        ),
                                      ),
                                hintText: '<EMAIL>',
                                hintStyle: TextStyle(
                                  color: Color(0xffE5E5E5),
                                ),
                                isDense: true,
                                prefixIconConstraints:
                                    BoxConstraints(minWidth: 0, minHeight: 0),
                                border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        width: 1, color: ColorConstants.WHITE),
                                    borderRadius: BorderRadius.circular(10)),
                                helperStyle: Styles.regular(
                                    size: 14,
                                    color: ColorConstants.GREY_3
                                        .withValues(alpha: 0.1)),
                                counterText: "",
                              ),
                              onChanged: (value) {
                                setState(() {
                                  if (!RegExp(
                                          r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                      .hasMatch(value)) {
                                    _emailTrue = false;
                                    isCodeSent = false;
                                    otpController.clear();
                                    newPassController.clear();
                                    confPassController.clear();
                                  } else {
                                    _emailTrue = true;
                                    otpController.clear();
                                    newPassController.clear();
                                    confPassController.clear();
                                  }
                                });
                              },
                              validator: (value) {
                                if (value == '') return tr('email_required');
                                int index = value?.length as int;

                                if (value![index - 1] == '.')
                                  return tr('email_address_error');

                                if (!RegExp(
                                        r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                    .hasMatch(value))
                                  return tr('email_address_error');

                                isCodeSent = true;
                                return null;
                              },
                            ),
                          ),
                        ),

                        ///Re-Send Code
                        isCodeSent == true
                            ? Row(
                                children: [
                                  Expanded(
                                    child: SizedBox(),
                                  ),
                                  CountdownTimer(
                                    endTime: endTime,
                                    widgetBuilder:
                                        (_, CurrentRemainingTime? time) {
                                      return Padding(
                                        padding: Utility().isRTL(context)
                                            ? EdgeInsets.only(
                                                left: 20.0, bottom: 15.0)
                                            : EdgeInsets.only(
                                                right: 20.0, bottom: 15.0),
                                        child: RichText(
                                          text: TextSpan(
                                              text: '',
                                              style: TextStyle(
                                                fontSize: 3,
                                              ),
                                              children: <TextSpan>[
                                                time == null
                                                    ? TextSpan(
                                                        text: tr('resend_code'),
                                                        recognizer:
                                                            TapGestureRecognizer()
                                                              ..onTap = () {
                                                                _emailTrue =
                                                                    false;
                                                                isCodeSent =
                                                                    false;
                                                                codeVerified =
                                                                    false;
                                                                _codeVerifiedTrue =
                                                                    false;
                                                                otpController
                                                                    .clear();
                                                                newPassController
                                                                    .clear();
                                                                confPassController
                                                                    .clear();

                                                                sendEmailVerificationCode(
                                                                    emailController
                                                                        .value
                                                                        .text);
                                                              },
                                                        style: Styles.regular(
                                                            size: 12,
                                                            color:
                                                                ColorConstants
                                                                    .BLACK))
                                                    : TextSpan(
                                                        text:
                                                            tr('resend_app_secs') +
                                                                ' ${time.sec} ' +
                                                                tr('secs'),
                                                        style: Styles.regular(
                                                            size: 12,
                                                            color:
                                                                ColorConstants
                                                                    .BLACK)),
                                              ]),
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              )
                            : SizedBox(),

                        ///enter email Code--
                        isCodeSent == true
                            ? Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(right: 20.0)
                                    : EdgeInsets.only(left: 20.0),
                                child: Row(
                                  children: [
                                    Text(
                                      tr('code_received_mail'),
                                      style: Styles.textRegular(),
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox(),
                        isCodeSent == true
                            ? Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(
                                        top: 8.0, right: 16, left: 16)
                                    : EdgeInsets.only(
                                        top: 8.0, left: 16, right: 16),
                                child: Container(
                                  height: height(context) * 0.1,
                                  child: Stack(
                                    children: [
                                      TextFormField(
                                        obscureText: false,
                                        keyboardType: TextInputType.number,
                                        cursorColor: codeVerified == false
                                            ? ColorConstants().gradientRight()
                                            : Colors.white,
                                        controller: otpController,
                                        style: Styles.otp(
                                            color: Colors.black,
                                            size: 14,
                                            letterSpacing: 40),
                                        maxLength: 4,
                                        onChanged: (value) {
                                          setState(() {
                                            if (value.length == 4) {
                                              codeVerified = true;
                                            } else {
                                              codeVerified = false;
                                            }
                                          });
                                        },
                                        decoration: InputDecoration(
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                              width: 1.5,
                                            ),
                                          ),
                                          fillColor: Color(0xffE5E5E5),
                                          hintText: '••••',
                                          hintStyle: TextStyle(
                                            color: Colors.black,
                                          ),
                                          isDense: true,
                                          prefixIconConstraints: BoxConstraints(
                                              minWidth: 0, minHeight: 0),
                                          border: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                  width: 1,
                                                  color: ColorConstants.WHITE),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          helperStyle: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.GREY_3
                                                  .withValues(alpha: 0.1)),
                                        ),
                                      ),
                                      Positioned(
                                        right: Utility().isRTL(context)
                                            ? null
                                            : 10,
                                        left: Utility().isRTL(context)
                                            ? 10
                                            : null,
                                        top: 14,
                                        child: _codeVerifiedTrue
                                            ? Container(
                                                padding: EdgeInsets.all(4),
                                                decoration: BoxDecoration(
                                                  color: ColorConstants.GREEN,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Icon(
                                                  Icons.done,
                                                  size: 12,
                                                  color: Colors.white,
                                                ),
                                              )
                                            : GestureDetector(
                                                onTap: () {
                                                  if (otpController
                                                      .value.text.isEmpty) {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(SnackBar(
                                                      content: Text(
                                                              'enter_verification_code')
                                                          .tr(),
                                                    ));
                                                  } else if (codeVerified ==
                                                      false) {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(SnackBar(
                                                      content: Text(
                                                              'valid_four_digit_code')
                                                          .tr(),
                                                    ));
                                                  } else {
                                                    verifyOtp(
                                                        emailController
                                                            .value.text,
                                                        otpController
                                                            .value.text);
                                                  }
                                                },
                                                child: GradientText(
                                                  tr('verify_otp'),
                                                  style:
                                                      Styles.regular(size: 14),
                                                  colors: codeVerified == true
                                                      ? [
                                                          ColorConstants()
                                                              .gradientLeft(),
                                                          ColorConstants()
                                                              .gradientRight(),
                                                        ]
                                                      : [
                                                          ColorConstants
                                                              .UNSELECTED_BUTTON,
                                                          ColorConstants
                                                              .UNSELECTED_BUTTON,
                                                        ],
                                                ),
                                              ),
                                      )
                                    ],
                                  ),
                                ),
                              )
                            : SizedBox(),

                        _codeVerifiedTrue == true
                            ? Column(
                                children: [
                                  Padding(
                                    padding: Utility().isRTL(context)
                                        ? EdgeInsets.only(right: 20.0)
                                        : EdgeInsets.only(left: 20.0),
                                    child: Row(
                                      children: [
                                        ShaderMask(
                                          blendMode: BlendMode.srcIn,
                                          shaderCallback: (Rect bounds) {
                                            return LinearGradient(
                                                begin: Alignment.centerLeft,
                                                end: Alignment.centerRight,
                                                colors: <Color>[
                                                  ColorConstants()
                                                      .gradientLeft(),
                                                  ColorConstants()
                                                      .gradientRight()
                                                ]).createShader(bounds);
                                          },
                                          child: Icon(
                                            Icons.lock_outline,
                                            size: 20,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 8,
                                        ),
                                        Text(
                                          "enter_pwd",
                                          style: Styles.textRegular(),
                                        ).tr(),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: Utility().isRTL(context)
                                        ? EdgeInsets.only(
                                            top: 8.0, right: 16, left: 16)
                                        : EdgeInsets.only(
                                            top: 8.0, left: 16, right: 16),
                                    child: Container(
                                      height: height(context) * 0.1,
                                      child: TextField(
                                        obscureText: _isObscurePass,
                                        cursorColor:
                                            ColorConstants().gradientRight(),
                                        autofocus: false,
                                        controller: newPassController,
                                        style: Styles.bold(
                                          color: Colors.black,
                                          size: 14,
                                        ),
                                        maxLength: 20,
                                        decoration: InputDecoration(
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                              width: 1.5,
                                            ),
                                          ),
                                          fillColor: Color(0xffE5E5E5),
                                          hintText:
                                              'please_enter_password'.tr(),
                                          hintStyle: TextStyle(
                                            color: Color(0xffE5E5E5),
                                          ),
                                          isDense: true,
                                          prefixIconConstraints: BoxConstraints(
                                              minWidth: 0, minHeight: 0),
                                          border: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                  width: 1,
                                                  color: ColorConstants.WHITE),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          helperStyle: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.GREY_3
                                                  .withValues(alpha: 0.1)),
                                          counterText: "",
                                          suffixIcon: Visibility(
                                            visible: true,
                                            child: TapWidget(
                                              onTap: () {
                                                this.setState(() {
                                                  _isObscurePass =
                                                      !_isObscurePass;
                                                });
                                              },
                                              child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 5),
                                                  child: !_isObscurePass
                                                      ? Icon(
                                                          Icons
                                                              .remove_red_eye_outlined,
                                                          color: ColorConstants
                                                              .PRIMARY_COLOR,
                                                        )
                                                      : Icon(
                                                          Icons.visibility_off,
                                                          color: ColorConstants
                                                              .PRIMARY_COLOR,
                                                        )),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: Utility().isRTL(context)
                                        ? EdgeInsets.only(right: 20.0)
                                        : EdgeInsets.only(left: 20.0),
                                    child: Row(
                                      children: [
                                        ShaderMask(
                                          blendMode: BlendMode.srcIn,
                                          shaderCallback: (Rect bounds) {
                                            return LinearGradient(
                                                begin: Alignment.centerLeft,
                                                end: Alignment.centerRight,
                                                colors: <Color>[
                                                  ColorConstants()
                                                      .gradientLeft(),
                                                  ColorConstants()
                                                      .gradientRight()
                                                ]).createShader(bounds);
                                          },
                                          child: Icon(
                                            Icons.lock_outline,
                                            size: 20,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 8,
                                        ),
                                        Text(
                                          'confirm_password',
                                          style: Styles.textRegular(),
                                        ).tr(),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: Utility().isRTL(context)
                                        ? EdgeInsets.only(
                                            top: 8.0, right: 16, left: 16)
                                        : EdgeInsets.only(
                                            top: 8.0, left: 16, right: 16),
                                    child: Container(
                                      child: TextField(
                                        obscureText: _isObscureConfPass,
                                        cursorColor:
                                            ColorConstants().gradientRight(),
                                        autofocus: false,
                                        controller: confPassController,
                                        style: Styles.bold(
                                          color: Colors.black,
                                          size: 14,
                                        ),
                                        maxLength: 20,
                                        decoration: InputDecoration(
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                              width: 1.5,
                                            ),
                                          ),
                                          fillColor: Color(0xffE5E5E5),
                                          hintText:
                                              'please_enter_confirm_password'
                                                  .tr(),
                                          hintStyle: TextStyle(
                                            color: Color(0xffE5E5E5),
                                          ),
                                          isDense: true,
                                          prefixIconConstraints: BoxConstraints(
                                              minWidth: 0, minHeight: 0),
                                          border: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                  width: 1,
                                                  color: ColorConstants.WHITE),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          helperStyle: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.GREY_3
                                                  .withValues(alpha: 0.1)),
                                          counterText: "",
                                          suffixIcon: Visibility(
                                            visible: true,
                                            child: TapWidget(
                                              onTap: () {
                                                this.setState(() {
                                                  _isObscureConfPass =
                                                      !_isObscureConfPass;
                                                });
                                              },
                                              child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 5),
                                                  child: !_isObscureConfPass
                                                      ? Icon(
                                                          Icons
                                                              .remove_red_eye_outlined,
                                                          color: ColorConstants
                                                              .PRIMARY_COLOR,
                                                        )
                                                      : Icon(
                                                          Icons.visibility_off,
                                                          color: ColorConstants
                                                              .PRIMARY_COLOR,
                                                        )),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            : SizedBox(),

                        _codeVerifiedTrue == true
                            ? Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(
                                        right: 18.0,
                                        left: 18.0,
                                        bottom: 18.0,
                                      )
                                    : EdgeInsets.only(
                                        left: 18.0,
                                        right: 18.0,
                                        bottom: 18.0,
                                      ),
                                child: Container(
                                  height: height(context) * 0.28,
                                  child: Column(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Container(
                                          height: height(context) * 0.06,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            gradient:
                                                emailController.value.text !=
                                                            '' &&
                                                        newPassController
                                                                .value.text !=
                                                            ''
                                                    ? LinearGradient(colors: [
                                                        ColorConstants()
                                                            .gradientLeft(),
                                                        ColorConstants()
                                                            .gradientRight(),
                                                      ])
                                                    : LinearGradient(colors: [
                                                        ColorConstants
                                                            .UNSELECTED_BUTTON,
                                                        ColorConstants
                                                            .UNSELECTED_BUTTON,
                                                      ]),
                                          ),
                                          child: InkWell(
                                            onTap: () {
                                              setState(() {});
                                              fieldValidation();
                                            },
                                            child: Center(
                                              child: Text(
                                                'next',
                                                style: Styles.regular(
                                                  size: 16,
                                                  color: ColorConstants.WHITE,
                                                ),
                                              ).tr(),
                                            ),
                                          ),
                                        ),
                                      ]),
                                ),
                              )
                            : SizedBox(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ));
  }
}

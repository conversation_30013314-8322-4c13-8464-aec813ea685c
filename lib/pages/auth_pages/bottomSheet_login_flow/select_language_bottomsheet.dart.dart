import 'dart:developer';
import 'dart:core';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_view_resp.dart';
import 'package:masterg/pages/auth_pages/bottomSheet_login_flow/login_type_screen.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/data/models/response/home_response/master_language_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';

class SelectLanguageBottomSheet extends StatefulWidget {
  final bool showEdulystLogo;
  SelectLanguageBottomSheet({Key? key, required this.showEdulystLogo})
      : super(key: key);

  @override
  _SelectLanguageBottomSheetState createState() =>
      _SelectLanguageBottomSheetState();
}

class _SelectLanguageBottomSheetState extends State<SelectLanguageBottomSheet> {
  @override
  void initState() {
    super.initState();
    _getLanguage();
    setCurrentLanguage();
    // getDashboardIsVisible();
  }

  var selected = 0;

  bool _isLoading = false;
  List<ListLanguage>? myList;
  DashboardViewResponse? dashboardViewResponse;
  WhoAmI? iAmAItemsList;
  Messages? loginTypeMsg;
  int? selectedLangCard = -1;

  var localeCodes = {
    'english': "en",
    'hindi': "hi",
    'kannada': "kn",
    'marathi': "mr",
    'tamil': "ta",
    'telugu': "te",
    'bengali': "bn",
    'malyalam': 'ml',
    'arabic': 'ar',
  };

  void setCurrentLanguage() async {
    int? currentLanId = Preference.getInt(Preference.APP_LANGUAGE) ?? 1;

    for (int i = 0; i < myList!.length; i++)
      if (currentLanId == myList?[i].languageId) {
        selected = i;
        Preference.setInt(Preference.APP_LANGUAGE, myList![i].languageId!);
        Preference.setInt(
            Preference.IS_PRIMARY_LANGUAGE, myList![i].isPrimaryLanguage!);
        Preference.setString(
            Preference.APP_ENGLISH_NAME, myList![i].englishName.toString());
        Preference.setString(
            Preference.LANGUAGE, '${myList?[i].languageCode?.toLowerCase()}');
        context.setLocale(Locale('${myList?[i].languageCode}'));
        break;
      } else {
        selected = 0;
      }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is MasterLanguageState) {
            _handleResponse(state);
          }
        },
        child: Builder(builder: (_context) {
          return Container(
              child: ScreenWithLoader(
            color: Colors.transparent,
            isLoading: _isLoading,
            isContainerHeight: false,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                    width: width(context),
                    margin: EdgeInsets.symmetric(
                        horizontal:
                            width(context) > 1000 ? width(context) * 0.1 : 10),
                    child: ListView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: myList?.length ?? 0,
                      itemBuilder: (BuildContext context, int index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 8),
                          child: languageCard(myList![index], index),
                        );
                      },
                    )),
                InkWell(
                  onTap: () {
                    if (selectedLangCard == -1) {
                      AlertsWidget.showCustomDialog(
                          context: context,
                          title: tr('app_language'),
                          text: "",
                          icon: 'assets/images/circle_alert_fill.svg',
                          showCancel: false,
                          oKText: tr('ok'),
                          onOkClick: () async {});
                    } else {
                      _getLoginTypeBottomSheet();
                    }
                  },
                  child: _isLoading
                      ? SizedBox()
                      : Container(
                          height: height(context) * 0.06,
                          width: width(context),
                          margin:  EdgeInsets.only(
                              left: 15, right: 15, top: 20, bottom: 20),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: selectedLangCard == -1
                                  ? ColorConstants.DARK_BUTTON
                                  : ColorConstants.WHITE),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    'Continue_button',
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold),
                                  ).tr(),
                                  SizedBox(width: 10),
                                  Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    size: 15,
                                    color: Colors.black,
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ));
        }),
      ),
    );
  }

  void _handleResponse(MasterLanguageState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          _isLoading = false;
          myList = state.response!.data!.listData;
          setCurrentLanguage();
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          FirebaseAnalytics.instance
              .logEvent(name: 'language_page', parameters: {
            "Error": '${state.response?.error?.first}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _getLanguage() {
    BlocProvider.of<HomeBloc>(context).add(MasterLanguageEvent());
  }

  Widget languageCard(langauge, index) {
    return InkWell(
      onTap: () {
        Preference.setInt(Preference.APP_LANGUAGE, langauge.languageId);
        Preference.setInt(
            Preference.IS_PRIMARY_LANGUAGE, langauge.isPrimaryLanguage);
        setState(() {
          // selected = index;
          selectedLangCard = index;
          context.setLocale(Locale('${langauge.languageCode}'));

          try {
            Preference.setString(
                Preference.LANGUAGE, langauge.languageCode.toLowerCase());
            Preference.setString(Preference.APP_ENGLISH_NAME,
                langauge.englishName.toLowerCase());
          } catch (e, stack) {
            log('$stack');
          }
        });
      },
      child: Container(
          height: height(context) * 0.06,
          width: width(context),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: ColorConstants.DARK_BUTTON),
          child: Stack(
            children: [
              Positioned(
                  left: 10,
                  top: 13,
                  child: SvgPicture.asset(
                    height: 16,
                    index == selectedLangCard
                        ? 'assets/images/selected_check.svg'
                        : 'assets/images/unselected_lang.svg',
                    fit: BoxFit.cover,
                  )),
              Center(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${langauge.title ?? ''}',
                    style: Styles.bold(
                        size: 16,
                        color: index == selectedLangCard
                            ? ColorConstants.GREEN
                            : ColorConstants.WHITE),
                  ),
                  SizedBox(height: 2),
                  Text(langauge.name ?? '',
                      style: Styles.regular(
                        size: 12,
                        color: ColorConstants.WHITE,
                      )),
                ],
              )),
            ],
          )),
    );
  }

  _getLoginTypeBottomSheet() {
    return showModalBottomSheet(
        enableDrag: false,
        isDismissible: false,
        isScrollControlled: true,
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return Padding(
                padding: const EdgeInsets.only(top: 210.0),
                child: Container(
                    color: Colors.transparent,
                    child: _isLoading ? SizedBox() : LoginTypeScreen()));
          });
        });
  }

 
}

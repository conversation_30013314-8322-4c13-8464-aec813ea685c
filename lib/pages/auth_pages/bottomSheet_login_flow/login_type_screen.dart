import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_view_resp.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/bottomSheet_login_flow/login_with_showBottomSheet.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'dart:math' as math;

class LoginTypeScreen extends StatefulWidget {
  const LoginTypeScreen({super.key});

  @override
  State<LoginTypeScreen> createState() => _LoginTypeScreenState();
}

class _LoginTypeScreenState extends State<LoginTypeScreen> {
  @override
  void initState() {
    getDashboardIsVisible();
    // TODO: implement initState
    super.initState();
  }

  int? selectedIndex = -1;
  bool _isLoading = false;
  final List<String> imageUrls = [
    'assets/images/mec_student.svg',
    'assets/images/mec_faculty.svg',
    'assets/images/mec_alumni.svg',
    'assets/images/prospective_student.svg',
    'assets/images/corporate.svg',
    'assets/images/government.svg',
  ];
  DashboardViewResponse? dashboardViewResponse;

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(listener: (context, state) {
          if (state is DashboardIsVisibleState) {
            handleDashboardIsVisible(state);
          }
        }, child: Builder(builder: (_context) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            resizeToAvoidBottomInset: false,
            body: ScreenWithLoader(
              isContainerHeight: false,
                color: Colors.transparent,
              isLoading: _isLoading,
              body: Column(
                children: [
                  Container(
                    height: Utility().isRTL(context)
                        ? MediaQuery.of(context).size.height * 0.72
                        : MediaQuery.of(context).size.height * 0.72,
                    decoration: BoxDecoration(
                      color: ColorConstants.DARK_BACKGROUND,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          Row(
                            children: [
                              Transform.rotate(
                                angle: Utility().isRTL(context) ? -math.pi : 0,
                                child: Container(
                                  height: 20,
                                  width: 20,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(100),
                                      color: ColorConstants.WHITE),
                                  child: InkWell(
                                      onTap: () {
                                        Navigator.pop(context);
                                      },
                                      child: Icon(
                                          Icons.arrow_back_ios_new_outlined,
                                          size: 10)),
                                ),
                              ),
                              SizedBox(width: 110),
                              Text('i_am',
                                      style: Styles.DMSansregular(
                                          color: ColorConstants.ACCENT_COLOR,
                                          size: 16))
                                  .tr(),
                            ],
                          ),
                          SizedBox(height: 20),
                          ListView.builder(
                            itemCount: dashboardViewResponse
                                    ?.data
                                    ?.whoAmI[
                                        '${Preference.getString(Preference.LANGUAGE)}']
                                    .length ?? 0,
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              String key = dashboardViewResponse
                                  ?.data
                                  ?.whoAmI[
                                      '${Preference.getString(Preference.LANGUAGE)}']
                                  .keys
                                  .toList()[index];
                              String value = dashboardViewResponse
                                          ?.data?.whoAmI[
                                      '${Preference.getString(Preference.LANGUAGE)}']
                                  [key];
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: GestureDetector(
                                  onTap: () {
                                    Preference.setString(
                                        Preference.REGISTER_TYPE, '$key');

                                    Preference.setString(
                                        Preference.USER_LOGIN_TYPE, '$value');

                                    Preference.setString(
                                        Preference.USER_LOGIN_TYPE_LOCALE,
                                        '${dashboardViewResponse?.data?.whoAmI['en'][key]}');
                                    setState(() {
                                      selectedIndex = index;
                                    });
                                  },
                                  child: Container(
                                    height: height(context) * 0.06,
                                    width: width(context),
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                          color: index == selectedIndex
                                              ? Colors.grey
                                              : ColorConstants.DARK_BUTTON,
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                        color: ColorConstants.DARK_BUTTON),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              left: 10.0, right: 10),
                                          child: SvgPicture.asset(
                                            height: 16,
                                            index == selectedIndex
                                                ? 'assets/images/selected_check.svg'
                                                : 'assets/images/unselected_lang.svg',
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        SizedBox(width: 70),
                                        SvgPicture.asset('${imageUrls[index]}'),
                                        SizedBox(width: 10),
                                        Expanded(
                                          child: Text(
                                            '$value',
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold),
                                          ).tr(),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                          SizedBox(height: 30),
                          InkWell(
                            onTap: () {
                              if (selectedIndex != -1) {
                               getUserLoginBottomSheet();
                              } else if (selectedIndex == -1) {
                                AlertsWidget.showCustomDialog(
                                    context: context,
                                    title: tr('plz_enter_login_Type'),
                                    text: "",
                                    icon: 'assets/images/circle_alert_fill.svg',
                                    showCancel: false,
                                    oKText: tr('ok'),
                                    onOkClick: () async {});
                              } else {
                                Navigator.pop(context);
                              }
                            },
                            child:  _isLoading?SizedBox():
                            Container(
                              height: height(context) * 0.06,
                              width: width(context),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: selectedIndex == -1
                                      ? ColorConstants.DARK_BUTTON
                                      : ColorConstants.WHITE),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(width: 10),
                                  Text(
                                    'Continue_button',
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold),
                                  ).tr(),
                                  SizedBox(width: 10),
                                  Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    size: 15,
                                    color: ColorConstants.BLACK,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          );
        })));
  }

  Future<void> getDashboardIsVisible() async {
    BlocProvider.of<HomeBloc>(context).add(DashboardIsVisibleEvent());
  }

  void handleDashboardIsVisible(DashboardIsVisibleState state) {
    var dashboardIsVisibleState = state;
    setState(() {
      switch (dashboardIsVisibleState.apiState) {
        case ApiStatus.LOADING:
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          dashboardViewResponse = state.response;
          _isLoading = false;
          // setCurrentLanguage();
          // iAmAItemsList = dashboardViewResponse?.data?.whoAmI;
          // loginTypeMsg = dashboardViewResponse?.data?.messages;
          // jsonDecode(dashboardViewResponse?.data?.messages ?? '');
          Log.v("getDashboardIsVisible................... $dashboardViewResponse");

          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          break;
        case ApiStatus.INITIAL:
          _isLoading = false;
          break;
      }
    });
  }
   getUserLoginBottomSheet() {
    return showModalBottomSheet(
        enableDrag: false,
        isDismissible: false,
        isScrollControlled: true,
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.78,
              decoration: BoxDecoration(
                color: ColorConstants.DARK_BACKGROUND,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      children: [
                        Transform.rotate(
                          angle: Utility().isRTL(context) ? -math.pi : 0,
                          child: Container(
                            height: 20,
                            width: 20,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(100),
                                color: ColorConstants.WHITE),
                            child: InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: Icon(Icons.arrow_back_ios_new_outlined,
                                    size: 10)),
                          ),
                        ),
                        Expanded(
                          child: Center(
                            child: RichText(
                              text: TextSpan(
                                text: tr('welcome_text'),
                                style: Styles.textBold(
                                    lineHeight: 1.3,
                                    color: ColorConstants.WHITE,
                                    size: 22),
                                children: <TextSpan>[
                                  TextSpan(
                                    text:
                                        ', ${dashboardViewResponse?.data?.messages?.toJson()['${Preference.getString(Preference.LANGUAGE)}']['${Preference.getString(Preference.USER_LOGIN_TYPE_LOCALE)}-label'] ?? ""}',
                                    style: Styles.textBold(
                                        lineHeight: 1.3,
                                        color: ColorConstants.WHITE,
                                        size: 22),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 3),
                    Text(
                        '${dashboardViewResponse?.data?.messages?.toJson()["${Preference.getString(Preference.LANGUAGE)}"][Preference.getString(Preference.USER_LOGIN_TYPE_LOCALE)] ?? ""}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: ColorConstants.ACCENT_COLOR,
                          fontSize: 12,
                          height: 2,
                        )).tr(),
                    SizedBox(height: 10),
                    Divider(
                      color: ColorConstants.GREY_10,
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.55,
                      child: LoginBottomSheet(
                        showEdulystLogo: true,
                      ),
                    )
                  ],
                ),
              ),
            );
          });
        });
  }
}

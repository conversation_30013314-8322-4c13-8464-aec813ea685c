import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:masterg/blocs/auth_bloc.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import '../../blocs/home_bloc.dart';
import '../../data/api/api_service.dart';
import '../../utils/Log.dart';
import '../../utils/config.dart';
import '../custom_pages/ScreenWithLoader.dart';
import '../custom_pages/custom_widgets/CommonWebView.dart';

class ForgotPassword extends StatefulWidget {
  final bool beforeLogin;
  const ForgotPassword({
    Key? key,
    this.beforeLogin = true,
  }) : super(key: key);

  @override
  State<ForgotPassword> createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPassword> {
  bool isFill = false;
  TextEditingController emailController = TextEditingController();
  TextEditingController otpController = TextEditingController();
  TextEditingController newPassController = TextEditingController();
  TextEditingController confPassController = TextEditingController();
  TextEditingController currentPassController = TextEditingController();

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  GlobalKey<FormState> _changePasswordFormKey = GlobalKey<FormState>();
  bool isCodeSent = false;
  bool codeVerified = false;
  bool _isLoading = false;
  bool _emailTrue = false;
  bool _codeVerifiedTrue = false;
  int endTime = 0;
  bool currentPassObs = true;
  bool newPassObs = true;
  bool confirmPassObs = true;

  @override
  void initState() {
    super.initState();
    currentPassObs = true;
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// when password need to be changed [BeforeLogin]
  void fieldValidation() {
    if (!_formKey.currentState!.validate()) return;
    if (newPassController.text.isEmpty || confPassController.text.isEmpty) {
      Utility.showSnackBar(
          scaffoldContext: context, message: tr('enter_new_confirm_password'));
    } else {
      if (newPassController.text.toString().length > 7) {
        if (newPassController.text == confPassController.text) {
          /* Navigator.push(
              context,
              MaterialPageRoute(builder: (context)=> SelfDetailsPage(
                email: emailController.text.toString().trim(),
                password: confPassController.text.toString().trim(),
                loginWithEmail: true,
              )));*/
          passwordUpdate(emailController.text.toString().trim(),
              confPassController.text.toString().trim());
        } else {
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('password_notmatched'));
        }
      } else {
        Utility.showSnackBar(
            scaffoldContext: context, message: tr('security_password_length'));
      }
    }
  }

  /// when password need to be changed [AfterLogin]
  void changePassword() {
    if (!_changePasswordFormKey.currentState!.validate()) return;
    if (newPassController.text.isEmpty || confPassController.text.isEmpty) {
      Utility.showSnackBar(
          scaffoldContext: context, message: tr('enter_new_confirm_password'));
    } else {
      if (newPassController.text.toString().length > 7) {
        if (newPassController.text == confPassController.text) {
          Map<String, String> data = new Map<String, String>();
          data['email'] = Utility()
              .decrypted128('${Preference.getString(Preference.USER_EMAIL)}');
          data['current_password'] = currentPassController.value.text;
          data['password'] = confPassController.value.text;
          data['locale'] =
              Preference.getString(Preference.APP_ENGLISH_NAME).toString();

          // if (Preference.getBool(Preference.LOGGEDIN_WITH_EMAIL) == true) {
          if (confPassController.text == currentPassController.text) {
            Utility.showSnackBar(
                scaffoldContext: context,
                message: tr('current_new_pass_not_same'));
            return;
          }
          BlocProvider.of<AuthBloc>(context)
              .add(ChangePasswordEvent(data: data));
          // } else
          //   passwordUpdate(
          //       Utility().decrypted128(
          //           '${Preference.getString(Preference.USER_EMAIL)}'),
          //       confPassController.text.toString().trim());
        } else {
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('password_notmatched'));
        }
      } else {
        Utility.showSnackBar(
            scaffoldContext: context, message: tr('security_password_length'));
      }
    }
  }

  void sendEmailVerificationCode(String email) {
    BlocProvider.of<HomeBloc>(context)
        .add(EmailCodeSendEvent(email: email, isSignup: 0, forgotPass: 1));
  }

  void verifyOtp(String email, String otp) {
    BlocProvider.of<HomeBloc>(context)
        .add(VerifyEmailCodeEvent(email: email, code: otp));
  }

  void passwordUpdate(String email, String pass) {
    BlocProvider.of<HomeBloc>(context).add(PasswordUpdateEvent(
        email: email,
        pass: pass,
        locale: Preference.getString(Preference.APP_ENGLISH_NAME).toString()));
  }

  void _handleEmailCodeSendResponse(EmailCodeSendState state) {
    var emailCodeSendState = state;
    setState(() {
      switch (emailCodeSendState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          isCodeSent = true;
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('verify_code_on_email'));
          endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
          Log.v("EmailCodeSend Singh....................");
          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error emailCodeSendState..........................${emailCodeSendState.error}");
          if (emailCodeSendState.error == 'sis redirection') {
            Navigator.push(
                context,
                NextPageRoute(CommonWebView(
                  url: APK_DETAILS["forgot_cange_pass"],
                )));
          } else {
            Utility.showSnackBar(
                scaffoldContext: context, message: emailCodeSendState.error);
          }
          FirebaseAnalytics.instance
              .logEvent(name: 'language_page', parameters: {
            "Error": '${emailCodeSendState.error}',
          });
          _isLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleVerifyOtp(VerifyEmailCodeState state) {
    var emailCodeSendState = state;
    setState(() {
      switch (emailCodeSendState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          codeVerified = false;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "VerifyOtp Suuuuuuuus....................${emailCodeSendState.state}");
          codeVerified = true;
          _codeVerifiedTrue = true;
          _isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error handleVerifyOtp ..........................${emailCodeSendState.error}");
          _isLoading = false;
          codeVerified = false;

          if (emailCodeSendState.error == 'sis_redirection') {
            Navigator.push(
                context,
                NextPageRoute(CommonWebView(
                  url: APK_DETAILS["forgot_cange_pass"],
                )));
          } else {
            Utility.showSnackBar(
                scaffoldContext: context, message: tr("invalid_code"));
            FirebaseAnalytics.instance
                .logEvent(name: 'language_page', parameters: {
              "Error": '${emailCodeSendState.error}',
            });
          }

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handlePasswordUpdate(PasswordUpdateState state) {
    var emailCodeSendState = state;
    setState(() {
      switch (emailCodeSendState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("VerifyOtp Suuuuuuuus....................");
          // int? lanuageId = Preference.getInt(Preference.APP_LANGUAGE) ?? 1;
          // String? appEnglishName = Preference.getString(Preference.APP_ENGLISH_NAME) ?? 'en';
          // Preference.clearPref().then((value) async {
          //   Preference.setInt(Preference.APP_LANGUAGE, lanuageId);
          //   Preference.setString(Preference.APP_ENGLISH_NAME, appEnglishName);
          //   // Navigator.pushAndRemoveUntil(
          //   //     context,
          //   //     NextPageRoute(NewSignUpScreen(
          //   //       showEdulystLogo: true,
          //   //     )),
          //   //     (route) => false);
          // });
          Navigator.of(context).pop();

          Utility.showSnackBar(
              scaffoldContext: context,
              message: tr('password_updated_successfully'));
          _isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error handleVerifyOtp ..........................${emailCodeSendState.error}");
          widget.beforeLogin == true
              ? Utility.showSnackBar(
                  scaffoldContext: context,
                  message: tr('password_update_error'))
              : Utility.showSnackBar(
                  scaffoldContext: context, message: tr('set_email_msg'));
          _isLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'language_page', parameters: {
            "Error": '${emailCodeSendState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleChangePassword(ChangePasswordState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Change Password Success....................");
          // int? lanuageId = Preference.getInt(Preference.APP_LANGUAGE) ?? 1;
          /*String? appEnglishName = Preference.getString(Preference.APP_ENGLISH_NAME) ?? 'en';
          Preference.clearPref().then((value) async {
            Preference.setInt(Preference.APP_LANGUAGE, lanuageId);
            Preference.setString(Preference.APP_ENGLISH_NAME, appEnglishName);
            // Navigator.pushAndRemoveUntil(
            //     context,
            //     NextPageRoute(NewSignUpScreen(showEdulystLogo: true)),
            //     (route) => false);
          });*/
          Navigator.of(context).pop();

          Utility.showSnackBar(
              scaffoldContext: context,
              message: '${state.response?.data?.message}');
          _isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Change Password ..........................${state.response?.toJson()}");
          Utility.showSnackBar(
              scaffoldContext: context,
              message:
                  '${state.response?.error?.first ?? 'Something went wrong'}');
          _isLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'language_page', parameters: {
            "Error": '${state.response?.error?.first}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      // initState: (BuildContext context) {},
      listeners: [
        BlocListener<HomeBloc, HomeState>(
          listener: (context, state) async {
            if (state is EmailCodeSendState) {
              _handleEmailCodeSendResponse(state);
            }
            if (state is VerifyEmailCodeState) {
              _handleVerifyOtp(state);
            }
            if (state is PasswordUpdateState) {
              _handlePasswordUpdate(state);
            }
          },
        ),
        BlocListener<AuthBloc, AuthState>(
          listener: (context, state) async {
            if (state is ChangePasswordState) _handleChangePassword(state);
            //PasswordUpdateState
          },
        ),
      ],
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.transparent,
          title: Text(
            widget.beforeLogin ? 'forgot_password' : 'change_password',
            style: Styles.semibold(color: ColorConstants.BLACK, size: 16),
          ).tr(),
          leading: InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Icon(
              Utility().isRTL(context)
                  ? Icons.arrow_back_ios_sharp
                  : Icons.arrow_back_ios_new_outlined,
              color: ColorConstants.BLACK,
            ),
          ),
          elevation: 0.0,
        ),
        body: SafeArea(
            child: ScreenWithLoader(
          isLoading: _isLoading,
          body: Container(
            //height: height(context) * 0.9,
            child: widget.beforeLogin
                ? Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          ///Email Field--
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(right: 20.0, top: 20.0)
                                : EdgeInsets.only(left: 20.0, top: 20.0),
                            child: Row(
                              children: [
                                ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            ColorConstants().gradientLeft(),
                                            ColorConstants().gradientRight()
                                          ]).createShader(bounds);
                                    },
                                    child: Icon(
                                      Icons.email_outlined,
                                      size: 20,
                                    )),
                                SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  "email",
                                  style: Styles.textRegular(),
                                ).tr(),
                              ],
                            ),
                          ),
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(top: 8.0, right: 16, left: 16)
                                : EdgeInsets.only(
                                    top: 8.0, left: 16, right: 16),
                            child: Container(
                              height: height(context) * 0.1,
                              child: TextFormField(
                                cursorColor: ColorConstants().gradientRight(),
                                autofocus: true,
                                // focusNode: phoneFocus,
                                controller: emailController,
                                // keyboardType: TextInputType.number,
                                style: Styles.regular(
                                  color: Color(0xff0E1638),
                                  size: 14,
                                ),
                                // inputFormatters: <TextInputFormatter>[
                                //   FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                                // ],

                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                      width: 1.5,
                                    ),
                                  ),
                                  suffix: isCodeSent
                                      ? Container(
                                          padding: EdgeInsets.all(4),
                                          decoration: BoxDecoration(
                                            color: ColorConstants.GREEN,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.done,
                                            size: 12,
                                            color: Colors.white,
                                          ),
                                        )
                                      : GestureDetector(
                                          onTap: () {
                                            codeVerified = false;
                                            _codeVerifiedTrue = false;
                                            otpController.clear();
                                            newPassController.clear();
                                            confPassController.clear();
                                            if (emailController
                                                .value.text.isEmpty) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(SnackBar(
                                                content: Text('plz_enter_email')
                                                    .tr(),
                                              ));
                                            } else if (_emailTrue == false) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(SnackBar(
                                                content:
                                                    Text('enter_valid_email')
                                                        .tr(),
                                              ));
                                            } else {
                                              sendEmailVerificationCode(
                                                  emailController.value.text);
                                            }
                                          },
                                          child: GradientText(
                                            '${tr('verify_email')}',
                                            style: Styles.regular(size: 14),
                                            colors: _emailTrue == true
                                                ? [
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight()
                                                  ]
                                                : [
                                                    ColorConstants
                                                        .UNSELECTED_BUTTON,
                                                    ColorConstants
                                                        .UNSELECTED_BUTTON
                                                  ],
                                          ),
                                          /*child:Text("Send Code", style: TextStyle(color:
                                  isCodeSent == true ? Colors.red: null),),*/
                                        ),
                                  hintText: '<EMAIL>',
                                  hintStyle: TextStyle(
                                    color: Color(0xffE5E5E5),
                                  ),
                                  isDense: true,
                                  prefixIconConstraints:
                                      BoxConstraints(minWidth: 0, minHeight: 0),
                                  border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1,
                                          color: ColorConstants.WHITE),
                                      borderRadius: BorderRadius.circular(10)),
                                  helperStyle: Styles.regular(
                                      size: 14,
                                      color: ColorConstants.GREY_3
                                          .withValues(alpha: 0.1)),
                                  counterText: "",
                                ),
                                onChanged: (value) {
                                  setState(() {
                                    if (!RegExp(
                                            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                        .hasMatch(value)) {
                                      _emailTrue = false;
                                      isCodeSent = false;
                                      otpController.clear();
                                      newPassController.clear();
                                      confPassController.clear();
                                    } else {
                                      _emailTrue = true;
                                      otpController.clear();
                                      newPassController.clear();
                                      confPassController.clear();
                                    }
                                  });
                                },
                                validator: (value) {
                                  if (value == '') return tr('email_required');
                                  int index = value?.length as int;

                                  if (value![index - 1] == '.')
                                    return tr('email_address_error');

                                  if (!RegExp(
                                          r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                      .hasMatch(value))
                                    return tr('email_address_error');

                                  isCodeSent = true;
                                  return null;
                                },
                              ),
                            ),
                          ),

                          ///Re-Send Code
                          isCodeSent == true
                              ? Row(
                                  children: [
                                    Expanded(
                                      child: SizedBox(),
                                    ),
                                    CountdownTimer(
                                      endTime: endTime,
                                      widgetBuilder:
                                          (_, CurrentRemainingTime? time) {
                                        return Padding(
                                          padding: Utility().isRTL(context)
                                              ? EdgeInsets.only(
                                                  left: 20.0, bottom: 15.0)
                                              : EdgeInsets.only(
                                                  right: 20.0, bottom: 15.0),
                                          child: RichText(
                                            text: TextSpan(
                                                text: '',
                                                style: TextStyle(
                                                  fontSize: 3,
                                                ),
                                                children: <TextSpan>[
                                                  time == null
                                                      ? TextSpan(
                                                          text:
                                                              tr('resend_code'),
                                                          recognizer:
                                                              TapGestureRecognizer()
                                                                ..onTap = () {
                                                                  _emailTrue =
                                                                      false;
                                                                  isCodeSent =
                                                                      false;
                                                                  codeVerified =
                                                                      false;
                                                                  _codeVerifiedTrue =
                                                                      false;
                                                                  otpController
                                                                      .clear();
                                                                  newPassController
                                                                      .clear();
                                                                  confPassController
                                                                      .clear();
                                                                  /*this.setState(() {
                                                    _emailTrue = false;
                                                    isCodeSent = false;
                                                  });*/
                                                                  sendEmailVerificationCode(
                                                                      emailController
                                                                          .value
                                                                          .text);
                                                                },
                                                          style: Styles.regular(
                                                              size: 12,
                                                              color:
                                                                  ColorConstants
                                                                      .BLACK))
                                                      : TextSpan(
                                                          text: tr(
                                                                  'resend_app_secs') +
                                                              ' ${time.sec} ' +
                                                              tr('secs'),
                                                          style: Styles.regular(
                                                              size: 12,
                                                              color:
                                                                  ColorConstants
                                                                      .BLACK)),
                                                ]),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                )
                              : SizedBox(),

                          ///enter email Code--
                          isCodeSent == true
                              ? Padding(
                                  padding: Utility().isRTL(context)
                                      ? EdgeInsets.only(right: 20.0)
                                      : EdgeInsets.only(left: 20.0),
                                  child: Row(
                                    children: [
                                      Text(
                                        'email_verification_code',
                                        style: Styles.textRegular(),
                                      ).tr(),
                                    ],
                                  ),
                                )
                              : SizedBox(),
                          isCodeSent == true
                              ? Padding(
                                  padding: EdgeInsets.only(
                                      top: 8.0, left: 16, right: 16),
                                  child: Container(
                                    height: height(context) * 0.1,
                                    child: Stack(
                                      children: [
                                        TextFormField(
                                          obscureText: false,
                                          keyboardType: TextInputType.number,
                                          cursorColor: codeVerified == false
                                              ? ColorConstants().gradientRight()
                                              : Colors.white,
                                          controller: otpController,
                                          style: Styles.otp(
                                              color: Colors.black,
                                              size: 14,
                                              letterSpacing: 40),
                                          maxLength: 4,
                                          onChanged: (value) {
                                            setState(() {
                                              if (value.length == 4) {
                                                codeVerified = true;
                                              } else {
                                                codeVerified = false;
                                              }
                                            });
                                          },
                                          decoration: InputDecoration(
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                                width: 1.5,
                                              ),
                                            ),
                                            fillColor: Color(0xffE5E5E5),
                                            hintText: '••••',
                                            hintStyle: TextStyle(
                                              color: Colors.black,
                                            ),
                                            isDense: true,
                                            prefixIconConstraints:
                                                BoxConstraints(
                                                    minWidth: 0, minHeight: 0),
                                            border: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    width: 1,
                                                    color:
                                                        ColorConstants.WHITE),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            helperStyle: Styles.regular(
                                                size: 14,
                                                color: ColorConstants.GREY_3
                                                    .withValues(alpha: 0.1)),
                                          ),
                                        ),
                                        Positioned(
                                          right: Utility().isRTL(context)
                                              ? null
                                              : 10,
                                          left: Utility().isRTL(context)
                                              ? 10
                                              : null,
                                          top: 14,
                                          //bottom: 14,
                                          child: _codeVerifiedTrue
                                              ? Container(
                                                  padding: EdgeInsets.all(4),
                                                  decoration: BoxDecoration(
                                                    color: ColorConstants.GREEN,
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.done,
                                                    size: 12,
                                                    color: Colors.white,
                                                  ),
                                                )
                                              : GestureDetector(
                                                  onTap: () {
                                                    if (otpController
                                                        .value.text.isEmpty) {
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                              SnackBar(
                                                        content: Text(
                                                                'enter_verification_code')
                                                            .tr(),
                                                      ));
                                                    } else if (codeVerified ==
                                                        false) {
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                              SnackBar(
                                                        content: Text(
                                                                'valid_four_digit_code')
                                                            .tr(),
                                                      ));
                                                    } else {
                                                      verifyOtp(
                                                          emailController
                                                              .value.text,
                                                          otpController
                                                              .value.text);
                                                    }
                                                  },
                                                  child: GradientText(
                                                    tr('verify_otp'),
                                                    style: Styles.regular(
                                                        size: 14),
                                                    colors: codeVerified == true
                                                        ? [
                                                            ColorConstants()
                                                                .gradientLeft(),
                                                            ColorConstants()
                                                                .gradientRight(),
                                                          ]
                                                        : [
                                                            ColorConstants
                                                                .UNSELECTED_BUTTON,
                                                            ColorConstants
                                                                .UNSELECTED_BUTTON,
                                                          ],
                                                  ),
                                                ),
                                        )
                                      ],
                                    ),
                                  ),
                                )
                              : SizedBox(),

                          ///Password
                          _codeVerifiedTrue == true
                              ? Column(
                                  children: [
                                    Padding(
                                      padding: Utility().isRTL(context)
                                          ? EdgeInsets.only(right: 20.0)
                                          : EdgeInsets.only(left: 20.0),
                                      child: Row(
                                        children: [
                                          ShaderMask(
                                            blendMode: BlendMode.srcIn,
                                            shaderCallback: (Rect bounds) {
                                              return LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: <Color>[
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight()
                                                  ]).createShader(bounds);
                                            },
                                            //child: Image.asset('assets/images/lock.png'),
                                            child: Icon(
                                              Icons.lock_outline,
                                              size: 20,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 8,
                                          ),
                                          Text(
                                            'new_password',
                                            style: Styles.textRegular(),
                                          ).tr(),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: Utility().isRTL(context)
                                          ? EdgeInsets.only(
                                              top: 8.0, right: 16, left: 16)
                                          : EdgeInsets.only(
                                              top: 8.0, left: 16, right: 16),
                                      child: Container(
                                        height: height(context) * 0.1,
                                        child: TextField(
                                          obscureText: true,
                                          cursorColor:
                                              ColorConstants().gradientRight(),
                                          autofocus: false,
                                          // focusNode: phoneFocus,
                                          controller: newPassController,
                                          // keyboardType: TextInputType.number,
                                          style: Styles.bold(
                                            color: Colors.black,
                                            size: 14,
                                          ),
                                          // inputFormatters: <TextInputFormatter>[
                                          //   FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                                          // ],
                                          maxLength: 20,
                                          decoration: InputDecoration(
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                                width: 1.5,
                                              ),
                                            ),
                                            fillColor: Color(0xffE5E5E5),
                                            hintText: tr('enter_new_password'),

                                            hintStyle: TextStyle(
                                              color: Color(0xffE5E5E5),
                                            ),
                                            isDense: true,
                                            prefixIconConstraints:
                                                BoxConstraints(
                                                    minWidth: 0, minHeight: 0),

                                            border: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    width: 1,
                                                    color:
                                                        ColorConstants.WHITE),
                                                borderRadius:
                                                    BorderRadius.circular(10)),

                                            helperStyle: Styles.regular(
                                                size: 14,
                                                color: ColorConstants.GREY_3
                                                    .withValues(alpha: 0.1)),
                                            counterText: "",
                                            // enabledBorder: UnderlineInputBorder(
                                            //   borderSide: BorderSide(
                                            //       color: ColorConstants.WHITE, width: 1.5),
                                            // ),
                                          ),
                                          // onChanged: (value) {
                                          //   setState(() {});
                                          // },
                                          // validator: (value) {
                                          //   if (value == null) return 'Enter phone number';
                                          //   if (value.length != 10) {
                                          //     return "Enter valid phone number.";
                                          //   }
                                          //   return null;
                                          // },
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: Utility().isRTL(context)
                                          ? EdgeInsets.only(right: 20.0)
                                          : EdgeInsets.only(left: 20.0),
                                      child: Row(
                                        children: [
                                          ShaderMask(
                                            blendMode: BlendMode.srcIn,
                                            shaderCallback: (Rect bounds) {
                                              return LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: <Color>[
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight()
                                                  ]).createShader(bounds);
                                            },
                                            //child: Image.asset('assets/images/lock.png'),
                                            child: Icon(
                                              Icons.lock_outline,
                                              size: 20,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 8,
                                          ),
                                          Text(
                                            'confirm_pwd',
                                            style: Styles.textRegular(),
                                          ).tr(),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 8.0, left: 16, right: 16),
                                      child: Container(
                                        //height: height(context) * 0.3,
                                        child: TextField(
                                          obscureText: true,
                                          cursorColor:
                                              ColorConstants().gradientRight(),
                                          autofocus: false,
                                          // focusNode: phoneFocus,
                                          controller: confPassController,
                                          // keyboardType: TextInputType.number,
                                          style: Styles.bold(
                                            color: Colors.black,
                                            size: 14,
                                          ),
                                          // inputFormatters: <TextInputFormatter>[
                                          //   FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                                          // ],
                                          maxLength: 20,
                                          decoration: InputDecoration(
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                                width: 1.5,
                                              ),
                                            ),
                                            fillColor: Color(0xffE5E5E5),
                                            hintText: tr(
                                                'please_enter_confirm_password'),
                                            hintStyle: TextStyle(
                                              color: Color(0xffE5E5E5),
                                            ),
                                            isDense: true,
                                            prefixIconConstraints:
                                                BoxConstraints(
                                                    minWidth: 0, minHeight: 0),
                                            border: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    width: 1,
                                                    color:
                                                        ColorConstants.WHITE),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            helperStyle: Styles.regular(
                                                size: 14,
                                                color: ColorConstants.GREY_3
                                                    .withValues(alpha: 0.1)),
                                            counterText: "",
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : SizedBox(),

                          _codeVerifiedTrue == true
                              ? Padding(
                                  padding: Utility().isRTL(context)
                                      ? EdgeInsets.only(
                                          right: 18.0, left: 18.0, bottom: 18.0)
                                      : EdgeInsets.only(
                                          left: 18.0,
                                          right: 18.0,
                                          bottom: 18.0),
                                  child: Container(
                                    height: height(context) * 0.28,
                                    child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Container(
                                            height: height(context) * 0.06,
                                            decoration: BoxDecoration(
                                              //color: Color(0xffe9e9e9),
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              gradient:
                                                  emailController.value.text !=
                                                              '' &&
                                                          newPassController
                                                                  .value.text !=
                                                              ''
                                                      ? LinearGradient(colors: [
                                                          ColorConstants()
                                                              .gradientLeft(),
                                                          ColorConstants()
                                                              .gradientRight(),
                                                        ])
                                                      : LinearGradient(colors: [
                                                          ColorConstants
                                                              .UNSELECTED_BUTTON,
                                                          ColorConstants
                                                              .UNSELECTED_BUTTON,
                                                        ]),
                                            ),
                                            child: InkWell(
                                              onTap: () {
                                                fieldValidation();
                                              },
                                              child: Center(
                                                child: Text(
                                                  'back_tologin',
                                                  style: Styles.regular(
                                                    size: 16,
                                                    color: ColorConstants.WHITE,
                                                  ),
                                                ).tr(),
                                              ),
                                            ),
                                          ),
                                        ]),
                                  ),
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  )
                : Form(
                    key: _changePasswordFormKey,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Padding(
                          //   padding: Utility().isRTL(context)
                          //       ? EdgeInsets.only(right: 20.0, top: 10.0)
                          //       : EdgeInsets.only(left: 20.0, top: 10.0),
                          //   child: Text(
                          //       'Change password for email: ${Utility().decrypted128('${Preference.getString(Preference.USER_EMAIL)}')}'),
                          // ),
                          // if (Preference.getBool(
                          //         Preference.LOGGEDIN_WITH_EMAIL) ==
                          //     true) ...[
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(right: 20.0, top: 20.0)
                                : EdgeInsets.only(left: 20.0, top: 20.0),
                            child: Row(
                              children: [
                                ShaderMask(
                                  blendMode: BlendMode.srcIn,
                                  shaderCallback: (Rect bounds) {
                                    return LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: <Color>[
                                          ColorConstants().gradientLeft(),
                                          ColorConstants().gradientRight()
                                        ]).createShader(bounds);
                                  },
                                  child: Icon(
                                    Icons.lock_outline,
                                    size: 20,
                                  ),
                                ),
                                SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  "current_password",
                                  style: Styles.textRegular(),
                                ).tr(),
                              ],
                            ),
                          ),
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(top: 8.0, right: 16, left: 16)
                                : EdgeInsets.only(
                                    top: 8.0, left: 16, right: 16),
                            child: Container(
                              height: height(context) * 0.1,
                              child: TextFormField(
                                obscureText: currentPassObs,
                                cursorColor: ColorConstants().gradientRight(),
                                autofocus: true,
                                controller: currentPassController,
                                style: Styles.bold(
                                  color: Color(0xff0E1638),
                                  size: 14,
                                ),
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                      width: 1.5,
                                    ),
                                  ),
                                  hintText: tr('current_password'),
                                  suffixIcon: TapWidget(
                                    onTap: () {
                                      setState(() {
                                        currentPassObs = !currentPassObs;
                                      });
                                    },
                                    child: Padding(
                                        padding:
                                            const EdgeInsets.only(right: 5),
                                        child: !currentPassObs
                                            ? Icon(
                                                Icons.remove_red_eye_outlined,
                                                color: ColorConstants.BLACK,
                                              )
                                            : Icon(
                                                Icons.visibility_off,
                                                color: ColorConstants.BLACK,
                                              )),
                                  ),
                                  hintStyle: TextStyle(
                                    color: Color(0xffE5E5E5),
                                  ),
                                  isDense: true,
                                  prefixIconConstraints:
                                      BoxConstraints(minWidth: 0, minHeight: 0),
                                  border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1,
                                          color: ColorConstants.WHITE),
                                      borderRadius: BorderRadius.circular(10)),
                                  helperStyle: Styles.regular(
                                      size: 14,
                                      color: ColorConstants.GREY_3
                                          .withValues(alpha: 0.1)),
                                  counterText: "",
                                ),
                                validator: (value) {
                                  if (value == null || value == '')
                                    return tr('please_enter_current_password');
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                            ),
                          ),
                          // ],
                          Column(
                            children: [
                              Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(right: 20.0)
                                    : EdgeInsets.only(left: 20.0),
                                child: Row(
                                  children: [
                                    ShaderMask(
                                      blendMode: BlendMode.srcIn,
                                      shaderCallback: (Rect bounds) {
                                        return LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: <Color>[
                                              ColorConstants().gradientLeft(),
                                              ColorConstants().gradientRight()
                                            ]).createShader(bounds);
                                      },
                                      //child: Image.asset('assets/images/lock.png'),
                                      child: Icon(
                                        Icons.lock_outline,
                                        size: 20,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 8,
                                    ),
                                    Text(
                                      'new_password',
                                      style: Styles.textRegular(),
                                    ).tr(),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(
                                        top: 8.0, right: 16, left: 16)
                                    : EdgeInsets.only(
                                        top: 8.0, left: 16, right: 16),
                                child: Container(
                                  height: height(context) * 0.1,
                                  child: TextField(
                                    obscureText: newPassObs,
                                    cursorColor:
                                        ColorConstants().gradientRight(),
                                    autofocus: false,
                                    controller: newPassController,
                                    style: Styles.bold(
                                      color: Colors.black,
                                      size: 14,
                                    ),
                                    onChanged: (value) => setState(() {}),
                                    maxLength: 20,
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                        borderSide: BorderSide(
                                          color: Color(0xffE5E5E5),
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                        borderSide: BorderSide(
                                          color: Color(0xffE5E5E5),
                                          width: 1.5,
                                        ),
                                      ),
                                      fillColor: Color(0xffE5E5E5),
                                      hintText: tr('enter_new_password'),
                                      suffixIcon: TapWidget(
                                        onTap: () {
                                          setState(() {
                                            newPassObs = !newPassObs;
                                          });
                                        },
                                        child: Padding(
                                            padding:
                                                const EdgeInsets.only(right: 5),
                                            child: !newPassObs
                                                ? Icon(
                                                    Icons
                                                        .remove_red_eye_outlined,
                                                    color: ColorConstants.BLACK,
                                                  )
                                                : Icon(
                                                    Icons.visibility_off,
                                                    color: ColorConstants.BLACK,
                                                  )),
                                      ),
                                      hintStyle: TextStyle(
                                        color: Color(0xffE5E5E5),
                                      ),
                                      isDense: true,
                                      prefixIconConstraints: BoxConstraints(
                                          minWidth: 0, minHeight: 0),
                                      border: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 1,
                                              color: ColorConstants.WHITE),
                                          borderRadius:
                                              BorderRadius.circular(10)),
                                      helperStyle: Styles.regular(
                                          size: 14,
                                          color: ColorConstants.GREY_3
                                              .withValues(alpha: 0.1)),
                                      counterText: "",
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(right: 20.0)
                                    : EdgeInsets.only(left: 20.0),
                                child: Row(
                                  children: [
                                    ShaderMask(
                                      blendMode: BlendMode.srcIn,
                                      shaderCallback: (Rect bounds) {
                                        return LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: <Color>[
                                              ColorConstants().gradientLeft(),
                                              ColorConstants().gradientRight()
                                            ]).createShader(bounds);
                                      },
                                      //child: Image.asset('assets/images/lock.png'),
                                      child: Icon(
                                        Icons.lock_outline,
                                        size: 20,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 8,
                                    ),
                                    Text(
                                      'confirm_pwd',
                                      style: Styles.textRegular(),
                                    ).tr(),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 8.0, left: 16, right: 16),
                                child: Container(
                                  child: TextField(
                                    obscureText: confirmPassObs,
                                    cursorColor:
                                        ColorConstants().gradientRight(),
                                    autofocus: false,
                                    controller: confPassController,
                                    style: Styles.bold(
                                      color: Colors.black,
                                      size: 14,
                                    ),
                                    onChanged: (value) => setState(() {}),
                                    maxLength: 20,
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                        borderSide: BorderSide(
                                          color: Color(0xffE5E5E5),
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                        borderSide: BorderSide(
                                          color: Color(0xffE5E5E5),
                                          width: 1.5,
                                        ),
                                      ),
                                      fillColor: Color(0xffE5E5E5),
                                      suffixIcon: TapWidget(
                                        onTap: () {
                                          setState(() {
                                            confirmPassObs = !confirmPassObs;
                                          });
                                        },
                                        child: Padding(
                                            padding:
                                                const EdgeInsets.only(right: 5),
                                            child: confirmPassObs
                                                ? Icon(
                                                    Icons.visibility_off,
                                                    color: ColorConstants.BLACK,
                                                  )
                                                : Icon(
                                                    Icons
                                                        .remove_red_eye_outlined,
                                                    color:
                                                        ColorConstants.BLACK)),
                                      ),
                                      hintText:
                                          tr('please_enter_confirm_password'),
                                      hintStyle: TextStyle(
                                        color: Color(0xffE5E5E5),
                                      ),
                                      isDense: true,
                                      prefixIconConstraints: BoxConstraints(
                                          minWidth: 0, minHeight: 0),
                                      border: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 1,
                                              color: ColorConstants.WHITE),
                                          borderRadius:
                                              BorderRadius.circular(10)),
                                      helperStyle: Styles.regular(
                                          size: 14,
                                          color: ColorConstants.GREY_3
                                              .withValues(alpha: 0.1)),
                                      counterText: "",
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(
                                    right: 18.0, left: 18.0, bottom: 18.0)
                                : EdgeInsets.only(
                                    left: 18.0, right: 18.0, bottom: 18.0),
                            child: Container(
                              height: height(context) * 0.28,
                              child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Container(
                                      height: height(context) * 0.06,
                                      decoration: BoxDecoration(
                                        //color: Color(0xffe9e9e9),
                                        borderRadius: BorderRadius.circular(10),
                                        gradient: newPassController
                                                        .value.text !=
                                                    '' &&
                                                confPassController.value.text !=
                                                    ''
                                            ? LinearGradient(colors: [
                                                ColorConstants().gradientLeft(),
                                                ColorConstants()
                                                    .gradientRight(),
                                              ])
                                            : LinearGradient(colors: [
                                                ColorConstants
                                                    .UNSELECTED_BUTTON,
                                                ColorConstants
                                                    .UNSELECTED_BUTTON,
                                              ]),
                                      ),
                                      child: InkWell(
                                        onTap: () {
                                          changePassword();
                                        },
                                        child: Center(
                                          child: Text(
                                            'save',
                                            style: Styles.regular(
                                              size: 16,
                                              color: ColorConstants.WHITE,
                                            ),
                                          ).tr(),
                                        ),
                                      ),
                                    ),
                                  ]),
                            ),
                          )
                        ],
                      ),
                    )),
          ),
        )),
      ),
    );
  }
}

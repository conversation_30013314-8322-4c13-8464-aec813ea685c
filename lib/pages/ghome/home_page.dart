import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/providers/video_player_provider.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/analytics_pages/analytic_page.dart';
import 'package:masterg/pages/gcarvaan/post/gcarvaan_post_page.dart';
import 'package:masterg/pages/ghome/g_school.dart';
import 'package:masterg/pages/ghome/ghome.dart';
import 'package:masterg/pages/learn_console/learn_consol_dashboard.dart';
import 'package:masterg/pages/reels/reel_screen.dart';
import 'package:masterg/pages/singularis/community/commiunity_dashboard.dart';
import 'package:masterg/pages/singularis/competition/competition.dart';
import 'package:masterg/pages/singularis/dashboard/dashboard.dart';
import 'package:masterg/pages/singularis/job/job_dashboard_page.dart';
import 'package:masterg/pages/singularis_world_of_wow/singularis_wow_dashboard.dart';
import 'package:masterg/routes/app_link_route.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
//import 'package:uni_links/uni_links.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../bots/set_goal_bot/singh_bot/singh_bot_page.dart';
import '../../routes/notification_route.dart';
import '../../utils/Log.dart';
import '../custom_pages/custom_widgets/NextPageRouting.dart';
import '../ebook/pages/dashboard.dart';
import '../event/event_console_page.dart';
import '../learning_console/time_table_page.dart';

const String homeTour = ' homeTour';

class homePage extends StatefulWidget {
  final index;
  List<MultipartFile>? fileToUpload;
  bool? isReelsPost;
  String? desc;
  List<String?>? filesPath;
  bool isFromCreatePost;
  List<Menu>? bottomMenu;
  Function? onLoad;
  final Widget? routeWidget;

  homePage(
      {Key? key,
      this.index = 0,
      this.fileToUpload,
      this.isReelsPost,
      this.desc,
      this.filesPath,
      this.bottomMenu,
      this.isFromCreatePost = false,
      this.onLoad,
      this.routeWidget})
      : super(key: key);

  @override
  _homePageState createState() => _homePageState();
}

class _homePageState extends State<homePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  // var currentIndex = 0;
  String? profileImage = '';

  final Shader linearGradient = const LinearGradient(
    colors: <Color>[Color(0xffDA44bb), Color(0xff8921aa)],
  ).createShader(const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0));

  // SpeechToText _speechToText = SpeechToText();
  // bool _speechEnabled = false;
  // String _lastWords = '';
  MenuListProvider? mp;

  void handleAppLink() async {
    //final initialLink = await getInitialLink();
    final initialLink = await AppLinks();
    AppLinkRoute.handleRoute(route: initialLink.toString());
  }

  @override
  initState() {
    super.initState();
    handleAppLink();

    // FeatureDiscovery.discoverFeatures(
    //   context,
    //   <String>{homeTour},
    // );

    if (widget.routeWidget != null) {
      Future.delayed(Duration(microseconds: 500))
          .then((value) => WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.push(context, NextPageRoute(widget.routeWidget!));
              }));
    }
    if (widget.routeWidget == null)
      FirebaseMessaging.instance.getInitialMessage().then((value) {
        if (value!.data.isNotEmpty) {
          NotificationRoute().open(value.data);
        } else {
          Log.v("Notification value is else singh ---");
        }
      });
    // if (widget.onLoad != null) {

    // }
    // FirebaseDynamicLinks.instance.onLink.listen((dynamicLinkData) {
    //   String queryString = '';
    //   switch (queryString.split('/').first) {
    //     case 'assessment':
    //       Navigator.push(
    //           context,
    //           NextPageRoute(ChangeNotifierProvider<MgAssessmentDetailProvider>(
    //               create: (context) => MgAssessmentDetailProvider(
    //                   TrainingService(ApiService()),
    //                   AssessmentList(
    //                       contentId: int.parse(queryString.split('/').last))),
    //               child: MgAssessmentDetailPage(
    //                 programName: '',
    //               ))));

    //       break;

    //     case 'user_profile':
    //       Navigator.push(
    //           context,
    //           NextPageRoute(NewPortfolioPage(
    //             userId: int.parse(queryString.split('/').last),
    //           )));

    //       break;

    //     case 'forgot_password':
    //       Navigator.push(context, NextPageRoute(ForgotPassword()));

    //       break;
    //     default:
    //   }
    // }).onError((error) {
    // });
    profileImage = Preference.getString(Preference.PROFILE_IMAGE);
  }

  void refreshData() {
    profileImage = Preference.getString(Preference.PROFILE_IMAGE);
  }

  onGoBack(dynamic value) {
    setState(() {
      profileImage = Preference.getString(Preference.PROFILE_IMAGE);
    });
  }

  DateTime? currentBackPressTime;

  // Timer? _commandExecutionTimer;
  // final Duration _commandExecutionDelay = Duration(seconds: 2);
  int countCall = 0;

  /// Each time to start a speech recognition session
  // void _startListening() async {
  //   // _speechEnabled = true;
  //   try {
  //     await _speechToText.listen(
  //         listenFor: Duration(minutes: 10),
  //         onResult: (SpeechRecognitionResult result) {
  //           countCall++;
  //           _commandExecutionTimer = Timer(_commandExecutionDelay, () {
  //             countCall--;
  //             String cmd = '';
  //             try {
  //               cmd = result.recognizedWords.substring(_lastWords.length);
  //             } catch (e) {
  //               setState(() {
  //                 _lastWords = result.recognizedWords;
  //               });
  //             }
  //             print("result $cmd");
  //             if (countCall == 0) {
  //               setState(() {
  //                 _lastWords = result.recognizedWords;
  //               });
  //               if (cmd.toLowerCase().contains('go back')) {
  //                 Navigator.pop(context);
  //               } else if (cmd.toLowerCase().contains('change password')) {
  //                 Navigator.push(
  //                     context,
  //                     MaterialPageRoute(
  //                         builder: (context) => ForgotPassword(
  //                               beforeLogin: false,
  //                             )));
  //               } else if (cmd.toLowerCase().contains('portfolio')) {
  //                 Navigator.push(
  //                     context,
  //                     MaterialPageRoute(
  //                         builder: (context) => NewPortfolioPage()));
  //               } else if (cmd.toLowerCase().contains('competition')) {
  //                 mp?.updateCurrentIndex('/g-competitions');
  //               } else if (cmd.toLowerCase().contains('jobs') ||
  //                   cmd.toLowerCase().contains('carrier')) {
  //                 mp?.updateCurrentIndex('/g-jobs');
  //               } else {
  //                 setState(() {
  //                   _lastWords = result.recognizedWords;
  //                 });
  //               }
  //               countCall = 0;
  //               _startListening();
  //             }
  //           });
  //         });
  //   } catch (e) {
  //     print("something went wrong $e");
  //   }
  //   setState(() {});
  // }

  @override
  Widget build(BuildContext context) {
    _getDrawerLayout(BuildContext context) {
      return Drawer(
          child: SingleChildScrollView(
        child: Container(
          width: MediaQuery.of(context).size.width - 50,
          padding: EdgeInsets.only(left: 10, right: 10, top: 20, bottom: 40),
          color: ColorConstants.WHITE,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListTile(
                contentPadding: EdgeInsets.symmetric(horizontal: 0),
                leading: CircleAvatar(
                    backgroundImage:
                        NetworkImage(UserSession.userImageUrl ?? ""),
                    radius: 20.0),
                title: Text(
                  UserSession.userName ?? '',
                  style: Styles.textExtraBold(
                      color: (ColorConstants.PRIMARY_COLOR)),
                ),
                subtitle: Text(
                  UserSession.email ?? '',
                  style: Styles.textExtraBold(size: 12),
                ),
                trailing: Icon(
                  Icons.navigate_next,
                  color: ColorConstants.PRIMARY_COLOR,
                ),
                onTap: () {},
              ),
            ],
          ),
        ),
      ));
    }

    var pages = {
      '/g-home': GHome(),
      '/g-dashboard':
          APK_DETAILS['package_name'] == 'com.singularis.mescdigilibrary'
              ? Home()
              : const DashboardPage(),
      // : Competetion(),
      '/g-competitions': Competetion(),
      //'/g-jobs': SingualrisWowDashboard(),
      '/g-jobs': APK_DETAILS["package_name"] == "com.singulariswow.mec" ||
              APK_DETAILS["package_name"] == "com.singularis.jumeira" ||
              APK_DETAILS["package_name"] == "com.singulariswow"
          ? SingualrisWowDashboard()
          : JobDashboardPage(),

      '/g-school': const GSchool(),
      //'/g-reels': ReelsDashboardPage(),
      '/g-reels': ReelScreen(),
      '/g-carvaan': CommunityDashboard(),
      '/analytics': AnalyticPage(
        isViewAll: true,
        drawerWidget: _getDrawerLayout(context),
      ),
      '/faculty-batch-detail': LearnConsolDashboard(),
      '/time-table-webview': TimeTablePage(),
      '/event_console': EventConsolePage(),
    };

    var iconUnSelected = {
      '/g-dashboard': 'assets/images/un_dashboard.svg',
      '/g-competitions': 'assets/images/participate.svg',
      //'/g-jobs': 'assets/images/unselected_wow.svg',
      '/g-jobs': APK_DETAILS["package_name"] == "com.singulariswow.mec" ||
              APK_DETAILS["package_name"] == "com.singularis.jumeira" ||
              APK_DETAILS["package_name"] == "com.singulariswow"
          ? 'assets/images/unselected_wow.svg'
          : 'assets/images/un_careers.svg',
      '/g-home': 'assets/images/un_community.svg',
      '/g-school': 'assets/images/un_learn.svg',
      '/g-reels': 'assets/images/un_trends.svg',
      '/g-carvaan': 'assets/images/un_community.svg',
      '/sic-council': 'assets/images/my_council.svg',
      '/training': 'assets/images/trainings.svg',
      '/announcements': 'assets/images/announcements.svg',
      '/analytics': 'assets/images/analytics.svg',
      '/library': 'assets/images/library.svg',
      '/my-space-settings': 'assets/images/mySpaceSettings.svg',
      '/faculty-batch-detail': 'assets/images/learning_console.svg',
      '/time-table-webview': 'assets/images/analytics.svg',
      '/event_console': 'assets/images/un_event.svg'
    };

    var iconSelected = {
      '/g-dashboard': 'assets/images/s_dashboards.svg',
      '/g-competitions': 'assets/images/participate.svg',
      //'/g-jobs': 'assets/images/selected_wow.svg',
      '/g-jobs': APK_DETAILS["package_name"] == "com.singulariswow.mec" ||
              APK_DETAILS["package_name"] == "com.singularis.jumeira" ||
              APK_DETAILS["package_name"] == "com.singulariswow"
          ? 'assets/images/selected_wow.svg'
          : 'assets/images/s_careers.svg',
      '/g-home': 'assets/images/s_community.svg',
      '/g-school': 'assets/images/s_learn.svg',
      '/g-reels': 'assets/images/s_trends.svg',
      '/g-carvaan': 'assets/images/s_community.svg',
      '/sic-council': 'assets/images/my_council.svg',
      '/training': 'assets/images/selectedTrainings.svg',
      '/announcements': 'assets/images/selectedAnnouncements.svg',
      '/analytics': 'assets/images/selectedAnalytics.svg',
      '/library': 'assets/images/selectedLibrary.svg',
      '/my-space-settings': 'assets/images/selectedMySpaceSettings.svg',
      '/faculty-batch-detail': 'assets/images/learning_console.svg',
      '/time-table-webview': 'assets/images/analytics.svg',
      '/event_console': 'assets/images/s_event.svg'
    };

    // var featureTexts = {
    //   '/g-dashboard':
    //       'Explore our all-inclusive platform featuring an intuitive Dashboard for seamless navigation. Monitor real-time data, track trends, and gain instant insights effortlessly.',
    //   '/g-school':
    //       'Discover a list of registered courses and receive personalized recommendations for knowledge enrichment.',
    //   '/g-competitions':
    //       ' Engage in competitions to demonstrate your skills and enhance your portfolio.',
    //   '/g-carvaan':
    //       'Engage in a collaborative community for networking and shared learning experiences.',
    // };
    // var featureTitle = {
    //   '/g-dashboard': 'Dashboard',
    //   '/g-school': 'Learn ',
    //   '/g-competitions': 'Competition',
    //   '/g-carvaan': 'Community',
    // };

    if (widget.isFromCreatePost) {
      pages['/g-carvaan'] = GCarvaanPostPage(
        fileToUpload: widget.fileToUpload,
        desc: widget.desc,
        filesPath: widget.filesPath,
        formCreatePost: widget.isFromCreatePost,
      );
      widget.fileToUpload = null;
      widget.desc = null;
      widget.filesPath = null;
      widget.isFromCreatePost = false;
    } else {
      widget.fileToUpload = null;
      widget.desc = null;
      widget.filesPath = null;
      widget.isFromCreatePost = false;

      pages['/g-carvaan'] = CommunityDashboard();
    }

    return MultiProvider(
        providers: [
          ChangeNotifierProvider<VideoPlayerProvider>(
            create: (context) => VideoPlayerProvider(true),
          ),
          ChangeNotifierProvider<MenuListProvider>(
            create: (context) =>
                MenuListProvider(widget.bottomMenu!, index: widget.index),
          ),
        ],
        child:
            Consumer<MenuListProvider>(builder: (context, menuProvider, child) {
          mp = menuProvider;
          Application(context);
          return Scaffold(
            floatingActionButton: APK_DETAILS['bot_floating'] == '1'
                ? SizedBox(
                    width: 60,
                    height: 60,
                    child: FloatingActionButton(
                      onPressed: () {
                        /*Navigator.push(
                      context,
                      NextPageRoute(DialogFlowBot())).then((value){
                    setState(() {});
                  });*/

                        /*Navigator.push(
                    context,
                    NextPageRoute(
                      BotScreen(fromDashboard: true),
                    ),
                  ).then((value) {
                    setState(() {});
                  });*/

                        Navigator.push(
                          context,
                          NextPageRoute(
                            SinghBotPage(fromDashboard: true),
                          ),
                        ).then((value) {
                          setState(() {});
                        });
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: Container(
                          decoration: BoxDecoration(
                            color: ColorConstants.WHITE,
                            shape: BoxShape.circle,
                          ),
                          width: 100, // Adjust width
                          height: 100,
                          /*child: SvgPicture.asset(
                      'assets/images/bot_asset.svg',
                      height: 40.0,
                      width: 40.0,
                      allowDrawingOutsideViewBox: true,
                    ),*/ // Adjust height
                          child: Image.asset(
                            'assets/images/bot_asset.png',
                            // Adjust width of the image if necessary
                          ),
                        ),
                      ),
                    ),
                  )
                : SizedBox(),
            key: _scaffoldKey,
            backgroundColor: ColorConstants.GREY,
            appBar:
                APK_DETAILS['package_name'] == 'com.singularis.mescdigilibrary'
                    ? null
                    : PreferredSize(
                        preferredSize: Size.fromHeight(0),
                        child: AppBar(
                            automaticallyImplyLeading: false,
                            elevation: 0,
                            flexibleSpace: Container(
                              decoration: BoxDecoration(
                                color: ColorConstants.WHITE,
                                gradient: LinearGradient(
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                    colors: [
                                      ColorConstants().gradientLeft(),
                                      ColorConstants().gradientRight()
                                    ]),
                              ),
                            )),
                      ),
            body: PopScope(
                child: pages[widget
                        .bottomMenu![menuProvider.getCurrentIndex()].url] ??
                    SizedBox(
                      child: Center(
                        child: Text(
                            '${widget.bottomMenu![menuProvider.getCurrentIndex()].url}'),
                      ),
                    ),
                canPop: false,
                onPopInvokedWithResult: (didPop, result) async {
                  if (!didPop) {
                    final shouldPop = await onWillPop();
                    if (shouldPop && context.mounted) {
                      Navigator.of(context).pop();
                    }
                  }
                }),
            bottomNavigationBar: SizedBox(
              height: MediaQuery.of(context).size.height * 0.11, //old 0.10
              child: BottomNavigationBar(
                backgroundColor: ColorConstants.bottomNavigation(),
                type: BottomNavigationBarType.fixed,
                currentIndex: menuProvider.getCurrentIndex(),
                selectedItemColor: ColorConstants().primaryColor(),
                items: [
                  for (int i = 0; i < widget.bottomMenu!.length; i++)
                    BottomNavigationBarItem(
                      icon: Transform.translate(
                        offset: Offset(
                            0,
                            i == 2 &&
                                    (APK_DETAILS["package_name"] ==
                                            "com.singulariswow.mec" ||
                                        APK_DETAILS["package_name"] ==
                                            "com.singularis.jumeira" ||
                                        APK_DETAILS["package_name"] ==
                                            "com.singulariswow")
                                ? -20.5
                                : 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 3,
                            ),
                            menuProvider.getCurrentIndex() ==
                                    widget.bottomMenu!
                                        .indexOf(widget.bottomMenu![i])
                                ? SvgPicture.asset(
                                    '${iconSelected['${widget.bottomMenu![i].url}']}',
                                    colorFilter: i == 2 &&
                                            (APK_DETAILS["package_name"] ==
                                                    "com.singulariswow.mec" ||
                                                APK_DETAILS["package_name"] ==
                                                    "com.singularis.jumeira" ||
                                                APK_DETAILS["package_name"] ==
                                                    "com.singulariswow")
                                        ? null
                                        : ColorFilter.mode(
                                            ColorConstants
                                                .bottomNavigationSelected(),
                                            BlendMode.srcIn),
                                    allowDrawingOutsideViewBox: true,
                                  )
                                : SvgPicture.asset(
                                    '${iconUnSelected['${widget.bottomMenu![i].url}']}',
                                    allowDrawingOutsideViewBox: true,
                                    colorFilter: i == 2 &&
                                            (APK_DETAILS["package_name"] ==
                                                    "com.singulariswow.mec" ||
                                                APK_DETAILS["package_name"] ==
                                                    "com.singularis.jumeira" ||
                                                APK_DETAILS["package_name"] ==
                                                    "com.singulariswow")
                                        ? null
                                        : ColorFilter.mode(
                                            ColorConstants
                                                .bottomNavigationUnSelected(),
                                            BlendMode.srcIn),
                                  ),
                            const SizedBox(
                              height: 2,
                            ),
                            Text(
                              i == 2 &&
                                      (APK_DETAILS["package_name"] ==
                                              "com.singulariswow.mec" ||
                                          APK_DETAILS["package_name"] ==
                                              "com.singularis.jumeira" ||
                                          APK_DETAILS["package_name"] ==
                                              "com.singulariswow")
                                  ? ''
                                  : '${widget.bottomMenu![i].label}',
                              style: Styles.regular(
                                  size: 12,
                                  lineHeight: 1.3,
                                  color: menuProvider.getCurrentIndex() ==
                                          widget.bottomMenu!
                                              .indexOf(widget.bottomMenu![i])
                                      ? ColorConstants
                                          .bottomNavigationSelected()
                                      : ColorConstants
                                          .bottomNavigationUnSelected()),
                            ),
                          ],
                        ),
                      ),
                      label: '',
                    ),
                ],
                onTap: (index) {
                  if (widget.bottomMenu![index].linkType != 0) {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => Scaffold(
                                  appBar: AppBar(
                                    backgroundColor:
                                        ColorConstants().primaryColor(),
                                    elevation: 0.0,
                                    leading: IconButton(
                                      icon: const Icon(Icons.arrow_back),
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                    ),
                                  ),
                                  body: WebViewWidget(
                                    //javascriptMode: JavascriptMode.unrestricted,
                                    //initialUrl: '${ApiConstants().PRODUCTION_BASE_URL()}${widget.bottomMenu![index].url}?cred=${UserSession.userToken}',
                                    controller: WebViewController()
                                      ..setJavaScriptMode(
                                          JavaScriptMode.unrestricted)
                                      ..loadRequest(Uri.parse(
                                          '${ApiConstants().PRODUCTION_BASE_URL()}${widget.bottomMenu![index].url}?cred=${UserSession.userToken}')),
                                  ),
                                )));
                  } else {
                    setState(() {
                      // currentIndex = index;
                      menuProvider
                          .updateCurrentIndex(widget.bottomMenu![index].url!);
                    });
                  }
                },
              ),
            ),
          );
        }));
  }

  Future<bool> onWillPop() {
    DateTime now = DateTime.now();
    if (currentBackPressTime == null ||
        now.difference(currentBackPressTime!) > Duration(seconds: 2)) {
      currentBackPressTime = now;
      Utility.showSnackBar(
          scaffoldContext: context, message: tr('close_app_confirm'));
      return Future.value(false);
    }

    return Future.value(true);
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/my_assessment_response.dart';
import 'package:masterg/data/providers/mg_assessment_detail_provioder.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/card_loader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/gschool_widget/date_picker.dart';
import 'package:masterg/pages/training_pages/mg_assessment_detail.dart';
import 'package:masterg/pages/training_pages/new_screen/assessment_your_report_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;

class MyAssessmentPage extends StatefulWidget {
  bool? isViewAll;
  bool fromDashboard;
  Drawer? drawerWidget;
  String? interestAreaID;
  int? jobRoleID;
  int? skillID;

  MyAssessmentPage(
      {this.isViewAll,
      this.drawerWidget,
      this.fromDashboard = false,
      this.interestAreaID = '0',
      this.jobRoleID,
      this.skillID});

  @override
  _MyAssessmentPageState createState() => _MyAssessmentPageState();
}

class _MyAssessmentPageState extends State<MyAssessmentPage> {
  List<AssessmentList>? assessmentList = [];
  var isLoading = true;
  int? categoryId = 16;
  Box? box;

  int selectedIndex = 0;
  String selectedOption = 'All';
  bool selectedCalanderView = false;
  DateTime? selectedDate = currentIndiaTime;
  int assessmentViewCount = 0;

  @override
  void initState() {
    _getHomeData();
    super.initState();
    categoryId = Utility.getCategoryValue(ApiConstants.ANNOUNCEMENT_TYPE);
  }

  _showPopUpMenu(Offset offset) async {
    final screenSize = MediaQuery.of(context).size;
    double left = offset.dx;
    double top = offset.dy;
    double right = screenSize.width - offset.dx;
    double bottom = screenSize.height - offset.dy;

    showMenu<String>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      position: RelativeRect.fromLTRB(left, top, right, bottom),
      items: [
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/upcoming_live.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('upcoming_assessment').tr()
            ]),
            value: '1', enabled: false,),
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/completed_icon.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('assessment_completed').tr()
            ]),
            value: '2', enabled: false),
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/pending_icon.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('assessment_pending').tr()
            ]),
            value: '3', enabled: false),
      ],
      elevation: 8.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.GREY,
      appBar: widget.fromDashboard
          ? PreferredSize(
              preferredSize: const Size.fromHeight(0.0),
              child: SizedBox(),
            )
          : AppBar(
              title: Text(tr('assessment'), style: Styles.bold(size: 18)),
              centerTitle: false,
              backgroundColor: ColorConstants.WHITE,
              elevation: 0.0,
              actions: [
                GestureDetector(
                  onTapDown: (TapDownDetails detail) {
                    _showPopUpMenu(detail.globalPosition);
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: SvgPicture.asset(
                      'assets/images/info_icon.svg',
                      height: 22,
                      width: 22,
                      allowDrawingOutsideViewBox: true,
                    ),
                  ),
                )
              ],
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.black,
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
      body: _mainBody(),
    );
  }

  _mainBody() {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is MyAssessmentState) _handleAnnouncmentData(state);
          },
          child: _verticalList(),
        ));
  }

  // bool checkViewDate(endDate) {
  //   if (endDate == null) return false;
  //   String endDateString = Utility.convertDateFromMillis(endDate, 'dd-MM-yyyy');
  //   final DateFormat formatter = DateFormat('dd-MM-yyyy');
  //   final String formatted = formatter.format(selectedDate!);
  //
  //   if (endDateString == formatted)
  //     return true;
  //   else
  //     return false;
  // }

  bool checkViewDate(int? endDate, int? startDate) {
    if (startDate == null || endDate == null || selectedDate == null) return false;

    DateTime start = DateTime.fromMillisecondsSinceEpoch(startDate * 1000);
    DateTime end = DateTime.fromMillisecondsSinceEpoch(endDate * 1000);

    DateTime selected = selectedDate!; // assuming it's a DateTime

    //you need add extra date then use this code ..days: 1.. selected.isBefore(end.add(const Duration(days: 1)))
    if (selected.isAfter(start.subtract(const Duration(days: 1))) &&
        selected.isBefore(end.add(const Duration()))) {
      return true;
    } else {
      return false;
    }
  }


  _announenmentList() {
    return box != null
        ? ValueListenableBuilder(
            valueListenable: box!.listenable(),
            builder: (bc, Box box, child) {
              if (box.get("myassessment") == null) {
                return CardLoader();
              } else if (box.get("myassessment").isEmpty) {
                return Container(
                  height: height(context) * 0.7,
                  child: Center(
                    child: Text(
                      'no_active_assessments',
                      style: Styles.bold(size: 16),
                      textAlign: TextAlign.center,
                    ).tr(),
                  ),
                );
              }
              assessmentList = box
                  .get("myassessment")
                  .map((e) =>
                      AssessmentList.fromJson(Map<String, dynamic>.from(e)))
                  .cast<AssessmentList>()
                  .toList();
              // if (selectedOption == 'All')
              //   assessmentViewCount = assessmentList!.length;

              //
              List<AssessmentList>? tempAssessments = [];

              for (var assessment in assessmentList!) {
                if (selectedCalanderView) {
                  if (checkViewDate(assessment.endDate, assessment.startDate) == true) {
                    if (selectedOption == 'All' ||
                        selectedOption == assessment.status)
                      tempAssessments.add(assessment);
                  }
                } else if (selectedOption == 'All' ||
                    selectedOption == assessment.status)
                  tempAssessments.add(assessment);
              }
              assessmentList = tempAssessments;
              assessmentViewCount = tempAssessments.length;

              return widget.fromDashboard
                  ? SizedBox(
                      height: 105,
                      child: Center(child: _rowItem(assessmentList![0])),
                    )
                  : Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            Row(
                              children: [
                                if(selectedCalanderView == false)
                                Text('${tr('sort_by')}: ',
                                    style: Styles.regular(size: 14)),
                                if(selectedCalanderView == false)
                                DropdownButton<String>(
                                  underline: SizedBox(),
                                  hint: Text('${selectedOption.toLowerCase()}',
                                          style: Styles.bold(size: 14))
                                      .tr(),
                                  items: <String>[
                                    'All',
                                    'Upcoming',
                                    'Completed',
                                    'Pending'
                                  ].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value.toLowerCase()).tr(),
                                    );
                                  }).toList(),
                                  onChanged: (_) {
                                    assessmentViewCount = 0;
                                    for (var element in assessmentList!) {
                                      if (_ == element.status || _ == 'All') {
                                        assessmentViewCount++;
                                      }
                                    }
                                    setState(() {
                                      selectedOption = _!;
                                    });
                                  },
                                ),

                                Expanded(child: SizedBox()),
                                InkWell(
                                  onTap: () {
                                    setState(() {
                                      selectedCalanderView = false;
                                    });
                                  },
                                  child: !selectedCalanderView
                                      ? SvgPicture.asset(
                                          'assets/images/selected_listview.svg',
                                          height: 16,
                                          width: 16,
                                          allowDrawingOutsideViewBox: true,
                                        )
                                      : SvgPicture.asset(
                                          'assets/images/unselected_listview.svg',
                                          height: 16,
                                          width: 16,
                                          allowDrawingOutsideViewBox: true,
                                        ),
                                ),
                                SizedBox(width: 10),
                                InkWell(
                                  onTap: () {
                                    setState(() {
                                      selectedCalanderView = true;
                                    });
                                  },
                                  child: selectedCalanderView
                                      ? SvgPicture.asset(
                                          'assets/images/selected_calender.svg',
                                          height: 20,
                                          width: 20,
                                          allowDrawingOutsideViewBox: true,
                                        )
                                      : SvgPicture.asset(
                                          'assets/images/unselected_calender.svg',
                                          height: 20,
                                          width: 20,
                                          allowDrawingOutsideViewBox: true,
                                        ),
                                )
                              ],
                            ),
                            if (selectedCalanderView)
                              Calendar(
                                sendValue: (DateTime date) {
                                  setState(() {
                                    selectedDate = date;
                                  });
                                },
                              ),
                            assessmentViewCount != 0
                                ? ListView.builder(
                                    scrollDirection: Axis.vertical,
                                    shrinkWrap: true,
                                    physics: BouncingScrollPhysics(),
                                    itemCount: assessmentList == null
                                        ? 0
                                        : assessmentList!.length,
                                    itemBuilder: (context, index) {
                                      return _rowItem(assessmentList![index]);
                                    })
                                : Container(
                                    height: height(context) * 0.7,
                                    child: Center(
                                      child: Text(
                                        'no_active_assessments',
                                        style: Styles.bold(size: 16),
                                        textAlign: TextAlign.center,
                                      ).tr(),
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    );
            },
          )
        : CardLoader();
  }

  _verticalList() {
    return _announenmentList();
  }

  _rowItem(AssessmentList item) {
    return InkWell(
      onTap: () {
        Navigator.push(
                context,
                NextPageRoute(
                    ChangeNotifierProvider<MgAssessmentDetailProvider>(
                        create: (context) => MgAssessmentDetailProvider(
                            TrainingService(ApiService()), item),
                        child: MgAssessmentDetailPage(
                          programName: item.programName,
                          programId: item.program,
                        )),
                    isMaintainState: true))
            .then((value) => _getHomeData());
      },
      child: Container(
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width * 0.9,
          margin: EdgeInsets.symmetric(vertical: 10, horizontal: 6),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Stack(alignment: Alignment.center, children: [
            Container(
              child: Row(children: [
                // Text('${item.status}'),
                if (Utility.isExpired(item.endDate!, currentIndiaTime!) &&
                    item.status != 'Completed') ...[
                  SvgPicture.asset(
                    'assets/images/missed_icon.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ] else if (item.status == 'Completed') ...[
                  SvgPicture.asset(
                    'assets/images/completed_icon.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ] else if (item.status == 'Upcoming') ...[
                  SvgPicture.asset(
                    'assets/images/upcoming_live.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ] else if (item.status == 'Pending') ...[
                  SvgPicture.asset(
                    'assets/images/pending_icon.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ],
                SizedBox(width: 20),
                Container(
                  width: MediaQuery.of(context).size.width * 0.6,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${item.title}',
                          style: Styles.bold(size: 16),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (item.status == 'Completed') ...[
                          SizedBox(height: 5),
                          Text(
                              '${item.displayScorecard != 1 ? "" : '${item.score.toStringAsFixed(2)}/${item.maximumMarks} ${tr('marks')} •'}${item.attemptAllowed == 0 ? 'Unlimited attempts' : '${item.attemptAllowed! - item.attemptCount!} ${tr('attempts_left')}'}',
                              style: Styles.regular(
                                  size: 12, color: Colors.black)),
                          SizedBox(height: 5),
                          Row(children: [
                            Text(
                              '${tr('submit_before')} : ',
                              style: Styles.regular(size: 12),
                            ),
                            Text(
                              '${DateFormat('MM/dd/yyyy, hh:mm a').format(DateTime.fromMillisecondsSinceEpoch(item.endDate! * 1000))}',
                              style: Styles.regular(size: 12),
                              textDirection: ui.TextDirection.ltr,
                            )
                          ]),
                        ] else ...[
                          SizedBox(height: 5),
                          Text(
                              '${item.durationInMinutes} ${tr('minute')} • ${item.maximumMarks} ${tr('marks')}',
                              style: Styles.regular(
                                  size: 12, color: Colors.black)),
                          SizedBox(height: 5),
                          Row(children: [
                            Text(
                              '${tr('submit_before')} : ',
                              style: Styles.regular(size: 12),
                            ),
                            Text(
                              '${DateFormat('MM/dd/yyyy, hh:mm a').format(DateTime.fromMillisecondsSinceEpoch(item.endDate! * 1000))}',
                              style: Styles.regular(size: 12),
                              textDirection: ui.TextDirection.ltr,
                            )
                          ]),
                        ],
                      ]),
                ),
              ]),
            ),
            Positioned(
                right: Utility().isRTL(context) ? null : 0,
                left: Utility().isRTL(context) ? 0 : null,
                child: Visibility(
                  visible: item.status == 'Completed',
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            NextPageRoute(AssessmentYourReportPage(
                                contentId: item.contentId,
                                programId: item.program,
                            displayScorecard: item.displayScorecard,)));
                      },
                      child: Text('report',
                              textAlign: TextAlign.right,
                              style: Styles.regular(
                                  size: 12,
                                  color: ColorConstants().primaryColor() ??
                                      ColorConstants().primaryColorAlways()))
                          .tr(),
                    ),
                  ),
                ))
          ])),
    );
  }

  void _handleAnnouncmentData(MyAssessmentState state) {
    var loginState = state;
    // setState(() {
    switch (loginState.apiState) {
      case ApiStatus.LOADING:
        Log.v("Loading....................");
        break;
      case ApiStatus.SUCCESS:
        isLoading = false;

        assessmentList!.clear();

        break;
      case ApiStatus.ERROR:
        isLoading = false;
        Log.v("Error..........................");
        Log.v("ErrorAnnoucement..........................${loginState.error}");
        FirebaseAnalytics.instance.logEvent(name: 'my_assessment', parameters: {
          "ERROR": '${loginState.error}',
        });
        break;
      case ApiStatus.INITIAL:
        break;
    }
    // });
  }

  @override
  void dispose() {
    // box.close();
    super.dispose();
  }

  void _getHomeData() async {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context)
        .add(MyAssessmentEvent(box: box, interestID: widget.interestAreaID,
        jobRoleID: widget.jobRoleID, skillID: widget.skillID));
    setState(() {});
  }
}

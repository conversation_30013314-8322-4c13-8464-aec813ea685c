import 'dart:math';

import 'package:easy_localization/easy_localization.dart' as Localization;
import 'package:flutter/material.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

import '../../../utils/Styles.dart';

class ReadMoreText extends StatefulWidget {
  final String text;
  final Color? color;
  final String? viewMore;
  final double? height;
  final Function? onChange;
  final bool? showGradient;

  ReadMoreText(
      {Key? key,
      required this.text,
      this.color = ColorConstants.GREY_3,
      this.viewMore,
      this.height,
      this.onChange,
      this.showGradient = false})
      : super(key: key);

  @override
  State<ReadMoreText> createState() => _ReadMoreTextState();
}

class _ReadMoreTextState extends State<ReadMoreText> {
  bool isExpanded = false;
  bool fullHeight = false;
  int animationMs = 400;

  List<TextSpan> _parseText(String text) {
    List<TextSpan> textSpans = [];

    // Split the text into words
    List<String> words = text.split(' ');

    // Iterate through each word
    for (String word in words) {
      if (word.startsWith('#')) {
        // If the word starts with '#', add it with red color
        textSpans.add(
          TextSpan(
            text: '$word ',
            style: TextStyle(
                color: ColorConstants.PRIMARY_BLUE), // Change color as needed
          ),
        );
      } else {
        // If the word doesn't start with '#', add it with default color
        textSpans.add(
          TextSpan(
            text: '$word ',
            style: TextStyle(
                color: widget.color ?? ColorConstants.BLACK), // Default color
          ),
        );
      }
    }

    return textSpans;
  }

  @override
  Widget build(BuildContext context) {
    final span = TextSpan(text: widget.text);
    final tp =
        TextPainter(text: span, maxLines: 2, textDirection: TextDirection.rtl);
    tp.layout(
        maxWidth: MediaQuery.of(context)
            .size
            .width); // equals the parent screen width
    final th =
        TextPainter(text: span, maxLines: 16, textDirection: TextDirection.rtl);
    th.layout(
        maxWidth: MediaQuery.of(context)
            .size
            .width); // equals the parent screen width
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        widget.height == null
            ? RichText(
                text: TextSpan(
                  children: _parseText(widget.text),
                ),
                maxLines: isExpanded != true ? 2 : null,
              )

            // ? Text(
            //     widget.text,
            //     style: Styles.regular(
            //         size: 14, color: widget.color ?? ColorConstants.BLACK),
            //     maxLines: isExpanded != true ? 2 : null,
            //     // overflow: isExpanded,
            //     // overflow: isExpanded != true ? TextOverflow.fade : null,
            //   )
            : AnimatedContainer(
                curve: Curves.easeIn,
                width: double.infinity,
                duration: Duration(milliseconds: animationMs),
                padding: EdgeInsets.only(
                    right: widget.showGradient == true
                        ? Utility().isRTL(context)
                            ? 10
                            : 70
                        : 0,
                    left: Utility().isRTL(context) ? 70 : 0),
                decoration: BoxDecoration(
                  gradient: widget.showGradient == true && widget.height != null
                      ? LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withValues(alpha: 0.1),
                            Colors.black.withValues(alpha: 0.5),
                            Colors.black.withValues(alpha: 0.5),
                            Colors.black.withValues(alpha: 0.2),
                          ],
                          stops: [0.0, 0.2, 0.8, 1.0],
                        )
                      : null,
                ),
                height: fullHeight ? min(widget.height!, th.height + 50) : 40,
                child: SingleChildScrollView(
                  physics: isExpanded
                      ? BouncingScrollPhysics()
                      : NeverScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: _parseText(widget.text),
                        ),
                        maxLines: isExpanded != true ? 2 : null,
                      )
                      // Text(
                      //   widget.text,
                      //   style: Styles.regular(
                      //       lineHeight: 1.5,
                      //       size: 14,
                      //       color: widget.color ?? ColorConstants.BLACK),
                      //   maxLines: isExpanded != true ? 2 : null,
                      //   // overflow: isExpanded,
                      //   // overflow: isExpanded != true ? TextOverflow.fade : null,
                      // ),
                    ],
                  ),
                ),
              ),
        if (widget.text.length != 0 && tp.didExceedMaxLines)
          InkWell(
            onTap: () async {
              if (fullHeight == false) {
                setState(() {
                  isExpanded = !isExpanded;
                });
                setState(() {
                  fullHeight = !fullHeight;
                });
              } else {
                setState(() {
                  fullHeight = !fullHeight;
                });
                if (widget.height != null)
                  await Future.delayed(Duration(milliseconds: animationMs));

                setState(() {
                  isExpanded = !isExpanded;
                });
              }
              if (widget.onChange != null) {
                widget.onChange!(isExpanded);
              }
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: widget.showGradient == false ? 0 : 0,
                  right: widget.showGradient == false ? 0 : 0,
                  top: 5.0),
              child: Text(
                isExpanded
                    ? Localization.tr("see_less")
                    : Localization.tr('view_more'),
                style: Styles.bold(
                    size: 11, color: widget.color ?? ColorConstants.GREY),
              ),
            ),
          ),
      ],
    );
  }
}

import 'package:flutter/material.dart';

class SettingAndPrivacyData {
  static final List<Map<String, dynamic>> settingsData = [
    {
      'icon': Icons.lock,
      'text': 'account_privacy',
      'trailing': 'private',
      'color': Colors.green,
    },
    {
      'icon': Icons.language,
      'text': 'language',
      'trailing': 'English',
      'color': Color(0xffF0F0F0),
    },
    {
      'icon': Icons.account_circle_rounded,
      'text': 'account_status',
      'trailing': 'active',
      'color': Colors.blue,
    },
    {
      'icon': Icons.thumb_up_alt_rounded,
      'text': 'like_count',
      'trailing': 'yes',
      'color': Colors.green,
    },
    {
      'icon': Icons.comment,
      'text': 'comments',
      'trailing': 'yes',
      'color': Colors.orange,
    },
    {
      'icon': Icons.share,
      'text': 'sharing',
      'trailing': 'yes',
      'color': Colors.blue,
    },
    {
      'icon': Icons.dark_mode,
      'text': 'dark_theme',
      'isToggle': true,
      'color': Colors.black,
    },
  ];

  static final List<Map<String, dynamic>> privacyData = [
    {
      'icon': Icons.sticky_note_2_outlined,
      'text': 't_c',
      'color': Colors.blue,
    },
    {
      'icon': 'assets/images/about.svg',
      'text': 'about_singularis',
      'color': Colors.amber,
    },
    {
      'icon': Icons.delete_outline,
      'text': 'delete_account',
      'color': Color(0xFFEB5757),
    },
  ];
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../local/pref/Preference.dart';

class HtmlWebViewPage extends StatefulWidget {
  //final String? url;
  final String? title;
  HtmlWebViewPage({Key? key, this.title}) : super(key: key);

  @override
  State<HtmlWebViewPage> createState() => _HtmlWebViewPageState();
}

class _HtmlWebViewPageState extends State<HtmlWebViewPage> {
  bool isLoading = true;
  final _key = UniqueKey();
  late final WebViewController _webViewController;
  @override
  void initState() {
    super.initState();

    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadHtmlString(htmlContentMCQ)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            Future.delayed(const Duration(seconds: 12), () {
              setState(() {
                isLoading = false;
              });
            });
          },
        ),
      );
  }

  final String htmlContent = """
 <html><body>
  <form style="display:block;" id="login" action="${Preference.getString(Preference.MEC_LEARN)}" method="post">
          <input type="hidden" name="anchor" value="">
          <input type="hidden" type="text" name="username" size="18" value="${Preference.getString(Preference.LOGIN_ID).toString().trim().split("@").first}" />
          <input type="hidden" type="text" name="password" size="18" value="${Preference.getString(Preference.LOGIN_PASS).toString()}" />
          <input type="hidden" type="text" name="wantsurl" size="18" value="/course/" /> 
          <h1> <button id="loginBtn" type="submit" style="display:none;">MEC Learn</button> </h1> 
          <script>
            document.getElementById("loginBtn").click();
          </script>
        </form>        
        </body></html>
        """;


  final String htmlContentMCQ = """
<html>
  <body>
    <style>
      body { font-family: Arial; padding: 10px; }
      select, input { padding: 6px; margin: 5px; font-size: 14px; }
    </style>

    <div class="question-content richText-editor">
      <!-- Select dropdown -->
      <span class="mb_blank" data-id="1" data-type="mcq">
        <select name="blank_1824_1" class="mb_blank_control">
          <option value="">Select</option>
          <option value="1755693159626.9">1</option>
          <option value="1755693164699">s</option>
        </select>
      </span>

      fgfffgf

      <!-- Editable input (keyboard will now open) -->
      <span class="mb_blank" data-id="2" data-type="short">
        <input type="text" name="blank_1824_2"
          class="mb_blank_control"
          placeholder="Type your answer here">
      </span>

      fddfdfqweeref

      <!-- Another dropdown -->
      <span class="mb_blank" data-id="3" data-type="mcq">
        <select name="blank_1824_3" class="mb_blank_control">
          <option value="">Select</option>
          <option value="1755695227319.2">a</option>
          <option value="1755695228640">d</option>
        </select>
      </span>
    </div>
  </body>
</html>
""";





  Future<void> _getAllValues() async {
    final result = await _webViewController.runJavaScriptReturningResult("""
    (function() {
      //const data = {};
      const values = [];
      document.querySelectorAll('input, select').forEach(el => {
        // if (el.tagName.toLowerCase() === 'select') {
        //   data[el.name || el.id] = el.options[el.selectedIndex].text; // visible text
        // } else {
        //   data[el.name || el.id] = el.value;
        // }
        if (el.tagName.toLowerCase() === 'select') {
          values.push(el.options[el.selectedIndex].text); // visible text
        } else {
          values.push(el.value);
        }
      });
      //return JSON.stringify(data);
      return JSON.stringify(values);
    })();
  """);

    // Convert JS result string into Dart Map
    final valuesMap = jsonDecode(result as String);
    debugPrint("Form values: $valuesMap");
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.WHITE,
      appBar: AppBar(
          elevation: 0.0,
          leading: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Icon(
                Icons.arrow_back,
                color: Colors.black,
              )),
          title: Text(
            '${widget.title}'
                .replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
            style: Styles.semibold(
              color: ColorConstants.BLACK,
            ),
          ),
          backgroundColor: ColorConstants.WHITE),
      body: Stack(
        children: <Widget>[
          WebViewWidget(
            key: _key,
            controller: _webViewController,
          ),
          // WebViewW(
          //   key: _key,
          //   initialUrl: 'about:blank',
          //   javascriptMode: JavascriptMode.unrestricted,
          //   onWebViewCreated: (WebViewController controller) {
          //     controller.loadUrl(Uri.dataFromString(
          //       htmlContent,
          //       mimeType: 'text/html',
          //       encoding: Encoding.getByName('utf-8'),
          //     ).toString());
          //   },
          //   onPageFinished: (finish) {
          //     Future.delayed(Duration(seconds: 12), () {
          //       // Set the state to trigger a rebuild and hide the widget
          //       setState(() {
          //         isLoading = false;
          //       });
          //     });
          //   },
          // ),
          isLoading
              ? Center(
                  child: CircularProgressIndicator(),
                )
              : Stack(),

          Padding(
            padding: const EdgeInsets.all(0.0),
            child: InkWell(
              onTap: () {
                _getAllValues();
              },

              child: Text('Get Html Data'),
            ),
          ),
        ],
      ),
    );
  }
}
//https://meclearn.mec.edu.om/spring2024/login/sso_login.php
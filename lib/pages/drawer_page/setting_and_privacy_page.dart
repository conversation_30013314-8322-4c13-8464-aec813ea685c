import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../local/pref/Preference.dart';
import '../../utils/config.dart';
import '../../utils/resource/colors.dart';
import '../auth_pages/terms_and_condition_page.dart';
import '../user_profile_page/delete_account_page.dart';

class SettingAndPrivacyPage extends StatefulWidget {
  const SettingAndPrivacyPage({Key? key}) : super(key: key);

  @override
  State<SettingAndPrivacyPage> createState() => _SettingAndPrivacyPageState();
}

class _SettingAndPrivacyPageState extends State<SettingAndPrivacyPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'settings_privacy',
          style: TextStyle(color: Colors.black),
        ).tr(),
        iconTheme: IconThemeData(
          color: Colors.black, //change your color here
        ),
      ),
      body: Container(
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 30.0),
              color: Colors.white,
              child: Column(
                children: [
                  Divider(
                    color: Colors.white,
                  ),
                  ListTile(
                    dense: true,
                    trailing: Container(
                      width: 100,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '${tr('private')}',
                            style: TextStyle(fontSize: 12),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                        ],
                      ),
                    ),
                    visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                    leading: Container(
                        width: 30,
                        height: 30,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: Colors.green,
                            borderRadius: BorderRadius.circular(6)),
                        child: Icon(
                          Icons.lock,
                          color: Colors.white,
                          size: 20,
                        )),
                    title: Text('${tr('account_privacy')}'),
                    onTap: () {},
                  ),
                  Divider(),
                  ListTile(
                    dense: true,
                    trailing: Container(
                      width: 100,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '${Preference.getString(Preference.APP_ENGLISH_NAME)}',
                            style: TextStyle(fontSize: 12),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                        ],
                      ),
                    ),
                    visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                    leading: Container(
                        width: 30,
                        height: 30,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: Colors.grey,
                            borderRadius: BorderRadius.circular(6)),
                        child: Icon(
                          Icons.language,
                          color: Colors.white,
                          size: 20,
                        )),
                    title: Text('${tr('language')}'),
                    onTap: () {},
                  ),
                  Divider(),
                  ListTile(
                    dense: true,
                    trailing: Container(
                      width: 100,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '${tr('active')}',
                            style: TextStyle(fontSize: 12),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                        ],
                      ),
                    ),
                    visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                    leading: Container(
                        width: 30,
                        height: 30,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: ColorConstants.UNSELECTED_PAGE,
                            borderRadius: BorderRadius.circular(6)),
                        child: Icon(
                          Icons.account_circle_rounded,
                          color: Colors.white,
                          size: 20,
                        )),
                    title: Text('${tr('account_status')}'),
                    onTap: () {},
                  ),
                  Divider(),
                  ListTile(
                    dense: true,
                    trailing: Container(
                      width: 100,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '${tr('yes')}',
                            style: TextStyle(fontSize: 12),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                        ],
                      ),
                    ),
                    visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                    leading: Container(
                        width: 30,
                        height: 30,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: ColorConstants.Color_GREEN,
                            borderRadius: BorderRadius.circular(6)),
                        child: Icon(
                          Icons.thumb_up_alt_rounded,
                          color: Colors.white,
                          size: 20,
                        )),
                    title: Text('${tr('like_count')}'),
                    onTap: () {},
                  ),
                  Divider(),
                  ListTile(
                    dense: true,
                    trailing: Container(
                      width: 100,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '${tr('yes')}',
                            style: TextStyle(fontSize: 12),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                        ],
                      ),
                    ),
                    visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                    leading: Container(
                        width: 30,
                        height: 30,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: ColorConstants.EXPERT,
                            borderRadius: BorderRadius.circular(6)),
                        child: Icon(
                          Icons.comment,
                          color: Colors.white,
                          size: 20,
                        )),
                    title: Text('${tr('comments')}'),
                    onTap: () {},
                  ),
                  Divider(),
                  ListTile(
                    dense: true,
                    trailing: Container(
                      width: 100,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '${tr('yes')}',
                            style: TextStyle(fontSize: 12),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                        ],
                      ),
                    ),
                    visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                    leading: Container(
                        width: 30,
                        height: 30,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: ColorConstants.CONTINUE_COLOR,
                            borderRadius: BorderRadius.circular(6)),
                        child: Icon(
                          Icons.share,
                          color: Colors.white,
                          size: 20,
                        )),
                    title: Text('${tr('sharing')}'),
                    onTap: () {},
                  ),
                  Divider(),
                ],
              ),
            ),
            SizedBox(
              height: 20,
            ),
            Container(
                color: Colors.white,
                padding: EdgeInsets.only(bottom: 10.0, top: 10.0),
                child: Column(
                  children: [
                    ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      leading: Container(
                          width: 30,
                          height: 30,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                              color: ColorConstants.BG_BLUE_BTN,
                              borderRadius: BorderRadius.circular(6)),
                          child: Icon(
                            Icons.sticky_note_2_outlined,
                            color: ColorConstants.WHITE,
                            size: 20,
                          )),
                      title: Text('t_c').tr(),
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: Preference.getString(
                                          Preference.TERMS_AND_CON_URL),
                                      title: tr('t_c'),
                                    ),
                                maintainState: false));
                      },
                    ),
                    Divider(),
                    ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      leading: Container(
                          width: 30,
                          height: 30,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(6)),
                          child: SvgPicture.asset(
                            'assets/images/about.svg',
                            colorFilter: ColorFilter.mode(
                                ColorConstants.WHITE, BlendMode.srcIn),
                          )),
                      title: Text('${tr('about_singularis')}'.replaceAll(
                          'Singularis', '${APK_DETAILS['app_name']}')),
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: APK_DETAILS['about_url'],
                                      title: tr('about_singularis'),
                                    ),
                                maintainState: false));
                      },
                    ),
                    Divider(),
                    ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      leading: Container(
                          width: 30,
                          height: 30,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                              color: ColorConstants.RED,
                              borderRadius: BorderRadius.circular(6)),
                          child: Icon(
                            Icons.delete_outline,
                            color: ColorConstants.WHITE,
                            size: 20,
                          )),
                      title: Text(
                        'delete_account',
                      ).tr(),
                      onTap: () async {
                        Navigator.pop(context);
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => DeleteAccountPage(
                                    imageUrl:
                                        '${Preference.getString(Preference.PROFILE_IMAGE)}')));
                      },
                    ),
                  ],
                )),
          ],
        ),
      ),
    );
  }
}

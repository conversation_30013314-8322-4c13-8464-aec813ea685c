
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../data/models/response/auth_response/bottombar_response.dart';
import '../../local/pref/Preference.dart';
import '../../utils/config.dart';
import '../../utils/constant.dart';
import '../auth_pages/terms_and_condition_page.dart';
import '../custom_pages/custom_widgets/rounded_appbar.dart';
import '../singularis/app_drawer_page.dart';
import '../user_profile_page/portfolio_create_form/portfolio_page.dart';

class EventConsolePage extends StatefulWidget {
  final bool? appBarEnable;
  const EventConsolePage({this.appBarEnable = false, super.key});

  @override
  State<EventConsolePage> createState() => _EventConsolePageState();
}

class _EventConsolePageState extends State<EventConsolePage> {

  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  MenuListProvider? menuProvider;

  @override
  void initState() {
    super.initState();
    print('AppBar= ${widget.appBarEnable}');
  }

  @override
  Widget build(BuildContext context) {
    print('${APK_DETAILS['faculty_events_console']}' +
        Preference.getInt(
            Preference.USER_ID)
            .toString());


    return widget.appBarEnable == false ? Scaffold(
      key: _scaffoldKey,
      endDrawer: new AppDrawer(),
      body: Column(
        children: [
          _customAppBar(),
          Container(
            height: MediaQuery.of(context).size.height -230,
            color: Colors.red,
            //margin: EdgeInsets.only(bottom: 200),
            child: TermsAndCondition(
              url:
              '${APK_DETAILS['faculty_events_console']}' +
                  Preference.getInt(
                      Preference.USER_ID)
                      .toString(),
              title: tr('event_console'),
              appBarEnable: widget.appBarEnable,
            ),
          ),
        ],
      ),
    ) : Container(
      child: TermsAndCondition(
        url:
        '${APK_DETAILS['faculty_events_console']}' +
            Preference.getInt(
                Preference.USER_ID)
                .toString(),
        title: tr('event_console'),
        appBarEnable: widget.appBarEnable,
      ),
    );
  }


  Widget _customAppBar() {
    return RoundedAppBar(
        appBarHeight: height(context) * 0.1,
        child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 12),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => NewPortfolioPage(expJobResume: false,)))
                              .then((value) {
                            if (value != null)
                              menuProvider?.updateCurrentIndex(value);
                          });
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(200),
                          child: SizedBox(
                              width: 50,
                              child: CachedNetworkImage(
                                imageUrl:
                                '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                height: 50,
                                width: 50,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => SvgPicture.asset(
                                  'assets/images/default_user.svg',
                                  width: 50,
                                ),
                                errorWidget: (context, url, error) =>
                                    SvgPicture.asset(
                                      'assets/images/default_user.svg',
                                      width: 50,
                                    ),
                              )),
                        ),
                      ),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          _scaffoldKey.currentState?.openEndDrawer();
                        },
                        child: SvgPicture.asset(
                            'assets/images/hamburger_menu.svg'),
                      )
                    ],
                  ),
                ])));
  }
}

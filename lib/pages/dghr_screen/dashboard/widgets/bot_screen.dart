//import 'package:dialog_flowtter/dialog_flowtter.dart';
import 'package:flutter/material.dart';
import 'package:masterg/bots/messages.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class BotScreen extends StatefulWidget {
  final String? question;
  const BotScreen({Key? key, this.question}) : super(key: key);

  @override
  _BotScreenState createState() => _BotScreenState();
}

class _BotScreenState extends State<BotScreen> {
  //DialogFlowtter? dialogFlowtter;
  final TextEditingController _controller = TextEditingController();

  List<Map<String, dynamic>> messages = [];
  Color themeColor = Color.fromARGB(255, 190, 183, 249);

  @override
  void initState() {
    /*DialogFlowtter.fromFile()
        .then((instance) => dialogFlowtter = instance)
        .then((value) {
      if (widget.question != null) sendMessage('${widget.question}');
      else sendMessage('default_msg'); 
    });*/
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
       titleTextStyle: Styles.bold(),
       iconTheme: IconThemeData(color: ColorConstants.BLACK),
        centerTitle: true,
        title: const Text('Customer Support'),
        elevation: 0.5,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                // Color.fromARGB(120, 146, 187, 227),
                // Color.fromARGB(255, 28, 115, 142),
                // themeColor,
                // themeColor,
                Colors.white,
                Colors.white,
              ],
            ),
          ),
        ),
      ),
      body: Container(
        // decoration: const BoxDecoration(
        //   image: DecorationImage(
        //       image: AssetImage('assets/logo.png'),
        //       fit: BoxFit.fitWidth,
        //       opacity: 0.8),
        // ),
        child: Column(
          children: [
            Expanded(child: MessagesScreen(messages: messages, blankPage: widget.question == null,)),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    themeColor,
                    themeColor,
                  ],
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Type a message...',
                        hintStyle: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Cera Pro',
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      sendMessage(_controller.text);
                      _controller.clear();
                    },
                    icon: const Icon(Icons.send_rounded),
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  sendMessage(String text) async {
    if (text.isEmpty) {
      // print('Message is empty');
    } else {
      setState(() {
        //addMessage(Message(text: DialogText(text: [text])), true);
      });

      // DetectIntentResponse? response = await dialogFlowtter?.detectIntent(
      //   queryInput: QueryInput(text: TextInput(text: text)),
      // );
      // if (response?.message == null) return;
      // setState(() {
      //   addMessage(response!.message!);
      // });
    }
  }

  // addMessage(Message message, [bool isUserMessage = false]) {
  //   messages.add({'message': message, 'isUserMessage': isUserMessage});
  // }
}

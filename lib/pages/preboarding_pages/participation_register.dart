import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/event_participate_response.dart';

import 'package:masterg/pages/auth_pages/new_language_screen.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';

import 'package:provider/provider.dart';

class ParticiaptionRegister extends StatefulWidget {
  final int programId;
  const ParticiaptionRegister({super.key, required this.programId});

  @override
  State<ParticiaptionRegister> createState() => _ParticiaptionRegisterState();
}

class _ParticiaptionRegisterState extends State<ParticiaptionRegister> {
  final _formKey = GlobalKey<FormState>();
  final phoneController = TextEditingController();
  final fullNameController = TextEditingController();
  final emailController = TextEditingController();
  ParticiapteResp? registerResponse;

  final nameFocus = FocusNode();
  final phoneFocus = FocusNode();
  final emailFocus = FocusNode();
  bool? isLoading = false;

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
                  listener: (context, state) async {
                    if (state is ParticipateState) {
                      _handleParticipateState(state);
                    }
                  },
                  child: Scaffold(
                      appBar: AppBar(
                          backgroundColor: ColorConstants.WHITE,
                          elevation: 0,
                          leading: BackButton(color: ColorConstants.BLACK),
                          title: Text('register', style: Styles.bold()).tr()),
                      body: ScreenWithLoader(
                        isLoading: isLoading,
                        body: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 8),
                          child: Form(
                            key: _formKey,
                            child: SingleChildScrollView(
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('${tr('full_name')}*',
                                        style: Styles.regular(size: 12)),
                                    SizedBox(
                                      height: 6,
                                    ),
                                    Center(
                                        child: Column(children: [
                                      TextFormField(
                                        controller: fullNameController,
                                        style: Styles.regular(),
                                        maxLength: 100,
                                        decoration: InputDecoration(
                                          prefixIcon: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: SizedBox(
                                              width: 40,
                                              height: 40,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  SvgPicture.asset(
                                                    'assets/images/default_user.svg',
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                            ColorConstants()
                                                                .primaryColor()!,
                                                            BlendMode.srcIn),
                                                    height: 24,
                                                    width: 24,
                                                  ),
                                                  VerticalDivider(
                                                    thickness: 1,
                                                    color: Color(0xffE5E5E5),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                            borderSide: BorderSide(
                                              color: Color(0xffE5E5E5),
                                              width: 1.5,
                                            ),
                                          ),
                                          hintText: tr('enter_full_name'),
                                          hintStyle: TextStyle(
                                            color: Color(0xffE5E5E5),
                                          ),
                                          isDense: true,
                                          prefixIconConstraints: BoxConstraints(
                                              minWidth: 0, minHeight: 0),
                                          border: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                  width: 1,
                                                  color: ColorConstants.WHITE),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          helperStyle: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.GREY_3
                                                  .withValues(alpha: 0.1)),
                                          counterText: "",
                                        ),
                                        onChanged: (value) {
                                          setState(() {});
                                        },
                                        validator: (value) {
                                          if (value!.length == 0)
                                            return tr('enter_full_name');

                                          return null;
                                        },
                                      ),
                                    ])),
                                    SizedBox(
                                      height: 20,
                                    ),

                                    ///Email Fields
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text('email',
                                                style: Styles.regular(size: 12))
                                            .tr(),
                                        SizedBox(
                                          height: 6,
                                        ),
                                        TextFormField(
                                          cursorColor:
                                              ColorConstants().gradientRight(),
                                          autofocus: true,
                                          controller: emailController,

                                          // readOnly: widget.loginWithEmail ?? true,
                                          style: Styles.regular(
                                            color: Color(0xff0E1638),
                                            size: 14,
                                          ),

                                          decoration: InputDecoration(
                                            prefixIcon: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: SizedBox(
                                                width: 40,
                                                height: 40,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.end,
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/images/email.svg',
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants()
                                                                  .primaryColor()!,
                                                              BlendMode.srcIn),
                                                    ),
                                                    VerticalDivider(
                                                      thickness: 1,
                                                      color: Color(0xffE5E5E5),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                                width: 1.5,
                                              ),
                                            ),
                                            hintText: '<EMAIL>',
                                            hintStyle: TextStyle(
                                              color: Color(0xffE5E5E5),
                                            ),
                                            isDense: true,
                                            prefixIconConstraints:
                                                BoxConstraints(
                                                    minWidth: 0, minHeight: 0),
                                            border: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    width: 1,
                                                    color:
                                                        ColorConstants.WHITE),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            helperStyle: Styles.regular(
                                                size: 14,
                                                color: ColorConstants.GREY_3
                                                    .withValues(alpha: 0.1)),
                                            counterText: "",
                                          ),
                                          validator: (value) {
                                            if (value == '')
                                              return tr('email_required');
                                            int index = value?.length as int;

                                            if (value![index - 1] == '.')
                                              return tr('email_is_invalid');

                                            if (!RegExp(
                                                    r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                                .hasMatch(value))
                                              return tr('email_is_invalid');

                                            return null;
                                          },
                                        ),
                                        SizedBox(height: 5),
                                        Text('phone',
                                                style: Styles.regular(size: 12))
                                            .tr(),
                                        SizedBox(
                                          height: 6,
                                        ),
                                        TextField(
                                          controller: phoneController,
                                          keyboardType: TextInputType.number,
                                          maxLength:
                                              APK_DETAILS["package_name"] ==
                                                      "com.singulariswow.mec"
                                                  ? 8
                                                  : 10,
                                          decoration: InputDecoration(
                                            prefixIcon: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: SizedBox(
                                                width: 40,
                                                height: 40,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.end,
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/images/call.svg',
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants()
                                                                  .primaryColor()!,
                                                              BlendMode.srcIn),
                                                    ),
                                                    VerticalDivider(
                                                      thickness: 1,
                                                      color: Color(0xffE5E5E5),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              borderSide: BorderSide(
                                                color: Color(0xffE5E5E5),
                                                width: 1.5,
                                              ),
                                            ),
                                            hintText: tr('enter_mobile_number'),
                                            hintStyle: TextStyle(
                                              color: Color(0xffE5E5E5),
                                            ),
                                            isDense: true,
                                            prefixIconConstraints:
                                                BoxConstraints(
                                                    minWidth: 0, minHeight: 0),
                                            border: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    width: 1,
                                                    color:
                                                        ColorConstants.WHITE),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            helperStyle: Styles.regular(
                                                size: 14,
                                                color: ColorConstants.GREY_3
                                                    .withValues(alpha: 0.1)),
                                            counterText: "",
                                          ),
                                        )
                                      ],
                                    ),

                                    SizedBox(height: 100),
                                    InkWell(
                                      onTap: () {
                                        print(
                                            'check is ${fullNameController.value.text}');
                                        print(
                                            'check is ${emailController.value.text}');
                                        print(
                                            'check is ${phoneController.value.text}');
                                        if (_formKey.currentState!.validate()) {
                                          getParticipate(
                                              name:
                                                  fullNameController.value.text,
                                              email: emailController.value.text,
                                              mobileNo:
                                                  phoneController.value.text,
                                              programId: widget.programId,
                                              isMobile: 1);
                                        }
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(SnackBar(
                                          content: Text(
                                                  'You are successfully enrolled')
                                              .tr(),
                                        ));
                                        Navigator.pushAndRemoveUntil(
                                            context,
                                            NextPageRoute(SelectLanguage(
                                              showEdulystLogo: true,
                                            )),
                                            (route) => false);
                                      },
                                      child: Container(
                                        height: height(context) * 0.06,
                                        width: width(context),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          gradient: LinearGradient(colors: [
                                            ColorConstants().gradientLeft(),
                                            ColorConstants().gradientRight(),
                                          ]),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              'Participate',
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold),
                                            ).tr(),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 10.0),
                                              child: Icon(
                                                Icons.arrow_forward_ios_rounded,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ]),
                            ),
                          ),
                        ),
                      )),
                )));
  }

  void getParticipate(
      {String? name,
      String? email,
      String? mobileNo,
      int? isMobile,
      int? programId}) {
    BlocProvider.of<HomeBloc>(context).add(ParticipateEvent(
        name: name,
        email: email,
        mobileNo: mobileNo,
        programId: programId,
        isMobile: isMobile));
  }

  void _handleParticipateState(ParticipateState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................ParticipateState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................ParticipateState");

            registerResponse = state.response;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................ParticipateState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }
}

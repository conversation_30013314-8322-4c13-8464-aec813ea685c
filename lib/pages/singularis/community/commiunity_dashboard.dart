import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/pages/reels/reel_screen.dart';
import 'package:masterg/pages/singularis/app_drawer_page.dart';
import 'package:masterg/pages/singularis/wow_studio.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../local/pref/Preference.dart';
import '../../../utils/config.dart';
import '../../../utils/constant.dart';
import '../../custom_pages/custom_widgets/NextPageRouting.dart';
import '../../custom_pages/custom_widgets/rounded_appbar.dart';
import '../../gcarvaan/post/gcarvaan_post_page.dart';

class CommunityDashboard extends StatefulWidget {
  final bool? fullHeight;
  const CommunityDashboard({Key? key, this.fullHeight = false})
      : super(key: key);

  @override
  State<CommunityDashboard> createState() => _CommunityDashboardState();
}

class _CommunityDashboardState extends State<CommunityDashboard> {
  var _scaffoldKey = new GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    // final Shader linearGradient = LinearGradient(
    //   colors: <Color>[
    //     ColorConstants().gradientLeft(),
    //     ColorConstants().gradientRight()
    //   ],
    // ).createShader(new Rect.fromLTWH(0.0, 0.0, 200.0, 70.0));

    // final Shader linearGradientUn = LinearGradient(
    //   colors: <Color>[ColorConstants.GREY_3, ColorConstants.GREY_3],
    // ).createShader(new Rect.fromLTWH(0.0, 0.0, 200.0, 70.0));

    return Scaffold(
      key: _scaffoldKey,
      endDrawer: new AppDrawer(),
      appBar: PreferredSize(
          preferredSize: const Size(double.infinity, kToolbarHeight * 1.55),
          child: _customAppBar()),
      body: BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) async {},
          child: Container(
            height: height(context) * (widget.fullHeight == true ? 0.9 : 0.8),
            child: GCarvaanPostPage(
              fileToUpload: null,
              desc: null,
              filesPath: null,
              formCreatePost: false,
            ),
          ),
        ),
      ),
    );
  }

  Widget _customAppBar() {
    return RoundedAppBar(
        appBarHeight: height(context) * 0.1,
        child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 18, horizontal: 12),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () {
                              if (Preference.getString(Preference.ROLE) ==
                                      'Learner' ||
                                  Preference.getString(Preference.ROLE) ==
                                      'Lead' ||
                                  Preference.getString(Preference.ROLE) ==
                                      'Alumni') {
                                Navigator.push(context,
                                        NextPageRoute(NewPortfolioPage()))
                                    .then((value) {
                                  if (value != null)
                                    mp.updateCurrentIndex(value);
                                });
                              } else {
                                Navigator.push(
                                    context,
                                    NextPageRoute(NewPortfolioPage(
                                      expJobResume: false,
                                    ))).then((value) {
                                  if (value != null)
                                    mp.updateCurrentIndex(value);
                                });
                                /* Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TermsAndCondition(
                                              url:
                                                  'https://mecfuture.mec.edu.om/hris/my-profile?user_id=' +
                                                      Preference.getInt(
                                                              Preference
                                                                  .USER_ID)
                                                          .toString(),
                                              title: tr('my_profile'),
                                            ),
                                        maintainState: false));*/
                              }
                            },
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(200),
                              child: SizedBox(
                                width: 50,
                                height: 50,
                                child: CachedNetworkImage(
                                  imageUrl:
                                      '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                  height: 50,
                                  width: 50,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) =>
                                      SvgPicture.asset(
                                    'assets/images/default_user.svg',
                                    width: 50,
                                  ),
                                  errorWidget: (context, url, error) =>
                                      SvgPicture.asset(
                                    'assets/images/default_user.svg',
                                    width: 50,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Spacer(),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              InkWell(
                                  onTap: () {
                                    FirebaseAnalytics.instance.logEvent(
                                        name: 'community_page',
                                        parameters: {
                                          "wow_studio_click": Preference.getInt(
                                              Preference.USER_ID)!,
                                        });
                                    Navigator.push(
                                        context, NextPageRoute(WowStudio()));
                                  },
                                  child: APK_DETAILS['package_name'] ==
                                          'com.singularis.mesc'
                                      ? SvgPicture.asset(
                                          'assets/images/mesc_wow_studio.svg')
                                      : APK_DETAILS['package_name'] ==
                                              'com.singularis.jumeira'
                                          ? SvgPicture.asset(
                                              'assets/images/wow_studio_w.svg')
                                          : SvgPicture.asset(
                                              'assets/images/studio_new.svg')),
                              SizedBox(width: 14),
                              InkWell(
                                  onTap: () {
                                    FirebaseAnalytics.instance.logEvent(
                                        name: 'community_page',
                                        parameters: {
                                          "wow_studio_click": Preference.getInt(
                                              Preference.USER_ID)!,
                                        });
                                    Navigator.push(
                                        context,
                                        NextPageRoute(ReelScreen(),
                                            isMaintainState: true));
                                  },
                                  child: APK_DETAILS['package_name'] ==
                                          'com.singularis.jumeira'
                                      ? SvgPicture.asset(
                                          'assets/images/open_reel_w.svg')
                                      : SvgPicture.asset(
                                          'assets/images/reels_new.svg')),
                              SizedBox(width: 14),
                              Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(left: 6, top: 8)
                                    : EdgeInsets.only(right: 6, bottom: 8),
                                child: SizedBox(
                                  // flex: 2,
                                  child: Align(
                                    alignment: Utility().isRTL(context)
                                        ? Alignment.topLeft
                                        : Alignment.topRight,
                                    child: InkWell(
                                      onTap: () {
                                        _scaffoldKey.currentState
                                            ?.openEndDrawer();
                                      },
                                      child: SvgPicture.asset(
                                          'assets/images/hamburger_menu.svg'),
                                    ),
                                  ),
                                ),
                              ),
                              // InkWell(
                              //     onTap: () {
                              //       _scaffoldKey.currentState?.openEndDrawer();
                              //     },
                              //     child: SvgPicture.asset(
                              //         'assets/images/hamburger_menu.svg')),
                            ],
                          ),
                        ],
                      ),
                    ]))));
  }
}

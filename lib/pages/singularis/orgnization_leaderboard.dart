import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/top_score.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'point_history.dart';

class OrganizationLeaderboard extends StatefulWidget {
  final int? rank;
  final dynamic score;
  const OrganizationLeaderboard(
      {Key? key, required this.rank, required this.score})
      : super(key: key);

  @override
  State<OrganizationLeaderboard> createState() =>
      _OrganizationLeaderboardState();
}

class _OrganizationLeaderboardState extends State<OrganizationLeaderboard> {
  void getTopScoringUsers() {
    BlocProvider.of<HomeBloc>(context)
        .add(TopScoringUserEvent(userId: null, skipCurrentUser: false));
  }

  bool isTopScoringListLoading = true;
  late TopScoringResponse topScoringUsers;
  int currentUserIndex = -1;
  @override
  void initState() {
    getTopScoringUsers();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) async {
            if (state is TopScoringUserState) {
              _handleTopScoringUsersResponse(state);
            }
          },
          child: Scaffold(
            backgroundColor: ColorConstants.BG_GREY,
            appBar: AppBar(
              elevation: 0.0,
              flexibleSpace: Container(
                color: ColorConstants.WHITE,
              ),
              centerTitle: true,
              leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: Colors.black,
                  )),
              title: Text(
                'leaderboard',
                style: Styles.bold(),
              ).tr(),
            ),
            body: isTopScoringListLoading == true
                ? SizedBox()
                : SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: ColorConstants.WHITE,
                            borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(20),
                                bottomRight: Radius.circular(20)),
                            boxShadow: [
                              BoxShadow(
                                  color:
                                      Color(0xff898989).withValues(alpha: 0.1),
                                  offset: Offset(0, 4.0),
                                  blurRadius: 11)
                            ],
                          ),
                          height: height(context) * 0.35,
                          width: width(context),
                          child: isTopScoringListLoading == true ||
                                  topScoringUsers.data?.length == 0
                              ? Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    leadercard(null, '', 0, 0, 2),
                                    leadercard(null, '', 0, 0, 1),
                                    leadercard(null, '', 0, 0, 3),
                                  ],
                                )
                              : Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    topScoringUsers.data!.length > 1
                                        ? leadercard(
                                            '${topScoringUsers.data?[1]?.profileImage}',
                                            '${topScoringUsers.data?[1]?.name}',
                                            int.parse('12'),
                                            '${topScoringUsers.data?[1]?.score}',
                                            2)
                                        : leadercard(null, '', 0, 0, 2),
                                    leadercard(
                                        '${topScoringUsers.data?[0]?.profileImage}',
                                        '${topScoringUsers.data?[0]?.name}',
                                        int.parse('12'),
                                        '${topScoringUsers.data?[0]?.score}',
                                        1),
                                    topScoringUsers.data!.length > 2
                                        ? leadercard(
                                            '${topScoringUsers.data?[2]?.profileImage}',
                                            '${topScoringUsers.data?[2]?.name}',
                                            int.parse('12'),
                                            '${topScoringUsers.data?[2]?.score}',
                                            3)
                                        : leadercard(null, '', 0, 0, 3),
                                  ],
                                ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Text(
                                'your_rank',
                                style: TextStyle(
                                    fontSize: 15, fontWeight: FontWeight.w500),
                              ).tr(),
                            ],
                          ),
                        ),
                        Container(
                            margin: EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(
                                  color: ColorConstants().gradientRight()),
                              boxShadow: [
                                BoxShadow(
                                    blurRadius: 13,
                                    offset: Offset(0, 4),
                                    color: ColorConstants.BLACK
                                        .withValues(alpha: 0.2)),
                              ],
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10)),
                            ),
                            child: Container(
                              margin: EdgeInsets.symmetric(horizontal: 10),
                              child: ListTile(
                                contentPadding:
                                    EdgeInsets.symmetric(horizontal: 0),
                                visualDensity: VisualDensity(
                                  horizontal: -4,
                                ),
                                leading: SizedBox(
                                  width: width(context) * 0.2,
                                  child: Row(
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(left: 8.0),
                                        child: widget.rank != null
                                            ? Text(currentUserIndex == -1
                                                ? widget.rank == 0
                                                    ? '-- '
                                                    : '${widget.rank}. '
                                                : '${currentUserIndex + 1}. ')
                                            : SizedBox(),
                                      ),
                                      Preference.getString(Preference
                                                      .PROFILE_IMAGE) !=
                                                  null ||
                                              Preference.getString(Preference
                                                      .PROFILE_IMAGE) !=
                                                  ''
                                          ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(200),
                                              child: SizedBox(
                                                width: 40,
                                                child: Image.network(
                                                  '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                                  errorBuilder: ((context,
                                                          error, stackTrace) =>
                                                      SizedBox(
                                                        width: 40,
                                                        child: SvgPicture.asset(
                                                          'assets/images/default_user.svg',
                                                          height: 40,
                                                        ),
                                                      )),
                                                ),
                                              ),
                                            )
                                          : ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(200),
                                              child: SizedBox(
                                                  width: 40,
                                                  height: 40,
                                                  child: SvgPicture.asset(
                                                    'assets/images/default_user.svg',
                                                  )),
                                            ),

                                      //  ? CircleAvatar(
                                      //       backgroundColor: ColorConstants.GREY,
                                      //       backgroundImage: NetworkImage(
                                      //           "${Preference.getString(Preference.PROFILE_IMAGE)??''}"),
                                      //       onBackgroundImageError:
                                      //           ((exception, stackTrace) =>
                                      //               SvgPicture.asset(
                                      //                 'assets/images/default_user.svg',
                                      //                 height: 30,
                                      //                 width: 30,
                                      //                 allowDrawingOutsideViewBox:
                                      //                     true,
                                      //               ))): SvgPicture.asset(
                                      //                 'assets/images/default_user.svg',
                                      //                 height: 30,
                                      //                 width: 30,
                                      //                 allowDrawingOutsideViewBox:
                                      //                     true,
                                      //               ),
                                    ],
                                  ),
                                ),
                                title: Text(currentUserIndex == -1
                                    ? Utility().decrypted128(
                                        '${Preference.getString(Preference.FIRST_NAME)}')
                                    : Utility().decrypted128(
                                        '${topScoringUsers.data?[currentUserIndex]?.name}')),
                                trailing: SizedBox(
                                  width: width(context) * 0.18,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      if (currentUserIndex != -1) ...[
                                        SvgPicture.asset(
                                          'assets/images/coin.svg',
                                          width: width(context) * 0.07,
                                        ),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                      ],
                                      Text(
                                          "${topScoringUsers.data?.where((e) => e?.id == Preference.getInt(Preference.USER_ID)).isEmpty == true ? widget.score == 0 ? ' --' : ' ${widget.score ?? 0}' : topScoringUsers.data?.where((e) => e?.id == Preference.getInt(Preference.USER_ID)).first?.score ?? 0}"),
                                    ],
                                  ),
                                ),
                              ),
                            )),
                        isTopScoringListLoading == false
                            ? topScoringUsers.data!.length > 3
                                ? Column(
                                    children: [
                                      Container(
                                        color: Colors.white,
                                        margin:
                                            EdgeInsets.symmetric(vertical: 16),
                                        child: ListView.builder(
                                            physics:
                                                NeverScrollableScrollPhysics(),
                                            itemCount: max(
                                                0,
                                                topScoringUsers.data!.length -
                                                    3),
                                            shrinkWrap: true,
                                            itemBuilder: (BuildContext context,
                                                int index) {
                                              index = index + 3;
                                              return userCard(
                                                  name: topScoringUsers
                                                      .data?[index]?.name,
                                                  profileImg: topScoringUsers
                                                      .data?[index]
                                                      ?.profileImage,
                                                  index: index + 1,
                                                  coin: topScoringUsers
                                                      .data?[index]?.score,
                                                  totalAct: 2);
                                            }),
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                    ],
                                  )
                                : SizedBox()
                            : Container(
                                margin:
                                    EdgeInsets.only(top: height(context) * 0.4),
                                child: Text(
                                  'loading',
                                  style: Styles.regular(),
                                ).tr()),
                      ],
                    ),
                  ),
          ),
        ));
  }

  Widget userCard(
      {String? name,
      String? profileImg,
      int? index,
      dynamic coin,
      int? totalAct}) {
    return Container(
      color: ColorConstants.WHITE,
      margin: EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          if (index == 4)
            SizedBox(
              height: 11,
            ),
          ListTile(
              contentPadding: const EdgeInsets.all(2),
              visualDensity: VisualDensity(horizontal: -4, vertical: -4),
              leading: SizedBox(
                width: width(context) * 0.2,
                child: Row(
                  children: [
                    index! < 10
                        ? Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              "  $index.",
                              style: Styles.semibold(size: 14),
                            ),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              "$index.",
                              style: Styles.semibold(size: 14),
                            ),
                          ),
                    SizedBox(width: 8),
                    CircleAvatar(
                        onBackgroundImageError: ((exception, stackTrace) =>
                            SvgPicture.asset(
                              'assets/images/default_user.svg',
                              height: 30,
                              width: 30,
                              allowDrawingOutsideViewBox: true,
                            )),
                        backgroundColor: ColorConstants.GREY,
                        backgroundImage: NetworkImage("$profileImg")),
                  ],
                ),
              ),
              title: Text(
                Utility().decrypted128('$name'),
                style: Styles.semibold(size: 14),
              ),
              trailing: SizedBox(
                width: width(context) * 0.25,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/images/coin.svg',
                      width: width(context) * 0.07,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        right: 8.0,
                      ),
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => PointHistory()));
                        },
                        child: Text(
                          "${coin ?? 0}",
                          style: Styles.semibold(size: 12),
                        ),
                      ),
                    )
                  ],
                ),
              )),
          Divider()
        ],
      ),
    );
  }

  void _handleTopScoringUsersResponse(TopScoringUserState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isTopScoringListLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("TopScoringUsersState....................");
          topScoringUsers = state.response!;

          currentUserIndex = topScoringUsers.data!.indexWhere((e) {
            Log.v(
                'check oon ${e?.id} and ${Preference.getInt(Preference.USER_ID)}');
            return e?.id == Preference.getInt(Preference.USER_ID);
          });

          isTopScoringListLoading = false;
          break;
        case ApiStatus.ERROR:
          isTopScoringListLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'organization_leaderboard', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Widget leadercard(String? image, String title, dynamic activityCount,
      dynamic coinCount, int rank) {
    String? url;
    switch (rank) {
      case 1:
        url = 'assets/images/leader_first.svg';
        break;
      case 2:
        url = 'assets/images/leader_second.svg';
        break;
      case 3:
        url = 'assets/images/leader_third.svg';
        break;
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SvgPicture.asset('$url', width: rank == 1 ? 50 : 30),
        const SizedBox(
          height: 10,
        ),
        Container(
          decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                  width: 3,
                  color: rank == 1
                      ? Color(0xffF2A91E)
                      : rank == 2
                          ? Color(0xffCACACA)
                          : Color(0xffE0997A))),
          child: image != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(200),
                  child: SizedBox(
                    width: rank == 1 ? 100 : 70,
                    child: Image.network(
                      image,
                      errorBuilder: ((context, error, stackTrace) => SizedBox(
                            width: rank == 1 ? 100 : 70,
                            child: SvgPicture.asset(
                              'assets/images/default_user.svg',
                              height: rank == 1 ? 100 : 70,
                            ),
                          )),
                    ),
                  ),
                )
              : ClipRRect(
                  borderRadius: BorderRadius.circular(200),
                  child: SizedBox(
                      width: rank == 1 ? 100 : 70,
                      height: rank == 1 ? 100 : 70,
                      child: SvgPicture.asset(
                        'assets/images/default_user.svg',
                        height: rank == 1 ? 100 : 70,
                      ))),
        ),
        const SizedBox(
          height: 5,
        ),
        SizedBox(
          width: width(context) * 0.3,
          child: Text(Utility().decrypted128('$title'),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Styles.semibold(
                size: 12,
                color: ColorConstants.DASHBOARD_APPLY_COLOR,
              )),
        ),
        Text("${tr('rank')} $rank",
            style: Styles.semibold(
              size: 12,
              color: ColorConstants().gradientRight(),
            )),
        // Text(
        //   "$activityCount ${tr('activities')}",
        //   style: Styles.regular(size: 10, color: Color(0xff5A5F73)),
        // ),
        Row(
          children: [
            SvgPicture.asset(
              'assets/images/coin.svg',
              width: width(context) * 0.05,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              "$coinCount",
              style: Styles.regular(size: 12),
            ),
          ],
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}

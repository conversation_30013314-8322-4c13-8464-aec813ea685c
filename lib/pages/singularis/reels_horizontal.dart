import 'dart:typed_data';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/greels_response.dart';
import 'package:masterg/data/providers/reels_proivder.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_post_provider.dart';
import 'package:masterg/pages/reels/reel_screen.dart';
import 'package:masterg/pages/reels/theme/colors.dart';
import 'package:masterg/pages/reels/widgets/left_panel.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:masterg/utils/widget_size.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:visibility_detector/visibility_detector.dart';

TabController? _tabController;

class ReelHorizontal extends StatefulWidget {
  @override
  _ReelHorizontalState createState() => _ReelHorizontalState();
}

class _ReelHorizontalState extends State<ReelHorizontal>
    with TickerProviderStateMixin {
  bool isGReelsLoading = true;
  List<GReelsElement>? greelsList;

  //Box box;

  @override
  void initState() {
    super.initState();
    _getGReels();
    // _tabController = TabController(length: 0, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();
    _tabController?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MultiProvider(
          providers: [
            ChangeNotifierProvider<CreatePostProvider>(
              create: (context) => CreatePostProvider([], false),
            ),
            ChangeNotifierProvider<ReelsProvider>(
              create: (context) => ReelsProvider(false, false),
            ),
            ChangeNotifierProvider<GReelsModel>(
              create: (context) => GReelsModel(greelsList),
            ),
          ],
          child: BlocManager(
              initState: (context) {},
              child: Consumer2<GReelsModel, MenuListProvider>(
                builder: (context, greelsModel, menuProvider, child) =>
                    BlocListener<HomeBloc, HomeState>(
                  listener: (context, state) async {
                    if (state is GReelsPostState) {
                      _handleGReelsResponse(state, greelsModel);
                    }
                  },
                  child: getBody(greelsModel, menuProvider),
                ),
              ))),
    );
  }

  Widget getBody(GReelsModel greelsList, MenuListProvider menuProvider) {
    var size = MediaQuery.of(context).size;

    if (greelsList.list == null || isGReelsLoading) {
      return ListView(
        scrollDirection: Axis.horizontal,
        children: [
          reelsShimmer(size),
          SizedBox(
            width: 10,
          ),
          reelsShimmer(size),
        ],
      );
    }

    return Container(
      child: ListView.builder(
          itemCount: greelsList.list!.length,
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) {
            return Container(
              height: size.height * 0.2,
              margin: EdgeInsets.only(right: 10, left: 10, top: 4, bottom: 4),
              child: SizedBox(
                  height: 280,
                  width: 180,
                  child: InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ReelScreen(
                                      fromDashboard: true,
                                      scrollTo: index,
                                    )));
                      },
                      child: ShowImage(
                          path: greelsList.list![index].resourcePath))),
            );
          }),
    );
  }

  void _getGReels() async {
    BlocProvider.of<HomeBloc>(context).add(GReelsPostEvent());
  }

  void _handleGReelsResponse(GReelsPostState state, GReelsModel greelsModel) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isGReelsLoading = true;
          break;
        case ApiStatus.SUCCESS:
          greelsList = state.response!.data!.list;
          greelsModel.refreshList(greelsList!);
          Log.v("ReelsUsersState.................... ${greelsList?.length}");
          isGReelsLoading = false;
          break;
        case ApiStatus.ERROR:
          isGReelsLoading = false;

          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Widget reelsShimmer(size) {
    return Shimmer.fromColors(
      baseColor: Color(0xffe6e4e6),
      highlightColor: Color(0xffeaf0f3),
      child: Container(
        height: size.height * 0.2,
        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        width: MediaQuery.of(context).size.width * 0.4,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(6)),
      ),
    );
  }
}

class VideoPlayerItem extends StatefulWidget {
  final String? videoUrl;
  final String? name;
  final String? caption;
  final String? profileImg;
  final int? likes;
  final int? comments;
  final String? shares;
  final String? albumImg;
  final bool? isLiked;
  final int? contentId;
  final int? viewCount;
  final String? createdAt;
  final GReelsModel? greelsModel;
  final int? index;
  final int? userID;
  final String? userStatus;
  VideoPlayerItem(
      {Key? key,
      required this.size,
      this.name,
      this.caption,
      this.profileImg,
      this.likes,
      this.comments,
      this.shares,
      this.albumImg,
      this.videoUrl,
      this.isLiked,
      this.contentId,
      this.viewCount,
      this.createdAt,
      this.index,
      this.userID,
      this.userStatus,
      this.greelsModel})
      : super(key: key);

  final Size size;

  @override
  _VideoPlayerItemState createState() => _VideoPlayerItemState();
}

class _VideoPlayerItemState extends State<VideoPlayerItem>
    with WidgetsBindingObserver {
  VideoPlayerController? _videoController;
  bool isShowPlaying = false;

  @override
  void initState() {
    super.initState();

    _videoController =
        VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl!));
    _videoController!.addListener(() {
      // setState(() {});
    });
    _videoController!.setLooping(true);
    _videoController!.initialize().then((_) => setState(() {
          setState(() {
            isShowPlaying = true;
          });
          _videoController!.play();
        }));

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    _videoController!.dispose();

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) if (mounted)
      setState(() {
        _videoController!.pause();
      });
  }

  Widget isPlaying() {
    return _videoController!.value.isPlaying
        ? Container()
        : Icon(
            Icons.play_arrow,
            size: 80,
            color: white.withValues(alpha: 0.5),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReelsProvider>(
      builder: (context, value, child) {
        if (value.isPaused)
          _videoController!.pause();
        else {
          _videoController!.play();
        }

        return Material(
          child: InkWell(
            onTap: () {
              setState(() {
                value.showVolumnIcon();
                if (value.isMuted) {
                  _videoController?.setVolume(1.0);
                  value.unMute();
                } else {
                  _videoController?.setVolume(0.0);

                  value.mute();
                }
                new Future.delayed(Duration(seconds: 1), () {
                  value.hideVolumneIcon();
                });
              });
            },
            child: RotatedBox(
              quarterTurns: -1,
              child: Container(
                  height: widget.size.height,
                  width: widget.size.width,
                  child: Stack(
                    children: <Widget>[
                      Container(
                        height: widget.size.height,
                        width: widget.size.width,
                        decoration: BoxDecoration(color: black),
                        child: Stack(
                          children: <Widget>[
                            isShowPlaying
                                ? VisibilityDetector(
                                    key: ObjectKey(_videoController),
                                    onVisibilityChanged: (visibility) {
                                      var visiblePercentage =
                                          visibility.visibleFraction * 100;
                                      if (visiblePercentage.round() <= 60 &&
                                          this.mounted) {
                                        setState(() {
                                          _videoController!.pause();
                                        }); //pausing  functionality
                                      } else {
                                        if (this.mounted) if (this.mounted)
                                          setState(() {
                                            _videoController!.play();
                                          });
                                      }
                                    },
                                    child: VideoPlayer(_videoController!))
                                : ShowImage(path: widget.videoUrl),
                            Center(
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                    color: value.volumneIconInView
                                        ? ColorConstants.BLACK
                                            .withValues(alpha: 0.5)
                                        : Colors.transparent,
                                    shape: BoxShape.circle),
                                child: value.isMuted
                                    ? Icon(Icons.volume_off_outlined,
                                        color: value.volumneIconInView
                                            ? ColorConstants.WHITE
                                            : Colors.transparent)
                                    : Icon(Icons.volume_up,
                                        color: value.volumneIconInView
                                            ? ColorConstants.WHITE
                                            : Colors.transparent),
                              ),
                            )
                          ],
                        ),
                      ),
                      Container(
                        //height: widget.size.height,
                        width: widget.size.width,

                        child: Padding(
                          padding:
                              const EdgeInsets.only(left: 0, top: 0, bottom: 0),
                          child: SafeArea(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                Expanded(
                                    child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      stops: [0.81, 1.0],
                                      colors: [
                                        Colors.black.withValues(alpha: 0.0),
                                        Colors.black.withValues(alpha: 0.4),
                                      ],
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 10, top: 0, bottom: 15),
                                    child: Stack(
                                      children: <Widget>[
                                        LeftPanel(
                                          size: widget.size,
                                          userStatus: widget.userStatus,
                                          name: "${widget.name}",
                                          caption: "${widget.caption}",
                                          viewCounts: widget.viewCount,
                                          createdAt: widget.createdAt,
                                          profileImg: "${widget.profileImg}",
                                          expanded: (value) {},
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(left: 20.0),
                                          child: RightPanel(
                                              mContext: context,
                                              size: widget.size,
                                              likes: widget.likes,
                                              comments: "${widget.comments}",
                                              shares: "${widget.shares}",
                                              profileImg:
                                                  "${widget.profileImg}",
                                              albumImg: "${widget.albumImg}",
                                              isLiked: widget.isLiked,
                                              contentId: widget.contentId,
                                              greelsModel: widget.greelsModel,
                                              index: widget.index,
                                              userID: widget.userID),
                                        )
                                      ],
                                    ),
                                  ),
                                ))
                              ],
                            ),
                          ),
                        ),
                      )
                    ],
                  )),
            ),
          ),
        );
      },
    );
  }
}

class RightPanel extends StatefulWidget {
  final BuildContext mContext;
  final int? likes;
  final String? comments;
  final String? shares;
  final String? profileImg;
  final String? albumImg;
  final bool? isLiked;
  final int? contentId;
  final int? index;
  final int? userID;
  final GReelsModel? greelsModel;
  final GReelsModel? joyContentModel;

  const RightPanel(
      {Key? key,
      required this.size,
      this.likes,
      this.comments,
      this.shares,
      this.profileImg,
      this.albumImg,
      this.isLiked,
      this.contentId,
      this.greelsModel,
      this.index,
      this.userID,
      required this.mContext,
      this.joyContentModel})
      : super(key: key);

  final Size size;

  @override
  State<RightPanel> createState() => _RightPanelState();
}

class _RightPanelState extends State<RightPanel> with TickerProviderStateMixin {
  void deletePost(int? postId) {
    BlocProvider.of<HomeBloc>(context).add(DeletePostEvent(postId: postId));
  }

  void reportPost(
      String? status, int? postId, String category, String comment) {
    BlocProvider.of<HomeBloc>(context).add(ReportEvent(
        status: status, postId: postId, comment: comment, category: category));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: <Widget>[
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              SizedBox(
                height: 20,
              ),
              LikeWidget(
                mcontext: widget.mContext,
                contentId: widget.contentId!,
              ),
              SizedBox(
                height: 18,
              ),
              InkWell(
                  onTap: () {
                    Utility.shortLink(
                            '${widget.greelsModel?.getResourcePath(widget.contentId!)}')
                        .then((value) {
                      SharePlus.instance.share(ShareParams(text: value));
                      FirebaseAnalytics.instance
                          .logEvent(name: 'share_url', parameters: {
                        "type": "reel_share",
                      });
                    });
                  },
                  child: SvgPicture.asset(
                    'assets/images/share_icon_reels.svg',
                    height: 40,
                    width: 40,
                    colorFilter:
                        ColorFilter.mode(ColorConstants.WHITE, BlendMode.srcIn),
                    allowDrawingOutsideViewBox: true,
                  )),
              SizedBox(
                height: 18,
              ),
              GestureDetector(
                onTap: () async {
                  bool reportPostFormEnabled = false;
                  // bool reportInprogress = false;
                  await showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.black,
                      builder: (context) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Center(
                              child: Container(
                                padding: EdgeInsets.all(10),
                                margin: EdgeInsets.only(top: 10),
                                height: 4,
                                width: 70,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8)),
                              ),
                            ),
                            Container(
                              child: Column(
                                children: [
                                  ListTile(
                                    leading: new Icon(
                                      Icons.report,
                                      color: Colors.white,
                                    ),
                                    title: new Text(
                                      'report_post',
                                      style: TextStyle(color: Colors.white),
                                    ).tr(),
                                    onTap: () {
                                      setState(() {
                                        reportPostFormEnabled = true;
                                      });
                                      return Navigator.pop(context);
                                    },
                                  ),
                                  Container(
                                    child: ListTile(
                                      leading: new Icon(
                                        Icons.hide_image_outlined,
                                        color: Colors.white,
                                      ),
                                      title: new Text(
                                        'Remove_Hide_post',
                                        style: TextStyle(color: Colors.white),
                                      ).tr(),
                                      onTap: () {
                                        reportPost(
                                            'remove', widget.contentId, '', '');
                                        widget.greelsModel?.hidePost(
                                            widget.index,
                                            RemoveType.removehide);
                                        if (widget.index == 0) {
                                          Future.delayed(
                                                  Duration(milliseconds: 500))
                                              .then((value) => setState(() {
                                                    _tabController
                                                        ?.animateTo(1);
                                                  }));
                                        }

                                        return Navigator.pop(context);
                                      },
                                    ),
                                  ),
                                  if (Preference.getInt(Preference.USER_ID) ==
                                      widget.userID)
                                    ListTile(
                                      leading: new Icon(
                                        Icons.delete,
                                        color: Colors.white,
                                      ),
                                      title: new Text(
                                        'Delete_post',
                                        style: TextStyle(color: Colors.white),
                                      ).tr(),
                                      onTap: () {
                                        Navigator.pop(context);

                                        AlertsWidget.showCustomDialog(
                                            context: context,
                                            title: tr('delete_post'),
                                            text:
                                                tr('confirm_deletion_textone'),
                                            icon:
                                                'assets/images/circle_alert_fill.svg',
                                            onOkClick: () async {
                                              deletePost(widget.contentId);
                                              widget.greelsModel?.hidePost(
                                                  widget.index,
                                                  RemoveType.delete);
                                              if (widget.index == 0) {
                                                await Future.delayed(Duration(
                                                        milliseconds: 500))
                                                    .then((value) =>
                                                        setState(() {
                                                          _tabController
                                                              ?.animateTo(1);
                                                        }));
// Future.delayed(Duration(milliseconds: 1000)).then((value) => setState((){
//   _tabController?.animateTo(0);
// }));
                                              }
                                            });
                                      },
                                    ),
                                ],
                              ),
                            ),
                          ],
                        );
                      });

                  void _handleReport(ReportState state) {
                    var reportState = state;
                    setState(() {
                      switch (reportState.apiState) {
                        case ApiStatus.LOADING:
                          Log.v(
                              "ContentReportState Loading....................");
                          // reportInprogress = true;
                          break;
                        case ApiStatus.SUCCESS:
                          Log.v("ContentReportState....................");
                          Navigator.pop(context);
                          widget.greelsModel
                              ?.hidePost(widget.index, RemoveType.report);

                          if (widget.index == 0) {
                            Future.delayed(Duration(milliseconds: 500))
                                .then((value) => setState(() {
                                      _tabController?.animateTo(1);
                                    }));
                          }

                          Utility.showSnackBar(
                              scaffoldContext: context,
                              message: '${reportState.response?.message}');
                          // reportInprogress = false;
                          break;
                        case ApiStatus.ERROR:
                          Log.v("ContentReportState error....................");
                          // reportInprogress = false;
                          FirebaseAnalytics.instance
                              .logEvent(name: 'reels_screen', parameters: {
                            "ERROR": '${reportState.response?.message}',
                          });
                          break;
                        case ApiStatus.INITIAL:
                          break;
                      }
                    });
                  }

                  if (reportPostFormEnabled) {
                    bool showTextField = false;
                    TextEditingController reportController =
                        TextEditingController();
                    List<dynamic> reportList = Utility.getReportList();
                    showModalBottomSheet(
                        context: context,
                        backgroundColor: Colors.black,
                        builder: (BuildContext context) {
                          return FractionallySizedBox(
                            heightFactor: 1,
                            child: BlocManager(
                              initState: (BuildContext context) {},
                              child: BlocListener<HomeBloc, HomeState>(
                                listener: (BuildContext context, state) {
                                  if (state is ReportState) {
                                    _handleReport(state);
                                  }
                                },
                                child: BottomSheet(
                                    onClosing: () {},
                                    builder: (BuildContext context) {
                                      return StatefulBuilder(
                                        builder:
                                            (BuildContext context, setState) =>
                                                SingleChildScrollView(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: <Widget>[
                                              Center(
                                                child: Container(
                                                  padding: EdgeInsets.all(10),
                                                  margin:
                                                      EdgeInsets.only(top: 10),
                                                  height: 4,
                                                  width: 70,
                                                  decoration: BoxDecoration(
                                                      color:
                                                          ColorConstants.GREY_4,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8)),
                                                ),
                                              ),
                                              Center(
                                                child: Text(
                                                  'report',
                                                  style: Styles.bold(),
                                                ).tr(),
                                              ),
                                              Divider(),
                                              Text(tr('reporting_this_post'),
                                                  style: Styles.regular(
                                                      color: ColorConstants
                                                          .WHITE)),
                                              if (showTextField == false)
                                                SingleChildScrollView(
                                                  child: Column(
                                                    children: [
                                                      ListView.builder(
                                                          physics:
                                                              BouncingScrollPhysics(),
                                                          shrinkWrap: true,
                                                          itemCount:
                                                              reportList.length,
                                                          itemBuilder:
                                                              (BuildContext
                                                                      context,
                                                                  int index) {
                                                            return ListTile(
                                                                onTap: () {
                                                                  reportPost(
                                                                      'offensive',
                                                                      widget
                                                                          .contentId,
                                                                      '${reportList[index]['value']}',
                                                                      reportController
                                                                          .value
                                                                          .text);
                                                                },
                                                                title: Text(
                                                                    '${reportList[index]['title']}'));
                                                          }),
                                                      ListTile(
                                                          onTap: () {
                                                            setState(() {
                                                              showTextField =
                                                                  true;
                                                            });
                                                          },
                                                          title: Text(
                                                                  'something_else')
                                                              .tr()),
                                                    ],
                                                  ),
                                                ),
                                              if (showTextField == true)
                                                Container(
                                                  margin: EdgeInsets.symmetric(
                                                      horizontal: 14,
                                                      vertical: 8),
                                                  child: SingleChildScrollView(
                                                    child: Column(
                                                      children: [
                                                        TextFormField(
                                                          controller:
                                                              reportController,
                                                          style: Styles.bold(
                                                            size: 14,
                                                          ),
                                                          decoration:
                                                              InputDecoration(
                                                            hintText: tr(
                                                                'Trying_report_this_post'),
                                                            isDense: true,
                                                            helperStyle: Styles.regular(
                                                                size: 12,
                                                                color: ColorConstants
                                                                    .GREY_3
                                                                    .withValues(
                                                                        alpha:
                                                                            0.1)),
                                                            counterText: "",
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          height: 20,
                                                        ),
                                                        InkWell(
                                                            onTap: () {
                                                              reportPost(
                                                                  'offensive',
                                                                  widget
                                                                      .contentId,
                                                                  '',
                                                                  reportController
                                                                      .value
                                                                      .text);
                                                            },
                                                            child: Container(
                                                              margin: EdgeInsets
                                                                  .symmetric(
                                                                      vertical:
                                                                          12),
                                                              width: double
                                                                  .infinity,
                                                              height: MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .height *
                                                                  WidgetSize
                                                                      .AUTH_BUTTON_SIZE,
                                                              decoration: BoxDecoration(
                                                                  color: ColorConstants()
                                                                      .buttonColor(),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              10)),
                                                              child: Center(
                                                                  child: Text(
                                                                'submit',
                                                                style: Styles
                                                                    .regular(
                                                                  color:
                                                                      ColorConstants
                                                                          .WHITE,
                                                                ),
                                                              ).tr()),
                                                            )),
                                                      ],
                                                    ),
                                                  ),
                                                )
                                            ],
                                          ),
                                        ),
                                      );
                                    }),
                              ),
                            ),
                          );
                        });
                  }
                },
                child: Icon(
                  Icons.more_vert,
                  color: ColorConstants.WHITE,
                  size: 40,
                ),
              ),
            ],
          ))
        ],
      ),
    );
  }
}

class LikeWidget extends StatelessWidget {
  final BuildContext? mcontext;
  final int contentId;
  const LikeWidget({Key? key, required this.contentId, this.mcontext})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var joyContentModel = Provider.of<GReelsModel>(mcontext!);
    updateLikeandViews(null);

    bool isLiked = joyContentModel.isUserLiked(contentId);
    return InkWell(
      onTap: () {
        if (isLiked) {
          updateLikeandViews(0);
          joyContentModel.decreaseLikeCount(contentId);
        } else {
          updateLikeandViews(1);

          joyContentModel.increaseLikeCount(contentId);
        }
        joyContentModel
            .updateCurrentIndex(joyContentModel.getCurrentPostIndex(contentId));
      },
      child: Container(
        child: Column(
          children: <Widget>[
            isLiked
                ? SvgPicture.asset(
                    'assets/images/greels_liked.svg',
                    height: 40.0,
                    width: 40.0,
                    allowDrawingOutsideViewBox: true,
                  )
                : SvgPicture.asset(
                    'assets/images/greels_like.svg',
                    height: 40.0,
                    width: 40.0,
                    allowDrawingOutsideViewBox: true,
                  ),
            SizedBox(
              height: 5,
            ),
            Text(
              '${joyContentModel.getLikeCount(contentId)}',
              style: Styles.regular(size: 12, color: ColorConstants.WHITE),
            )
          ],
        ),
      ),
    );
  }

  void updateLikeandViews(int? like) async {
    BlocProvider.of<HomeBloc>(mcontext!).add(
        LikeContentEvent(contentId: contentId, like: like, type: 'contents'));
  }
}

class ShowImage extends StatefulWidget {
  final String? path;
  ShowImage({Key? key, this.path}) : super(key: key);

  @override
  State<ShowImage> createState() => _ShowImageState();
}

class _ShowImageState extends State<ShowImage> {
  Uint8List? imageFile;
  @override
  void initState() {
    super.initState();
    getFile();
  }

  Future<Uint8List?> getFile() async {
    final uint8list = await VideoThumbnail.thumbnailData(
      video: widget.path!,
      imageFormat: ImageFormat.JPEG,
      timeMs: Duration(seconds: 1).inMilliseconds,
    );
    if (this.mounted)
      setState(() {
        imageFile = uint8list;
      });
    return uint8list;
  }

  @override
  Widget build(BuildContext context) {
    return imageFile != null
        ? Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.memory(
                  imageFile!,
                  fit: BoxFit.cover,
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                ),
              ),
              Center(
                child: SvgPicture.asset(
                  'assets/images/play.svg',
                  height: 40.0,
                  width: 40.0,
                  allowDrawingOutsideViewBox: true,
                ),
              ),
            ],
          )
        : Shimmer.fromColors(
            baseColor: Color(0xffe6e4e6),
            highlightColor: Color(0xffeaf0f3),
            child: Container(
              height: MediaQuery.of(context).size.height * 0.2,
              margin: EdgeInsets.symmetric(horizontal: 10),
              width: MediaQuery.of(context).size.width * 0.4,
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(6)),
            ),
          );
  }
}

import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/pages/event/event_console_page.dart';
import 'package:masterg/pages/onboarding_pages/onboarding_select_intreset.dart';
import 'package:masterg/pages/singularis/second_menu/company.dart';
import 'package:masterg/pages/singularis/second_menu/employability.dart';
import 'package:masterg/pages/singularis/second_menu/event_admin_consol.dart';
import 'package:masterg/pages/singularis/second_menu/job_opportunity.dart';
import 'package:masterg/pages/singularis/second_menu/portfolios.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/size_constants.dart';
import 'package:masterg/utils/utility.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:simple_barcode_scanner/simple_barcode_scanner.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../data/api/api_constants.dart';
import '../../data/models/response/auth_response/user_session.dart';
import '../../local/pref/Preference.dart';
import '../../routes/app_link_route.dart';
import '../../utils/Styles.dart';
import '../../utils/convert_icons.dart';
import '../../utils/resource/colors.dart';
import '../auth_pages/forget_password.dart';
import '../custom_pages/alert_widgets/alerts_widget.dart';
import '../custom_pages/custom_widgets/CommonWebView.dart';
import '../custom_pages/custom_widgets/NextPageRouting.dart';
import '../drawer_page/setting_and_privacy_page.dart';
import '../mecate/mecate_login_state_page.dart';
import '../notifications/notification_list_page.dart';

class AppDrawer extends StatefulWidget {
  final Function? onActionFinish;

  const AppDrawer({super.key, this.onActionFinish});
  @override
  _AppDrawerState createState() => new _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  bool? isMaintainState = false;
  String? appVersion;
  final phoneController = TextEditingController();
  int? selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    getAppVersion();
  }

  void getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      appVersion = packageInfo.version;
    });
  }

  _launchURL(String strUrl) async {
    final Uri url = Uri.parse(strUrl);

    if (Platform.isIOS) {
      if (!await launchUrl(url)) {
        throw Exception('Could not launch $url');
      }
    } else {
      if (await canLaunchUrl(url)) {
        await launchUrl(
          url,
          mode: LaunchMode.externalApplication,
        );
      }
    }
  }

  final Map<String, Widget Function(BuildContext)> pages = {
    '/p_coordinators': (context) => EmployabilityMenuPage(),
    '/event_console': (context) => EventConsolePage(appBarEnable: true),
    '/company': (context) => CompanyMenuPage(),
    '/list-portfolio': (context) => PortfoliosMenuPage(),
    '/job-opportunity-dashboard': (context) => JobOpportunityMenuPage(),
    '/manager-events': (context) => EventAdminConsolePage(),
    '/faculty-events': (context) => EventConsolePage(appBarEnable: true),
  };

  Map<String, Icon> menuIcons = {
    '/p_coordinators': Icon(
      Icons.work,
      color: Colors.white,
      size: 20,
    ),
    '/event_console': Icon(
      Icons.event_note,
      color: Colors.white,
      size: 20,
    ),
    '/company': Icon(
      Icons.corporate_fare,
      color: Colors.white,
      size: 20,
    ),
    '/list-portfolio': Icon(
      Icons.work,
      color: Colors.white,
      size: 20,
    ),
    '/job-opportunity-dashboard': Icon(
      Icons.travel_explore_outlined,
      color: Colors.white,
      size: 20,
    ),
    '/manager-events': Icon(
      Icons.folder_special,
      color: Colors.white,
      size: 20,
    ),
    '/faculty-events': Icon(
      Icons.school,
      color: Colors.white,
      size: 20,
    ),
  };

  Map<String, Color> menuColor = {
    '/p_coordinators': Color(0xFF006989),
    '/event_console': Color(0xFF5C88C4),
    '/company': Color(0xFF9B86BD),
    '/list-portfolio': Color(0xFFADD8E6),
    '/job-opportunity-dashboard': Color(0xFF607D8B),
    '/manager-events': Color(0xFF808080),
    '/faculty-events': Color(0xFF607D8B),
  };

  @override
  Widget build(BuildContext context) {
    MenuListProvider menuProvider = Provider.of(context);

    return new Drawer(
      backgroundColor: Color(0xffF5F5F5),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topRight: Radius.circular(0), bottomRight: Radius.circular(0)),
      ),
      child: ListView(children: <Widget>[
        DrawerHeader(
          margin: EdgeInsets.all(0.0),
          padding: EdgeInsets.all(0.0),
          child: new Container(
            height: 130,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(0),
                  bottomRight: Radius.circular(0)),
            ),
            child: Container(
              width: double.infinity,
              color: APK_DETAILS['package_name'] == 'com.singulariswow.mec' ||
                      APK_DETAILS['package_name'] == 'com.singularis.mesc' ||
                      APK_DETAILS['package_name'] == 'com.singularis.jumeira'
                  ? Colors.white
                  : null,
              padding: const EdgeInsets.symmetric(horizontal: 22),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (APK_DETAILS['package_name'] !=
                      'com.singulariswow.aid') ...[
                    APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                        ? SvgPicture.asset(
                            height: height(context) * 0.06,
                            width: width(context) * 0.5,
                            'assets/images/mec_login_logo.svg',
                          )
                        : APK_DETAILS['package_name'] != 'com.singularis.mesc'
                            ? SvgPicture.asset(
                                height: height(context) * 0.05,
                                width: width(context) * 0.4,
                                'assets/images/singularis_white.svg',
                                colorFilter: ColorFilter.mode(
                                    Colors.black, BlendMode.srcIn),
                              )
                            : SizedBox(),
                  ],
                  if (APK_DETAILS['package_name'] != 'com.singulariswow.mec' &&
                      APK_DETAILS['package_name'] != 'com.singulariswow') ...[
                    CachedNetworkImage(
                      imageUrl: '${Preference.getString(Preference.ORG_URL)}',
                      imageBuilder: (context, imageProvider) => Column(
                        children: [
                          if (APK_DETAILS['package_name'] !=
                                  'com.singulariswow.aid' &&
                              APK_DETAILS['package_name'] !=
                                  'com.singularis.mesc')
                            Divider(
                              color: ColorConstants.GREY_5,
                            ),
                          SizedBox(height: 10),
                          Container(
                            height: height(context) * 0.05,
                            width: width(context) * 0.5,
                            decoration: BoxDecoration(
                                image: DecorationImage(
                              image: imageProvider,
                              fit: BoxFit.contain,
                            )),
                          ),
                        ],
                      ),
                      placeholder: (context, url) => SizedBox(),
                      errorWidget: (context, url, error) => SizedBox(),
                    )
                  ],
                ],
              ),
            ),
          ),
        ),

        //Container 2
        //TODO: SIS Connect
        APK_DETAILS["college_modules"] == "1"
            ? Preference.getString(Preference.ROLE) == 'Learner'
                ? Container(
                    margin: const EdgeInsets.only(top: 15.0),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                        // borderRadius: BorderRadius.circular(20),
                        color: ColorConstants.WHITE),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 17.0, bottom: 17.0, right: 17.0),
                          child: Text(
                            'sis_connect',
                            style:
                                Styles.textBold(size: 16, color: Colors.grey),
                          ).tr(),
                        ),
                        ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.GREEN,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.account_circle_rounded,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'my_profile',
                          ).tr(),
                          onTap: () async {
                            Navigator.of(context).pop();
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TermsAndCondition(
                                          url:
                                              '${APK_DETAILS['my_profile_url']}' +
                                                  Preference.getInt(
                                                          Preference.USER_ID)
                                                      .toString(),
                                          title: tr('my_profile'),
                                        ),
                                    maintainState: false));
                          },
                        ),
                        Divider(),
                        ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.ORANGE,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.app_registration,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'module_registration',
                          ).tr(),
                          onTap: () async {
                            Navigator.of(context).pop();
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TermsAndCondition(
                                          //url: '${APK_DETAILS['module_registration']}'+Preference.getString(Preference.SSOTOKEN).toString(),
                                          url:
                                              '${APK_DETAILS['module_registration']}' +
                                                  Preference.getInt(
                                                          Preference.USER_ID)
                                                      .toString(),
                                          title: tr('module_registration'),
                                        ),
                                    maintainState: false));
                          },
                        ),
                        Divider(),
                        ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.UNSELECTED_PAGE,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.school_outlined,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'current_semester',
                          ).tr(),
                          onTap: () async {
                            print('${APK_DETAILS['current_sem']}' +
                                Preference.getInt(Preference.USER_ID)
                                    .toString());
                            Navigator.pop(context);
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TermsAndCondition(
                                          url: '${APK_DETAILS['current_sem']}' +
                                              Preference.getInt(
                                                      Preference.USER_ID)
                                                  .toString(),
                                          title: tr('current_semester'),
                                        ),
                                    maintainState: false));
                          },
                        ),
                        Divider(),
                        ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.YELLOW,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.next_plan_outlined,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'course_plan',
                          ).tr(),
                          onTap: () async {
                            print('url is ${APK_DETAILS['course_plan']}' +
                                Preference.getInt(Preference.USER_ID)
                                    .toString());
                            Navigator.pop(context);
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TermsAndCondition(
                                          url: '${APK_DETAILS['course_plan']}' +
                                              Preference.getInt(
                                                      Preference.USER_ID)
                                                  .toString(),
                                          title: tr('course_plan'),
                                        ),
                                    maintainState: false));
                          },
                        ),
                        Divider(),
                        ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.Color_5f6687,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.schedule_rounded,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'time_table',
                          ).tr(),
                          onTap: () async {
                            Navigator.of(context).pop();
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TermsAndCondition(
                                          url:
                                              '${APK_DETAILS['time_table_url']}' +
                                                  Preference.getInt(
                                                          Preference.USER_ID)
                                                      .toString(),
                                          title: tr('time_table'),
                                        ),
                                    maintainState: false));
                          },
                        ),
                        Divider(),
                        ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.ACTIVE_TAB_UNDERLINE,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.payments,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'fees',
                          ).tr(),
                          onTap: () async {
                            Navigator.of(context).pop();
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TermsAndCondition(
                                          url:
                                              '${APK_DETAILS['sis_fee_scholarship_url']}' +
                                                  Preference.getInt(
                                                          Preference.USER_ID)
                                                      .toString(),
                                          title: tr('fees'),
                                        ),
                                    maintainState: false));
                          },
                        ),
                        Divider(),
                        ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: Colors.green,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.payment,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'pay_fees_online',
                          ).tr(),
                          onTap: () async {
                            Navigator.of(context).pop();
                            _launchURL('${APK_DETAILS['pay_fees_online']}');
                            /* Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                  //url: '${APK_DETAILS['pay_fees_online']}',
                                  title: tr('pay_fees_online'),
                                ),
                                maintainState: false));*/
                          },
                        ),
                      ],
                    ),
                  )
                : Preference.getString(Preference.ROLE) == 'Presenter' ||
                        Preference.getString(Preference.ROLE) == 'Staff'
                    ? Container(
                        margin: const EdgeInsets.only(top: 15.0),
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        decoration: BoxDecoration(
                            // borderRadius: BorderRadius.circular(20),
                            color: ColorConstants.WHITE),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 17.0, bottom: 17.0, right: 17.0),
                              child: Text(
                                'hris_connect',
                                style: Styles.textBold(
                                    size: 16, color: Colors.grey),
                              ).tr(),
                            ),
                            ListTile(
                              dense: true,
                              trailing: Icon(
                                Icons.arrow_forward_ios,
                                color: ColorConstants.GREY_3,
                                size: 15,
                              ),
                              visualDensity:
                                  VisualDensity(vertical: -4, horizontal: -4),
                              leading: Container(
                                  width: 30,
                                  height: 30,
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.ORANGE,
                                      borderRadius: BorderRadius.circular(6)),
                                  child: Icon(
                                    Icons.holiday_village_outlined,
                                    color: ColorConstants.WHITE,
                                    size: 20,
                                  )),
                              title: Text(
                                'holidays',
                              ).tr(),
                              onTap: () async {
                                Navigator.of(context).pop();
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TermsAndCondition(
                                              url:
                                                  'https://mecfuture.mec.edu.om/hris/holidays?user_id=' +
                                                      Preference.getInt(
                                                              Preference
                                                                  .USER_ID)
                                                          .toString(),
                                              title: tr('holidays'),
                                            ),
                                        maintainState: false));
                              },
                            ),
                            Divider(),
                            ListTile(
                              dense: true,
                              trailing: Icon(
                                Icons.arrow_forward_ios,
                                color: ColorConstants.GREY_3,
                                size: 15,
                              ),
                              visualDensity:
                                  VisualDensity(vertical: -4, horizontal: -4),
                              leading: Container(
                                  width: 30,
                                  height: 30,
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.UNSELECTED_PAGE,
                                      borderRadius: BorderRadius.circular(6)),
                                  child: Icon(
                                    Icons.access_time,
                                    color: ColorConstants.WHITE,
                                    size: 20,
                                  )),
                              title: Text(
                                'scheduled_weekly',
                              ).tr(),
                              onTap: () async {
                                Navigator.pop(context);
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TermsAndCondition(
                                              //url: '${APK_DETAILS['current_sem']}'+Preference.getInt(Preference.USER_ID).toString(),
                                              url:
                                                  'https://mecfuture.mec.edu.om/hris/scheduled-weekly-timetable?user_id=' +
                                                      Preference.getInt(
                                                              Preference
                                                                  .USER_ID)
                                                          .toString(),
                                              title: tr('scheduled_weekly'),
                                            ),
                                        maintainState: false));
                              },
                            ),
                            Divider(),
                            ListTile(
                              dense: true,
                              trailing: Icon(
                                Icons.arrow_forward_ios,
                                color: ColorConstants.GREY_3,
                                size: 15,
                              ),
                              visualDensity:
                                  VisualDensity(vertical: -4, horizontal: -4),
                              leading: Container(
                                  width: 30,
                                  height: 30,
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.YELLOW,
                                      borderRadius: BorderRadius.circular(6)),
                                  child: Icon(
                                    Icons.calendar_month,
                                    color: ColorConstants.WHITE,
                                    size: 20,
                                  )),
                              title: Text(
                                'attendance_logs',
                              ).tr(),
                              onTap: () async {
                                Navigator.pop(context);
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TermsAndCondition(
                                              //url: '${APK_DETAILS['course_plan']}'+Preference.getInt(Preference.USER_ID).toString(),
                                              url:
                                                  'https://mecfuture.mec.edu.om/hris/attendancelogs?user_id=' +
                                                      Preference.getInt(
                                                              Preference
                                                                  .USER_ID)
                                                          .toString(),
                                              title: tr('attendance_logs'),
                                            ),
                                        maintainState: false));
                              },
                            ),
                            Divider(),
                            ListTile(
                              dense: true,
                              trailing: Icon(
                                Icons.arrow_forward_ios,
                                color: ColorConstants.GREY_3,
                                size: 15,
                              ),
                              visualDensity:
                                  VisualDensity(vertical: -4, horizontal: -4),
                              leading: Container(
                                  width: 30,
                                  height: 30,
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.Color_5f6687,
                                      borderRadius: BorderRadius.circular(6)),
                                  child: Icon(
                                    Icons.leave_bags_at_home,
                                    color: ColorConstants.WHITE,
                                    size: 20,
                                  )),
                              title: Text(
                                'my_leave',
                              ).tr(),
                              onTap: () async {
                                Navigator.of(context).pop();
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TermsAndCondition(
                                              //url: '${APK_DETAILS['time_table_url']}'+Preference.getInt(Preference.USER_ID).toString(),
                                              url:
                                                  'https://mecfuture.mec.edu.om/hris/myleave?user_id=' +
                                                      Preference.getInt(
                                                              Preference
                                                                  .USER_ID)
                                                          .toString(),
                                              title: tr('my_leave'),
                                            ),
                                        maintainState: false));
                              },
                            ),
                            Divider(),
                            ListTile(
                              dense: true,
                              trailing: Icon(
                                Icons.arrow_forward_ios,
                                color: ColorConstants.GREY_3,
                                size: 15,
                              ),
                              visualDensity:
                                  VisualDensity(vertical: -4, horizontal: -4),
                              leading: Container(
                                  width: 30,
                                  height: 30,
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.BG_BLUE_BTN,
                                      borderRadius: BorderRadius.circular(6)),
                                  child: Icon(
                                    Icons.schedule_rounded,
                                    color: ColorConstants.WHITE,
                                    size: 20,
                                  )),
                              title: Text(
                                'time_table',
                              ).tr(),
                              onTap: () async {
                                Navigator.of(context).pop();
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TermsAndCondition(
                                              url:
                                                  'https://mecfuture.mec.edu.om/time-table-webview/?user_id=' +
                                                      Preference.getInt(
                                                              Preference
                                                                  .USER_ID)
                                                          .toString(),
                                              title: tr('time_table'),
                                            ),
                                        maintainState: false));
                              },
                            ),
                            Divider(),
                            ListTile(
                              dense: true,
                              trailing: Icon(
                                Icons.arrow_forward_ios,
                                color: ColorConstants.GREY_3,
                                size: 15,
                              ),
                              visualDensity:
                                  VisualDensity(vertical: -4, horizontal: -4),
                              leading: Container(
                                  width: 30,
                                  height: 30,
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.Color_GREEN,
                                      borderRadius: BorderRadius.circular(6)),
                                  child: Icon(
                                    Icons.account_circle_rounded,
                                    color: ColorConstants.WHITE,
                                    size: 20,
                                  )),
                              title: Text(
                                'my_profile',
                              ).tr(),
                              onTap: () async {
                                Navigator.of(context).pop();
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TermsAndCondition(
                                              //url: '${APK_DETAILS['time_table_url']}'+Preference.getInt(Preference.USER_ID).toString(),
                                              url:
                                                  'https://mecfuture.mec.edu.om/hris/my-profile?user_id=' +
                                                      Preference.getInt(
                                                              Preference
                                                                  .USER_ID)
                                                          .toString(),
                                              title: tr('my_profile'),
                                            ),
                                        maintainState: false));
                              },
                            ),
                            // Divider(),
                            // ListTile(
                            //   dense: true,
                            //   trailing: Icon(
                            //     Icons.arrow_forward_ios,
                            //     color: ColorConstants.GREY_3,
                            //     size: 15,
                            //   ),
                            //   visualDensity:
                            //   VisualDensity(vertical: -4, horizontal: -4),
                            //   leading: Container(
                            //       width: 30,
                            //       height: 30,
                            //       padding: const EdgeInsets.all(2),
                            //       decoration: BoxDecoration(
                            //           color: ColorConstants.BG_DARK_BLUE_BTN,
                            //           borderRadius: BorderRadius.circular(6)),
                            //       child: Icon(
                            //         Icons.account_circle_rounded,
                            //         color: ColorConstants.WHITE,
                            //         size: 20,
                            //       )),
                            //   title: Text(
                            //     'user_search',
                            //   ).tr(),
                            //   onTap: () async {
                            //     Navigator.of(context).pop();
                            //     Navigator.push(
                            //         context,
                            //         MaterialPageRoute(
                            //             builder: (context) => TermsAndCondition(
                            //               url: '${APK_DETAILS['user_search']}'+Preference.getInt(Preference.USER_ID).toString(),
                            //               title: tr('user_search'),
                            //             ),
                            //             maintainState: false));
                            //   },
                            // ),
                          ],
                        ),
                      )
                    : SizedBox()
            : SizedBox(),

        //TODO: dynamic side menu
        ValueListenableBuilder(
          valueListenable: Hive.box("content").listenable(),
          builder: (bc, Box box, child) {
            if (box.get("sideMenu") == null) {
              return Text("Loading");
            } else if (box.get("sideMenu").isEmpty) {
              return SizedBox();
            }

            dynamic data = box.get("sideMenu");
            List<dynamic> menu = data.map((e) => Menu.fromJson(e)).toList();
            List<Widget> sideMenu = List.generate(
              menu.length,
              (index) {
                Menu item = menu[index];
                return Column(
                  children: [
                    ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      leading: item.image!.contains('http')
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: Image.network(
                                '${item.image}',
                                height: 35,
                                width: 35,
                              ),
                            )
                          : Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.GREEN,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                IconCode.getIcon('${item.image}'),
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                      title: Text(
                        '${item.label}',
                      ).tr(),
                      onTap: () async {
                        Navigator.of(context).pop();

                        if (item.url!.contains('http')) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TermsAndCondition(
                                        url: item.url,
                                        title: '${item.label}',
                                      ),
                                  maintainState: false));
                        } else {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TermsAndCondition(
                                        url: ApiConstants()
                                                .PRODUCTION_BASE_URL() +
                                            '${item.url}' +
                                            Preference.getInt(
                                                    Preference.USER_ID)
                                                .toString(),
                                        title: '${item.label}',
                                      ),
                                  maintainState: false));
                        }
                      },
                    ),
                    if (index + 1 != menu.length) Divider(),
                  ],
                );
              },
            );
            return sideMenu.length == 0
                ? SizedBox()
                : Container(
                    margin: const EdgeInsets.only(top: 15.0),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                        // borderRadius: BorderRadius.circular(20),
                        color: ColorConstants.WHITE),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 17.0, bottom: 17.0, right: 17.0),
                          child: Text(
                            'other_modules',
                            style:
                                Styles.textBold(size: 16, color: Colors.grey),
                          ).tr(),
                        ),
                        ListView(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          children: sideMenu,
                        ),
                      ],
                    ),
                  );
          },
        ),
        SizedBox(
          height: 15,
        ),

        APK_DETAILS["package_name"] == "com.singularis.mesc"
            ? Container(
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 14.0, bottom: 17.0, right: 17.0, top: 10.0),
                      child: Text(
                        'quick_links',
                        style: Styles.textBold(size: 16, color: Colors.grey),
                      ).tr(),
                    ),
                    ListTile(
                      dense: true,
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        //child: SvgPicture.asset('assets/images/mesc_digital.svg'),
                        child: Image.asset(
                          'assets/images/mesc_sidemenu.png',
                          height: 30,
                          width: 30,
                        ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      title: Text(
                        'mesc_m',
                      ).tr(),
                      onTap: () async {
                        Navigator.pop(context);
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: APK_DETAILS['mesc_url'],
                                      title: tr('mesc_m'),
                                    ),
                                maintainState: false));
                      },
                    ),
                    Preference.getInt(Preference.ENABLE_MECAT) == 1
                        ? Divider()
                        : SizedBox(),
                    Preference.getInt(Preference.ENABLE_MECAT) == 1
                        ? ListTile(
                            dense: true,
                            leading: ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              //child: SvgPicture.asset('assets/images/mecat_logo.svg'),
                              child: Image.asset(
                                'assets/images/mecat_sidemenu.png',
                                width: 30,
                                height: 30,
                              ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios,
                              color: ColorConstants.GREY_3,
                              size: 15,
                            ),
                            visualDensity:
                                VisualDensity(vertical: -4, horizontal: -4),
                            title: Text(
                              'mecat',
                            ).tr(),
                            onTap: () async {
                              Navigator.pop(context);

                              /*Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: APK_DETAILS['mecat_url'],
                                      title: tr('mecat'),
                                    ),
                                maintainState: false));*/
                              Navigator.push(
                                  context,
                                  NextPageRoute(MeCateLoginStatePage(
                                    title: tr('mecat'),
                                  )));
                            },
                          )
                        : SizedBox(),
                    Divider(),
                    ListTile(
                      dense: true,
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(0),
                        //child: Image.asset('assets/images/creative_warriors.png'),
                        child: Image.asset(
                          'assets/images/creative_war_sidemenu.png',
                          width: 30,
                          height: 30,
                        ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      title: Text(
                        'creative_warriors',
                      ).tr(),
                      onTap: () async {
                        Navigator.pop(context);
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: APK_DETAILS['creative_warriors_url'],
                                      title: tr('creative_warriors'),
                                    ),
                                maintainState: false));
                      },
                    ),
                    Divider(),
                    ListTile(
                      dense: true,
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.asset(
                          'assets/images/vidyadaan_sidemenu.png',
                          width: 30,
                          height: 30,
                        ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      title: Text(
                        'vidyadaan',
                      ).tr(),
                      onTap: () async {
                        Navigator.pop(context);
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: APK_DETAILS['vidyadaan_url'],
                                      title: tr('vidyadaan'),
                                    ),
                                maintainState: false));
                      },
                    ),
                    Divider(),
                    ListTile(
                      dense: true,
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.asset(
                          'assets/images/mediatalk_sidemenu.png',
                          width: 30,
                          height: 30,
                          errorBuilder: (context, error, stackTrace) {
                            return SizedBox();
                          },
                        ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      title: Text(
                        'media_talkback',
                      ).tr(),
                      onTap: () async {
                        Navigator.pop(context);
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: APK_DETAILS['media_talkback_url'],
                                      title: tr('media_talkback'),
                                    ),
                                maintainState: false));
                      },
                    ),
                    Divider(),
                    ListTile(
                      dense: true,
                      leading: Container(
                          width: 30,
                          height: 30,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                              color: ColorConstants.WHITE,
                              borderRadius: BorderRadius.circular(6)),
                          child: Icon(
                            Icons.handshake_outlined,
                            color: ColorConstants.GREY_3,
                            size: 20,
                          )),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      title: Text(
                        'academic_partners',
                      ).tr(),
                      onTap: () async {
                        Navigator.pop(context);
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: APK_DETAILS['academic_partners_url'],
                                      title: tr('academic_partners'),
                                    ),
                                maintainState: false));
                      },
                    ),
                    SizedBox(
                      height: 8,
                    )
                  ],
                ),
              )
            : SizedBox(),

        APK_DETAILS["package_name"] == "com.singularis.mesc"
            ? SizedBox(
                height: 20,
              )
            : SizedBox(),

        Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
              // borderRadius: BorderRadius.circular(20),
              color: ColorConstants.WHITE),
          child: Column(
            children: [
              /*APK_DETAILS["package_name"] == "com.singulariswow.mec"
                  ? Preference.getString(Preference.ROLE) == 'Presenter' ||
                          Preference.getString(Preference.ROLE) == 'Learner'
                      ? ListTile(
                          dense: true,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.ORANGE_3,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.book,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          title: Text(
                            'mec_learn',
                          ).tr(),
                          onTap: () async {
                            //Navigator.pop(context);
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => HtmlWebViewPage(
                                          //url: '${APK_DETAILS['module_registration']}'+Preference.getString(Preference.SSOTOKEN).toString(),
                                          //url: '${APK_DETAILS['module_registration']}'+Preference.getInt(Preference.USER_ID).toString(),
                                          title: tr('mec_learn'),
                                        ),
                                    maintainState: false));
                          },
                        )
                      : SizedBox()
                  : SizedBox(),*/
              // APK_DETAILS["package_name"] == "com.singulariswow.mec"
              //     ? Preference.getString(Preference.ROLE) == 'Presenter' ||
              //             Preference.getString(Preference.ROLE) == 'Learner'
              //         ? Divider()
              //         : SizedBox()
              //     : SizedBox(),
              Preference.getString(Preference.ROLE) == 'Presenter'
                  ? ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      leading: Container(
                        width: 30,
                        height: 30,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: ColorConstants.ORANGE,
                            borderRadius: BorderRadius.circular(6)),
                        child: SvgPicture.asset(
                            'assets/images/learning_console.svg',
                            height: 20),
                      ),
                      title: Text(
                        'learning_console',
                      ).tr(),
                      onTap: () async {
                        Navigator.of(context).pop();

                        menuProvider
                            .updateCurrentIndex('/faculty-batch-detail');
                      },
                    )
                  : SizedBox(),
              Preference.getString(Preference.ROLE) == 'Presenter'
                  ? Divider()
                  : SizedBox(),
              ListTile(
                dense: true,
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color: ColorConstants.GREY_3,
                  size: 15,
                ),
                visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                leading: Container(
                    width: 30,
                    height: 30,
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: ColorConstants.GREEN,
                        borderRadius: BorderRadius.circular(6)),
                    child: Icon(
                      Icons.interests,
                      color: ColorConstants.WHITE,
                      size: 20,
                    )),
                title: Text(
                  'my_interest',
                ).tr(),
                onTap: () async {
                  // Navigator.push(
                  //         context,
                  //         MaterialPageRoute(
                  //             builder: (context) => InterestPage()))
                  //     .then((isUpdated) {
                  //   if (isUpdated == true) {
                  //     menuProvider.updateCurrentIndex('/g-dashboard');
                  //   }
                  // });

                  Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) =>
                                  OnboardingSelecteInterestPage()))
                      .then((isUpdated) {
                    if (isUpdated == true) {
                      if (menuProvider.list![menuProvider.currentIndex!].url ==
                          '/g-dashboard') {
                        Navigator.pop(context);
                        widget.onActionFinish!();
                      } else
                        menuProvider.updateCurrentIndex('/g-dashboard');
                    }
                  });
                },
              ),
              Divider(),
              ListTile(
                dense: true,
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color: ColorConstants.GREY_3,
                  size: 15,
                ),
                visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                leading: Container(
                    width: 30,
                    height: 30,
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: ColorConstants().gradientLeft(),
                        borderRadius: BorderRadius.circular(6)),
                    child: Icon(
                      Icons.lock_outline,
                      color: ColorConstants.WHITE,
                      size: 20,
                    )),
                title: Text(
                  'change_password',
                ).tr(),
                onTap: () async {
                  String email =
                      Utility().decrypted128(UserSession.email.toString());
                  Navigator.pop(context);
                  if (APK_DETAILS["package_name"] == "com.singulariswow.mec") {
                    if (email.contains('mec.edu.om')) {
                      Navigator.push(
                          context,
                          NextPageRoute(CommonWebView(
                            url: APK_DETAILS["forgot_cange_pass"],
                          )));
                    } else {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ForgotPassword(
                                    beforeLogin: false,
                                  )));
                    }
                  } else {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ForgotPassword(
                                  beforeLogin: false,
                                )));
                  }
                },
              ),

              APK_DETAILS["qr_code_enable"] == "1" ? Divider() : SizedBox(),
              APK_DETAILS["qr_code_enable"] == "1"
                  ? ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      leading: Container(
                          width: 30,
                          height: 30,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                              color: Colors.lightGreen,
                              borderRadius: BorderRadius.circular(6)),
                          child: Icon(
                            Icons.qr_code_2,
                            color: ColorConstants.WHITE,
                            size: 20,
                          )),
                      title: Text(
                        'qr_code_scanner',
                      ).tr(),
                      onTap: () async {
                        //Navigator.push(context, MaterialPageRoute(builder: (context) => QRCodeScanner()));
                        var res =
                            await SimpleBarcodeScanner.scanBarcode(context);
                        Log.v('result is $res',
                            name: 'Simple Barcode Scanner Result');
                        if (res != null) {
                          if (res.toString().contains('mecfuture.mec')) {
                            AppLinkRoute.handleRoute(route: res);
                          } else {
                            Utility.showSnackBar(
                                scaffoldContext: context,
                                message: tr('scanning_msg'));
                          }
                        }
                        Navigator.pop(context);
                      },
                    )
                  : SizedBox(),

              APK_DETAILS["notification_enable"] == "1"
                  ? Column(
                      children: [
                        Divider(),
                        ListTile(
                          dense: true,
                          leading: Container(
                              width: 30,
                              height: 30,
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: ColorConstants.ORANGE,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Icon(
                                Icons.notifications_active_outlined,
                                color: ColorConstants.WHITE,
                                size: 20,
                              )),
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: ColorConstants.GREY_3,
                            size: 15,
                          ),
                          visualDensity:
                              VisualDensity(vertical: -4, horizontal: -4),
                          title: Text(
                            'notification',
                          ).tr(),
                          onTap: () async {
                            Navigator.pop(context);
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        NotificationsScreen()));
                          },
                        ),
                      ],
                    )
                  : SizedBox(),

              //Add new code for testing 26-08-2025
              // ListTile(
              //   dense: true,
              //   trailing: Icon(
              //     Icons.arrow_forward_ios,
              //     color: ColorConstants.GREY_3,
              //     size: 15,
              //   ),
              //   visualDensity:
              //   VisualDensity(vertical: -4, horizontal: -4),
              //   leading: Container(
              //       width: 30,
              //       height: 30,
              //       padding: const EdgeInsets.all(2),
              //       decoration: BoxDecoration(
              //           color: ColorConstants.LEADER,
              //           borderRadius: BorderRadius.circular(6)),
              //       child: Icon(
              //         Icons.offline_pin_sharp,
              //         color: ColorConstants.WHITE,
              //         size: 20,
              //       )),
              //   title: Text(
              //     'Text Html Question Type',
              //   ).tr(),
              //   onTap: () async {
              //     //Navigator.push(context, NextPageRoute(BotFAQPage()));
              //     Navigator.push(
              //         context,
              //         MaterialPageRoute(
              //             builder: (context) => HtmlWebViewPage(
              //               //url: '${APK_DETAILS['module_registration']}'+Preference.getString(Preference.SSOTOKEN).toString(),
              //               //url: '${APK_DETAILS['module_registration']}'+Preference.getInt(Preference.USER_ID).toString(),
              //               title: tr('mec_learn'),
              //             ),
              //             maintainState: false));
              //   },
              // )

              // APK_DETAILS["offline_video_download"] == "1" || APK_DETAILS["offline_pdf_download_learn"] == "1"
              //     ? Divider()
              //     : SizedBox(),
              // APK_DETAILS["offline_video_download"] == "1" || APK_DETAILS["offline_pdf_download_learn"] == "1"
              //     ? ListTile(
              //         dense: true,
              //         trailing: Icon(
              //           Icons.arrow_forward_ios,
              //           color: ColorConstants.GREY_3,
              //           size: 15,
              //         ),
              //         visualDensity:
              //             VisualDensity(vertical: -4, horizontal: -4),
              //         leading: Container(
              //             width: 30,
              //             height: 30,
              //             padding: const EdgeInsets.all(2),
              //             decoration: BoxDecoration(
              //                 color: ColorConstants.LEADER,
              //                 borderRadius: BorderRadius.circular(6)),
              //             child: Icon(
              //               Icons.offline_pin_sharp,
              //               color: ColorConstants.WHITE,
              //               size: 20,
              //             )),
              //         title: Text(
              //           'offline_downloads',
              //         ).tr(),
              //         onTap: () async {
              //           //Navigator.push(context, NextPageRoute(BotFAQPage()));
              //           Navigator.push(
              //             context,
              //             MaterialPageRoute(
              //               builder: (context) => ViewOfflineVideoPage(),
              //             ),
              //           );
              //         },
              //       )
              //     : SizedBox(),

              // Divider(),
              // ListTile(
              //   dense: true,
              //   trailing: Icon(
              //     Icons.arrow_forward_ios,
              //     color: ColorConstants.GREY_3,
              //     size: 15,
              //   ),
              //   visualDensity: VisualDensity(vertical: -4, horizontal: -4),
              //   leading: Container(
              //       width: 30,
              //       height: 30,
              //       padding: const EdgeInsets.all(2),
              //       decoration: BoxDecoration(
              //           color: ColorConstants().gradientLeft(),
              //           borderRadius: BorderRadius.circular(6)),
              //       child: Icon(
              //         Icons.question_answer,
              //         color: ColorConstants.WHITE,
              //         size: 20,
              //       )),
              //   title: Text(
              //     'FAQs',
              //   ).tr(),
              //   onTap: () async {
              //     Navigator.push(context, NextPageRoute(BotFAQPage()));
              //   },
              // ),
              /*Divider(),
                    ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                      leading: Container(
                          width: 30,
                          height: 30,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                              color: ColorConstants().gradientLeft(),
                              borderRadius: BorderRadius.circular(6)),
                          child: Icon(
                            Icons.video_call,
                            color: ColorConstants.WHITE,
                            size: 20,
                          )),
                      title: Text(
                        'Song Video',
                      ).tr(),
                      onTap: () async {
                        Navigator.push(context, NextPageRoute(SongOnVideo()));
                      },
                    ),*/
            ],
          ),
        ),

        ValueListenableBuilder(
            valueListenable: Hive.box("content").listenable(),
            builder: (bc, Box box, child) {
              List<Menu> allMenu;
              allMenu = box
                  .get('bottomMenu')
                  .map((e) => Menu.fromJson(Map<String, dynamic>.from(e)))
                  .cast<Menu>()
                  .toList();
              List<String> preferenceRoles =
                  Preference.getString(Preference.SECONDARY_ROLE)
                          ?.toLowerCase()
                          .split(',') ??
                      [];
//event menu

              List<Menu> eventMenu = allMenu
                  .where((menu) =>
                      menu.role != null &&
                      Preference.getString(Preference.SECONDARY_ROLE)
                              ?.toLowerCase() !=
                          "" &&
                      (menu.role!.contains('EventCoordinator') ||
                          menu.role!.contains('EventManager') ||
                          menu.role!.contains('MediaManager')))
                  .toList();

              eventMenu = eventMenu.where((element) {
                List<String> elementRoles =
                    element.role.toString().toLowerCase().split(',');

                bool containRole = elementRoles
                    .any((role) => preferenceRoles.contains(role.trim()));

                return containRole;
              }).toList();

//placemnt menu
              allMenu = allMenu
                  .where((menu) =>
                      menu.role != null &&
                      (!menu.role!.contains('EventCoordinator') &&
                          !menu.role!.contains('EventManager')))
                  .toList();

              // Filter menu items based on secondary roles
              allMenu = allMenu.where((element) {
                List<String> elementRoles =
                    element.role.toString().toLowerCase().split(',');

                bool containRole = elementRoles
                    .any((role) => preferenceRoles.contains(role.trim()));

                return containRole;
              }).toList();

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  eventMenu.length != 0
                      ? SizedBox(
                          height: 10,
                        )
                      : SizedBox(),
                  eventMenu.length != 0
                      ? Padding(
                          padding: const EdgeInsets.only(
                              left: 17.0, bottom: 10.0, right: 17.0),
                          child: Text(
                            'events',
                            style:
                                Styles.textBold(size: 16, color: Colors.grey),
                          ).tr(),
                        )
                      : SizedBox(),
                  ListView.builder(
                    physics: NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: eventMenu.length,
                    itemBuilder: (context, eventIndex) {
                      if (eventIndex >= eventMenu.length ||
                          !menuColor.containsKey(eventMenu[eventIndex].url)) {
                        return Container();
                      }

                      return Container(
                        // padding: const EdgeInsets.symmetric(vertical: 10),
                        decoration: BoxDecoration(
                            // borderRadius: BorderRadius.circular(20),
                            color: ColorConstants.WHITE),
                        child: Column(
                          children: [
                            Container(height: 1, color: ColorConstants.GREY),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: ListTile(
                                dense: true,
                                trailing: Icon(
                                  Icons.arrow_forward_ios,
                                  color: ColorConstants.GREY_3,
                                  size: 15,
                                ),
                                visualDensity:
                                    VisualDensity(vertical: -4, horizontal: -4),
                                leading: Container(
                                    width: 30,
                                    height: 30,
                                    padding: const EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                        color: menuColor[
                                            eventMenu[eventIndex].url],
                                        borderRadius: BorderRadius.circular(6)),
                                    child:
                                        menuIcons[eventMenu[eventIndex].url] ??
                                            Icon(Icons.error,
                                                color: Colors.red, size: 20)

                                    // Icon(
                                    //   Icons.menu,
                                    //   color: ColorConstants.WHITE,
                                    //   size: 20,
                                    // )
                                    ),
                                title: Text(
                                  '${eventMenu[eventIndex].label}',
                                ).tr(),
                                onTap: () async {
                                  setState(() {
                                    final routeBuilder =
                                        pages[eventMenu[eventIndex].url];
                                    if (routeBuilder != null) {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: routeBuilder),
                                      );
                                    } else {
                                      // Handle the case where the URL is not mapped
                                      print(
                                          'No route defined for ${allMenu[eventIndex].url}');
                                    }
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  //placement
                  allMenu.length != 0 ? SizedBox(height: 10) : SizedBox(),
                  allMenu.length != 0
                      ? Padding(
                          padding: const EdgeInsets.only(
                              left: 17.0, bottom: 10.0, right: 17.0),
                          child: Text(
                            'placement',
                            style:
                                Styles.textBold(size: 16, color: Colors.grey),
                          ).tr(),
                        )
                      : SizedBox(),
                  ListView.builder(
                    physics: NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: allMenu.length,
                    itemBuilder: (context, index) {
                      if (index >= allMenu.length ||
                          !menuColor.containsKey(allMenu[index].url)) {
                        return Container();
                      }
                      return Container(
                        decoration: BoxDecoration(color: ColorConstants.WHITE),
                        child: Column(
                          children: [
                            Container(height: 1, color: ColorConstants.GREY),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: ListTile(
                                dense: true,
                                trailing: Icon(
                                  Icons.arrow_forward_ios,
                                  color: ColorConstants.GREY_3,
                                  size: 15,
                                ),
                                visualDensity:
                                    VisualDensity(vertical: -4, horizontal: -4),
                                leading: Container(
                                  width: 30,
                                  height: 30,
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.DARK_BLUE,
                                      borderRadius: BorderRadius.circular(6)),
                                  child: menuIcons[allMenu[index].url] ??
                                      Icon(Icons.error,
                                          color: Colors.red, size: 20),
                                ),
                                title: Text(
                                  '${allMenu[index].label}',
                                ).tr(),
                                onTap: () async {
                                  // Log.v(
                                  //     'webview url is   ${APK_DETAILS['portfolios']}' +
                                  //         Preference.getInt(Preference.USER_ID)
                                  //             .toString());
                                  setState(() {
                                    final routeBuilder =
                                        pages[allMenu[index].url];

                                    if (routeBuilder != null) {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: routeBuilder),
                                      );
                                    } else {
                                      print(
                                          'No route defined for ${allMenu[index].url}');
                                    }
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              );
            }),

        SizedBox(height: 10),
        Container(
          // margin: const EdgeInsets.all(10),
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
              // borderRadius: BorderRadius.circular(20),
              color: ColorConstants.WHITE),
          child: Column(
            children: [
              ListTile(
                dense: true,
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color: ColorConstants.GREY_3,
                  size: 15,
                ),
                visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                leading: Container(
                    width: 30,
                    height: 30,
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(6)),
                    child: Icon(
                      Icons.share_outlined,
                      color: ColorConstants.WHITE,
                      size: 20,
                    )),
                title: Text('share_app').tr(),
                onTap: () {
                  SharePlus.instance.share(ShareParams(
                      text: '${tr('share_app_text', args: [
                        '${APK_DETAILS['app_name']}'
                      ])}\n\n${Platform.isAndroid ? "https://play.google.com/store/apps/details?id=${APK_DETAILS['package_name']}" : "https://apps.apple.com/us/app/id${APK_DETAILS['appId']}"}'));
                  FirebaseAnalytics.instance
                      .logEvent(name: 'share_url', parameters: {
                    "type": "app_share",
                  });
                },
              ),

              Divider(),
              ListTile(
                dense: true,
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color: ColorConstants.GREY_3,
                  size: 15,
                ),
                visualDensity: VisualDensity(vertical: -4, horizontal: -4),
                leading: Container(
                    width: 30,
                    height: 30,
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: ColorConstants.UNSELECTED_PAGE,
                        borderRadius: BorderRadius.circular(6)),
                    child: Icon(
                      Icons.settings_outlined,
                      color: ColorConstants.WHITE,
                      size: 20,
                    )),
                title: Text(
                  'settings_privacy',
                ).tr(),
                onTap: () async {
                  Navigator.pop(context);
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => SettingAndPrivacyPage()));
                },
              ),

              APK_DETAILS["help_emable"] == "1" ? Divider() : SizedBox(),
              APK_DETAILS["help_emable"] == "1"
                  ? ListTile(
                      dense: true,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: ColorConstants.GREY_3,
                        size: 15,
                      ),
                      visualDensity:
                          VisualDensity(vertical: -4, horizontal: -4),
                      leading: Container(
                          width: 30,
                          height: 30,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                              color: ColorConstants.YELLOW,
                              borderRadius: BorderRadius.circular(6)),
                          child: Icon(
                            Icons.help,
                            color: ColorConstants.WHITE,
                            size: 20,
                          )),
                      title: Text(
                        'help_mec_future',
                      ).tr(),
                      onTap: () async {
                        Navigator.pop(context);
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => TermsAndCondition(
                                      url: '${APK_DETAILS['help_mec_future']}' +
                                          Preference.getInt(Preference.USER_ID)
                                              .toString(),
                                      title: tr('help_mec_future'),
                                    ),
                                maintainState: false));
                      },
                    )
                  : SizedBox(),

              // Divider(),
              // APK_DETAILS['geminiEnabled'] == '1' ? ListTile(
              //   dense: true,
              //   trailing: Icon(
              //     Icons.arrow_forward_ios,
              //     color: ColorConstants.GREY_3,
              //     size: 15,
              //   ),
              //   visualDensity: VisualDensity(vertical: -4, horizontal: -4),
              //   leading: Container(
              //       width: 30,
              //       height: 30,
              //       padding: const EdgeInsets.all(2),
              //       decoration: BoxDecoration(
              //           color: ColorConstants.UNSELECTED_PAGE,
              //           borderRadius: BorderRadius.circular(6)),
              //       child: Icon(
              //         Icons.settings_input_svideo,
              //         color: ColorConstants.WHITE,
              //         size: 20,
              //       )),
              //   title: Text(
              //     'Gemini AI',
              //   ).tr(),
              //   onTap: () async {
              //     Navigator.pop(context);
              //     Navigator.push(
              //         context,
              //         MaterialPageRoute(
              //             builder: (context) => SettingAndPrivacyPage()));
              //   },
              // ) :SizedBox(),

              // Divider(),
              // ListTile(
              //   dense: true,
              //   trailing: Icon(
              //     Icons.arrow_forward_ios,
              //     color: ColorConstants.GREY_3,
              //     size: 15,
              //   ),
              //   visualDensity: VisualDensity(vertical: -4, horizontal: -4),
              //   leading: Container(
              //       width: 30,
              //       height: 30,
              //       padding: const EdgeInsets.all(2),
              //       decoration: BoxDecoration(
              //           color: ColorConstants().gradientLeft(),
              //           borderRadius: BorderRadius.circular(6)),
              //       child: Icon(
              //         Icons.question_answer,
              //         color: ColorConstants.WHITE,
              //         size: 20,
              //       )),
              //   title: Text(
              //     'FAQs',
              //   ).tr(),
              //   onTap: () async {
              //     Navigator.push(context, NextPageRoute(BotFAQPage()));
              //   },
              // ),
            ],
          ),
        ),

        // Divider(),
        SizedBox(
          height: 10,
        ),

        Align(
          alignment: FractionalOffset.bottomCenter,
          child: ListTile(
            dense: true,
            visualDensity: VisualDensity(vertical: -1, horizontal: -4),
            leading: Container(
                width: 30,
                height: 30,
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                    color: Colors.red, borderRadius: BorderRadius.circular(6)),
                child: Icon(
                  Icons.logout,
                  color: ColorConstants.grey,
                  size: SizeConstants.ICON_20,
                )),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('logout').tr(),
                Text(
                  '${tr('version')}: $appVersion',
                  style: Styles.regular(size: 12),
                )
              ],
            ),
            onTap: () async {
              AlertsWidget.showCustomDialog(
                  context: context,
                  title: tr('confirm_message'),
                  oKText: tr('ok'),
                  icon: 'assets/images/circle_alert_fill.svg',
                  onOkClick: () async {
                    Utility.logoutUser(context);
                  });
            },
          ),
        ),
        if (APK_DETAILS['package_name'] == 'com.singulariswow.mec' ||
            APK_DETAILS['package_name'] == 'com.singulariswow.aid')
          Padding(
              padding: const EdgeInsets.only(left: 16, top: 14, right: 16),
              child: Center(
                  child: Text(
                'powered_by',
                style: Styles.regular(size: 10),
              ).tr())),

        APK_DETAILS['domain_url'] == 'https://mecfutureuat.mec.edu.om/'
            ? Padding(
                padding: const EdgeInsets.only(left: 16, top: 2, right: 16),
                child: Center(
                    child: Text(
                  'UAT',
                  style: Styles.regular(size: 10),
                ).tr()))
            : SizedBox(),

        if (APK_DETAILS['package_name'] == 'com.singulariswow.mec' ||
            APK_DETAILS['package_name'] == 'com.singulariswow.aid')
          Padding(
              padding: const EdgeInsets.only(left: 70, right: 70),
              child: SvgPicture.asset(
                height: height(context) * 0.05,
                width: width(context) * 0.4,
                'assets/images/singularis_white.svg',
                colorFilter: ColorFilter.mode(Colors.black, BlendMode.srcIn),
                //'assets/images/singualris_wow_head.svg',
              )),
        SizedBox(
          height: 10,
        ),
      ]),
    );
  }
}

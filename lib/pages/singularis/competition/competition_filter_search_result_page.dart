import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/competition_response.dart';
import '../../../utils/Log.dart';
import '../../../utils/Styles.dart';
import '../../../utils/constant.dart';
import '../../../utils/resource/colors.dart';
import 'competition_detail.dart';
import 'package:masterg/pages/singularis/job/widgets/blank_widget_page.dart';

class CompetitionFilterSearchResultPage extends StatefulWidget {
  final String? appBarTitle;
  final bool? isSearchMode;
  final bool? isPopular;
  final String? jobRolesId;
  final String? domainId;

  const CompetitionFilterSearchResultPage(
      {Key? key,
      this.appBarTitle,
      this.isSearchMode = true,
      this.isPopular,
      this.jobRolesId,
      this.domainId})
      : super(key: key);

  @override
  State<CompetitionFilterSearchResultPage> createState() =>
      _CompetitionFilterSearchResultPageState();
}

class _CompetitionFilterSearchResultPageState
    extends State<CompetitionFilterSearchResultPage> {
  bool? competitionLoading;
  // CompetitionResponse? competitionResponse;
  List<Competition?>? competitionList;
  // int listLength = 0;

  @override
  void initState() {
    getCompetitionList();
    super.initState();
  }

  void getCompetitionList() {
    BlocProvider.of<HomeBloc>(context).add(CompetitionListFilterEvent(
        isPopular: false,
        isFilter: widget.isSearchMode,
        ids: widget.jobRolesId,
        domainId: widget.domainId));

    BlocProvider.of<HomeBloc>(context).add(CompetitionListFilterEvent(
        isPopular: true,
        isFilter: widget.isSearchMode,
        ids: widget.jobRolesId,
        domainId: widget.domainId));
  }

  void _handleCmpSearchResultListResponse(CompetitionListFilterState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          competitionLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("CompetitionState....................");
          if (competitionList != null && competitionList?.length != 0) {
            competitionList
                ?.addAll(state.competitionFilterResponse!.data!.toList());
          } else {
            // competitionResponse = state.competitionFilterResponse;
            competitionList = state.competitionFilterResponse?.data;
          }
          competitionLoading = false;
          // listLength = int.parse('${competitionResponse?.data!.length}');
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState ..........................${competitionState.error}");
          competitionLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is CompetitionListFilterState) {
            _handleCmpSearchResultListResponse(state);
          }
        },
        child: Scaffold(
          appBar: AppBar(
            iconTheme: IconThemeData(
              color: Colors.black,
            ),
            elevation: 0.0,
            backgroundColor: ColorConstants.WHITE,
            title: Text(
              'search_result',
              style: TextStyle(color: Colors.black),
            ).tr(),
          ),
          backgroundColor: ColorConstants.JOB_BG_COLOR,
          body: _comResultCard(),
        ),
      ),
    );
  }

  Widget _comResultCard() {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: competitionList != null &&
              int.parse('${competitionList?.length}') != 0
          ? ListView.builder(
              // itemCount: int.parse('${competitionResponse?.data!.length}'),
              itemCount: competitionList?.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (BuildContext context, int index) {
                // String startDate =
                //     '${competitionList?[index]?.startDate?.split(' ').first}';

                // DateTime dateTime = DateTime.parse(startDate);
                // String startFormated =
                //     DateFormat('dd-MMM-yyyy').format(dateTime);

                // String endDate =
                //     '${competitionList?[index]?.endDate?.split(' ').first}';
                // dateTime = DateTime.parse(endDate);
                // String endDateFormated =
                //     DateFormat('dd-MMM-yyyy').format(dateTime);
                return Container(
                  padding: EdgeInsets.all(10),
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => CompetitionDetail(
                                  competitionId: competitionList?[index]?.id)));
                    },
                    child: renderCompetitionCard(
                        '${competitionList![index]?.image ?? ''}',
                        '${competitionList![index]?.name ?? ''}',
                        '${competitionList![index]?.organizedBy ?? ''}',
                        '${competitionList![index]?.competitionLevel ?? tr('easy')}',
                        '${competitionList![index]?.gScore ?? 0}',
                        // '$startFormated ${tr('to')} $endDateFormated',

                        '${competitionList?[index]?.startDate}',
                        '${competitionList?[index]?.endDate}'),
                  ),
                );
              })
          : competitionLoading == false
              ? Container(
                  height: height(context),
                  width: double.infinity,
                  color: ColorConstants.WHITE,
                  child: Center(
                    child: Text(
                      'no_competition_found',
                      textAlign: TextAlign.center,
                      style: Styles.regular(size: 20),
                    ).tr(),
                  ))
              : BlankWidgetPage(),
    );
  }

  renderCompetitionCard(String competitionImg, String name, String companyName,
      String difficulty, String gScore, String startdate, String endDate) {
    return Stack(
      children: [
        Container(
          height: 115,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.symmetric(vertical: 6),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: const Offset(5, 5),
              ),
            ],
          ),
          child: Row(children: [
            SizedBox(
              width: 90,
              height: 90,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  imageUrl: competitionImg,
                  width: 100,
                  height: 120,
                  errorWidget: (context, url, error) => Image.asset(
                    'assets/images/comp_emp.png',
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(width: 10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: width(context) * 0.6,
                    child: Text(
                      name,
                      style: Styles.bold(
                        size: 14,
                        lineHeight: 1,
                      ),
                      maxLines: 1,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(
                    height: 2,
                  ),
                  if (companyName != '')
                    SizedBox(
                      width: width(context) * 0.4,
                      child: Text(
                        companyName,
                        style:
                            Styles.semibold(size: 12, color: Color(0xff929BA3)),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  SizedBox(
                    height: 4,
                  ),
                  Row(
                    children: [
                      Text('${difficulty.capital()}',
                          style: Styles.regular(
                              color: ColorConstants.GREEN_1, size: 12)),
                      SizedBox(
                        width: 4,
                      ),
                      Text('•',
                          style: Styles.regular(
                              color: ColorConstants.GREY_2, size: 12)),
                      SizedBox(
                        width: 4,
                      ),
                      SizedBox(
                          height: 15,
                          child: Image.asset('assets/images/coin.png')),
                      SizedBox(
                        width: 4,
                      ),
                      Text('$gScore ${tr('points')}',
                          style: Styles.regular(
                              color: ColorConstants.ORANGE_4, size: 12)),
                    ],
                  ),
                  SizedBox(height: 6),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.calendar_month,
                        size: 20,
                      ),
                      SizedBox(
                        width: 4,
                      ),
                      StrToTime(
                        time: startdate,
                        dateFormat: ' dd-MMM-yyyy ',
                        appendString: Utility().isRTL(context) ? '' : tr('to'),
                        textStyle: Styles.regular(
                            size: 12, lineHeight: 1, color: Color(0xff5A5F73)),
                      ),
                      StrToTime(
                        time: endDate,
                        dateFormat: ' dd-MMM-yyyy ',
                        appendString: Utility().isRTL(context) ? tr('to') : '',
                        textStyle: Styles.regular(
                            size: 12, lineHeight: 1, color: Color(0xff5A5F73)),
                      ),
                    ],
                  ),
                  SizedBox(height: 2),
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        color: ColorConstants.BODY_TEXT,
                        size: 20,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      StrToTime(
                        time: startdate,
                        dateFormat: ' hh:mm a ',
                        appendString: Utility().isRTL(context) ? '' : tr('to'),
                        textStyle: Styles.regular(
                            size: 12,
                            lineHeight: 1,
                            color: ColorConstants.BODY_TEXT),
                      ),
                      StrToTime(
                        time: endDate,
                        dateFormat: ' hh:mm a ',
                        appendString: Utility().isRTL(context) ? tr('to') : '',
                        textStyle: Styles.regular(
                            size: 12,
                            lineHeight: 1,
                            color: Color.fromARGB(255, 4, 6, 16)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ]),
        ),
        Positioned(
            right: 2,
            top: 10,
            bottom: 10,
            child: Icon((Icons.arrow_forward_ios))),
      ],
    );
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}

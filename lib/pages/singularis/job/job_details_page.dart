import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_notes.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_video.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_youtube.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/competition_content_list_resp.dart';
import '../../../data/providers/assessment_detail_provider.dart';
import '../../../data/providers/assignment_detail_provider.dart';
import '../../../utils/Log.dart';
import '../../../utils/Styles.dart';
import '../../../utils/resource/colors.dart';
import '../../../utils/resource/size_constants.dart';
import '../../auth_pages/terms_and_condition_page.dart';
import '../../training_pages/assessment_page.dart';
import '../../training_pages/assignment_detail_page.dart';
import '../competition/competition_navigation/competition_session.dart';
import 'package:masterg/data/models/response/home_response/user_jobs_list_response.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/utility.dart';
import 'dart:math' as math;

enum CardType { assignment, assessment, session, video, note, youtube }

class JobDetailsPage extends StatefulWidget {
  final String? title;
  final String? description;
  final String? location;
  final String? skillNames;
  final String? companyName;
  final String? domain;
  final String? companyThumbnail;
  final String? experience;
  final List<ListElement>? jobListDetails;
  final int? id;
  final int? jobStatusNumeric;
  String? jobStatus = '';
  final String? skillMatching;
  final int? vacancy;
  final double? minExperience;
  final double? maxExperience;
  final bool? isMyJob;
  final String? landingPageUrl;

  JobDetailsPage(
      {Key? key,
      this.title,
      this.description,
      this.location,
      this.skillNames,
      this.companyName,
      this.domain,
      this.companyThumbnail,
      this.experience,
      this.jobListDetails,
      this.id,
      this.jobStatus,
      required this.jobStatusNumeric,
      this.skillMatching,
      this.vacancy,
      this.minExperience,
      this.maxExperience,
      this.isMyJob = false,
      this.landingPageUrl})
      : super(key: key);

  @override
  State<JobDetailsPage> createState() => _JobDetailsPageState();
}

class _JobDetailsPageState extends State<JobDetailsPage> {
  bool? competitionDetailLoading = true;
  CompetitionContentListResponse? contentList;
  int? applied = 0;

  @override
  void initState() {
    getCompetitionContentList(0);
    super.initState();
  }

  void getCompetitionContentList(int? isApplied) {
    BlocProvider.of<HomeBloc>(context).add(CompetitionContentListEvent(
        competitionId: widget.id, isApplied: isApplied));
  }

  void handleCompetitionListState(AppJobListCompeState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          competitionDetailLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Competition Content List State....................");
          contentList = competitionState.response;
          competitionDetailLoading = false;
          if (applied != 0) {
            Get.rawSnackbar(
              messageText: Text(
                'application_submitted',
                style: Styles.regular(size: 14, color: ColorConstants.WHITE),
              ).tr(),
              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: ColorConstants.BLACK,
              borderRadius: 4,
              duration: Duration(seconds: 3),
              boxShadows: [
                BoxShadow(
                    color: Color(0xff898989).withValues(alpha: 0.1),
                    offset: Offset(0, 4.0),
                    blurRadius: 11)
              ],
            );
          }
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Competition Content ..........................${competitionState.response?.error}");
          FirebaseAnalytics.instance.logEvent(name: 'job_details', parameters: {
            "ERROR": '${competitionState.response?.error}',
          });

          competitionDetailLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is AppJobListCompeState) handleCompetitionListState(state);
        },
        child: Scaffold(
          appBar: AppBar(
            iconTheme: IconThemeData(
              color: Colors.black,
            ),
            elevation: 0.0,
            backgroundColor: ColorConstants.WHITE,
            title: Text(
              '',
              style: TextStyle(color: Colors.black),
            ),
          ),
          backgroundColor: ColorConstants.JOB_BG_COLOR,
          body: _makeBody(),
        ),
      ),
    );
  }

  Widget _makeBody() {
    return SingleChildScrollView(
      child: Container(
        margin: EdgeInsets.only(bottom: SizeConstants.JOB_BOTTOM_SCREEN_MGN),
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            ///Job List
            Divider(
              height: 1,
              color: ColorConstants.GREY_3,
            ),
            _jobDetailsWidget(),

            ///Similar Jobs
            SizedBox(
              height: 30,
            ),
            _progressActivitiesSection(),
          ],
        ),
      ),
    );
  }

  Widget _jobDetailsWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      color: Colors.white,
      child: Column(
        children: [
          SizedBox(
            height: 10,
          ),
          Container(
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 3,
                  child: Container(
                      padding: EdgeInsets.only(
                        right: 10.0,
                      ),
                      child: widget.companyThumbnail != null
                          ? Image.network(
                              '${widget.companyThumbnail}',
                            )
                          : Image.asset(
                              'assets/images/pb_2.png',
                            )),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(left: 5.0, right: 5.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${widget.title ?? ''}',
                            style: Styles.bold(
                                size: 14,
                                color:
                                    ColorConstants().primaryColorbtnAlways())),
                        SizedBox(height: 6),
                        Text('${widget.companyName ?? ''}',
                            style: Styles.bold(
                                size: 12, color: ColorConstants.BLACK)),
                        SizedBox(height: 6),
                        // Text('time',
                        //     style: Styles.regular(
                        //         size: 12, color: ColorConstants.GREY_3)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 10),
          widget.minExperience != null || widget.maxExperience != null
              ? Row(
                  children: [
                    Icon(
                      Icons.work_outline,
                      size: 16,
                      color: ColorConstants.GREY_3,
                    ),
                    // SizedBox(width: 8),
                    Padding(
                      padding: EdgeInsets.only(
                        left: Utility().isRTL(context) ? 0 : 5.0,
                        right: Utility().isRTL(context) ? 5.0 : 0.0,
                      ),
                      child: Text(
                        '${tr('exp')}: ',
                        style: Styles.regular(
                          size: 13,
                          color: ColorConstants.BODY_TEXT,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 100,
                      /*child: Text(
                        '${widget.location}',
                        style: Styles.regular(
                            size: 12, color: ColorConstants.GREY_3),
                        overflow: TextOverflow.visible,
                        maxLines: 1,
                        softWrap: false,
                      ),*/

                      child: Text(
                        '${widget.minExperience != null ? widget.minExperience : "0"}' +
                            '-${widget.maxExperience != null ? widget.maxExperience : "0"} ${tr('yrs')} ',
                        style: Styles.regular(
                            size: 12, color: ColorConstants.GREY_3),
                        overflow: TextOverflow.visible,
                        maxLines: 1,
                        softWrap: false,
                      ),
                    ),
                  ],
                )
              : SizedBox(),
          SizedBox(height: 10),
          if (widget.location != null && widget.location!.isNotEmpty) ...[
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: ColorConstants.GREY_3,
                ),
                SizedBox(width: 8),
                SizedBox(
                  width: 100,
                  child: Text(
                    '${widget.location}',
                    style:
                        Styles.regular(size: 12, color: ColorConstants.GREY_3),
                    overflow: TextOverflow.visible,
                    maxLines: 1,
                    softWrap: false,
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
          ],

          widget.vacancy != null
              ? Row(
                  children: [
                    Icon(Icons.group, size: 16, color: ColorConstants.GREY_3),
                    SizedBox(width: 8),
                    Text('${tr('vacancies')}: ',
                        style: Styles.regular(
                            size: 12, color: ColorConstants.GREY_3)),
                    Text('${widget.vacancy ?? '0'}',
                        style: Styles.regular(
                          size: 12,
                          color: ColorConstants.GREY_3,
                        )),
                  ],
                )
              : SizedBox(),
          SizedBox(height: 10),
          widget.skillNames != null
              ? Row(
                  children: [
                    Icon(Icons.checklist,
                        size: 16, color: ColorConstants.GREY_3),
                    SizedBox(width: 8),
                    Text('skills',
                            style: Styles.regular(
                                size: 12, color: ColorConstants.GREY_3))
                        .tr(),
                    Flexible(
                      child: Text(': ${widget.skillNames ?? ''}',
                          style: Styles.regular(
                            size: 12,
                            color: ColorConstants.GREY_3,
                          )),
                    ),
                  ],
                )
              : SizedBox(),
          SizedBox(height: 20),
          // Container(
          //   width: double.infinity,
          //   child: Padding(
          //     padding: const EdgeInsets.only(
          //         left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.start,
          //       children: [
          //         Expanded(
          //           flex: 2,
          //           child: Container(
          //               padding: EdgeInsets.only(
          //                 right: 10.0,
          //               ),
          //               child: widget.companyThumbnail != null
          //                   ? Image.network('${widget.companyThumbnail}')
          //                   : Image.asset('assets/images/pb_2.png')),
          //         ),
          //         Expanded(
          //           flex: 9,
          //           child: Container(
          //             padding: EdgeInsets.only(left: 5.0, right: 5.0),
          //             child: Column(
          //               crossAxisAlignment: CrossAxisAlignment.start,
          //               children: [
          //                 Text('${widget.title}',
          //                     style: Styles.bold(
          //                         size: 14, color: ColorConstants().primaryColorbtnAlways())),
          //                 Padding(
          //                   padding: const EdgeInsets.only(top: 6.0),
          //                   child: Text('${widget.companyName}',
          //                       style: Styles.regular(
          //                           size: 12, color: Color(0xff3E4245))),
          //                 ),
          //                 Padding(
          //                   padding: const EdgeInsets.only(top: 5.0),
          //                   child: Row(
          //                     children: [
          //                       // Image.asset('assets/images/jobicon.png'),
          //                       Icon(Icons.work_outline,
          //                           size: 16, color: ColorConstants.GREY_6),
          //                       Padding(
          //                         padding: EdgeInsets.only(
          //                             left: Utility().isRTL(context) ? 0 : 5.0,
          //                             right:
          //                                 Utility().isRTL(context) ? 5.0 : 0),
          //                         child: Text('${tr('exp')}: ',
          //                             style: Styles.regular(
          //                                 size: 12,
          //                                 color: ColorConstants.GREY_6)),
          //                       ),
          //                       Text(
          //                           '${widget.experience != null ? widget.experience : "0"} ${tr('yrs')} ',
          //                           style: Styles.regular(
          //                               size: 12,
          //                               color: ColorConstants.GREY_6)),
          //                       Text('  • ',
          //                           style: Styles.regular(
          //                               color: ColorConstants.GREY_2,
          //                               size: 12)),
          //                       widget.location != null
          //                           ? Row(
          //                               children: [
          //                                 Padding(
          //                                   padding: EdgeInsets.only(
          //                                     left: Utility().isRTL(context)
          //                                         ? 1.0
          //                                         : 10.0,
          //                                     right: Utility().isRTL(context)
          //                                         ? 1.0
          //                                         : 0.0,
          //                                   ),
          //                                   child: Icon(
          //                                     Icons.location_on_outlined,
          //                                     size: 16,
          //                                     color: ColorConstants.GREY_3,
          //                                   ),
          //                                 ),
          //                                 Text('${widget.location}',
          //                                     style: Styles.regular(
          //                                         size: 12,
          //                                         color:
          //                                             ColorConstants.GREY_3)),
          //                               ],
          //                             )
          //                           : SizedBox(),
          //                     ],
          //                   ),
          //                 ),
          //               ],
          //             ),
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),

          // Container(
          //   child: Padding(
          //     padding: const EdgeInsets.only(
          //         left: 10.0, top: 10.0, right: 10.0, bottom: 30.0),
          //     child: Text(
          //       '${widget.description}',
          //       style: Styles.regular(
          //         size: 13,
          //         color: ColorConstants.GREY_3,
          //       ),
          //     ),
          //   ),
          // ),
          (contentList?.data?.competitionInstructions?.jobStatus == null ||
                      contentList?.data?.competitionInstructions?.jobStatus ==
                          "") &&
                  widget.isMyJob == false
              ? InkWell(
                  onTap: () {
                    FirebaseAnalytics.instance.logEvent(
                        name: 'careers_details_page_job_apply',
                        parameters: {
                          "job_name": widget.title ?? '',
                        });
                    applied = 1;
                    getCompetitionContentList(1);
                    _onLoadingForJob();

                    this.setState(() {
                      contentList?.data?.competitionInstructions?.jobStatus =
                          tr('application_under_process');
                    });
                  },
                  child: Container(
                    height: 40,
                    margin: EdgeInsets.symmetric(horizontal: 12),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(colors: [
                        ColorConstants().primaryColorbtnAlways(),
                        ColorConstants().primaryColorbtnAlways(),
                      ]),
                    ),
                    child: Align(
                      alignment: Alignment.center,
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Text(
                          'apply_button',
                          style: Styles.regular(
                            size: 13,
                            color: ColorConstants.WHITE,
                          ),
                        ).tr(),
                      ),
                    ),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.only(bottom: 20.0),
                  child: Text(
                    '${contentList?.data?.competitionInstructions?.jobStatus ?? ''}',
                    style: Styles.bold(
                        color: contentList?.data?.competitionInstructions
                                    ?.jobStatusNumeric ==
                                0
                            ? ColorConstants.VIEW_ALL
                            : Colors.green,
                        size: 12),
                  ),
                ),
          if (contentList?.data?.competitionInstructions?.landingPageUrl !=
                  null &&
              contentList?.data?.competitionInstructions?.landingPageUrl != '')
            InkWell(
              onTap: () {
                launchUrl(
                    Uri.parse(
                        '${contentList?.data?.competitionInstructions?.landingPageUrl}'),
                    mode: LaunchMode.externalApplication);
              },
              child: Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'apply_on_company_website',
                      style: Styles.bold(
                          color: ColorConstants.ACTIVE_TAB_UNDERLINE, size: 12),
                    ).tr(),
                    SizedBox(width: 5),
                    Transform.rotate(
                        angle: Utility().isRTL(context) ? -math.pi * 0.5 : 0,
                        child: SvgPicture.asset(
                          'assets/images/open_url.svg',
                          fit: BoxFit.cover,
                        ))
                  ],
                ),
              ),
            ),
          SizedBox(height: 10),
          widget.landingPageUrl != null && widget.landingPageUrl != ""
              ? InkWell(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => TermsAndCondition(
                                  url: widget.landingPageUrl,
                                  title: tr('apply_company_website'),
                                ),
                            maintainState: false));
                  },
                  child: Container(
                    height: 40,
                    margin: EdgeInsets.symmetric(horizontal: 50),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: LinearGradient(colors: [
                        Colors.black12,
                        ColorConstants().primaryColorGradient(),
                      ]),
                    ),
                    child: Align(
                      alignment: Alignment.center,
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Text(
                          'apply_company_website',
                          style: Styles.regular(
                            size: 13,
                            color: ColorConstants.WHITE,
                          ),
                        ).tr(),
                      ),
                    ),
                  ),
                )
              : SizedBox(),
          //landingPageUrl
          SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _progressActivitiesSection() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          if (competitionDetailLoading == false) ...[
            contentList?.data?.competitionInstructions?.jobStatus ==
                        'Application under review' ||
                    contentList?.data?.competitionInstructions?.jobStatus ==
                        'Placed'
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Align(
                        alignment: Alignment.topLeft,
                        child: Text('progress',
                                style: Styles.bold(
                                    size: 16, color: Color(0xff0E1638)))
                            .tr()),
                  )
                : SizedBox(),
          ],
          if (competitionDetailLoading == false) ...[
            (contentList?.data?.competitionInstructions?.jobStatusNumeric ==
                        2 ||
                    contentList
                            ?.data?.competitionInstructions?.jobStatusNumeric ==
                        3)
                ? ListView.builder(
                    physics: BouncingScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: contentList?.data?.list?.length,
                    itemBuilder: (context, index) {
                      bool isLocked = index != 0;
                      bool isTick = false;

                      if (contentList?.data?.list?[index]?.perCompletion !=
                              0.0 &&
                          (contentList?.data?.list?[index]?.contentType ==
                                  'assignment' ||
                              contentList?.data?.list?[index]?.contentType ==
                                  'assessment') &&
                          double.parse(
                                  '${contentList?.data?.list?[index]?.overallScore ?? 0}') >=
                              double.parse(
                                  '${contentList?.data?.list?[index]?.perCompletion ?? 0}')) {
                        isTick = true;
                      } else if (contentList
                                  ?.data?.list?[index]?.perCompletion !=
                              0.0 &&
                          contentList
                                  ?.data?.list?[index]?.completionPercentage !=
                              0.0 &&
                          !(contentList?.data?.list?[index]?.contentType ==
                                  'assignment' ||
                              contentList?.data?.list?[index]?.contentType ==
                                  'assessment') &&
                          double.parse(
                                  '${contentList?.data?.list?[index]?.completionPercentage}') >=
                              double.parse(
                                  '${contentList?.data?.list?[index]?.perCompletion}')) {
                        isTick = true;
                      }
                      if (contentList?.data?.list?[index]?.activityStatus ==
                          2) {
                        isTick = true;
                      }

                      if (index != 0) {
                        CompetitionContent? data =
                            contentList?.data?.list?[index - 1];

                        if (data?.activityStatus != 0) {
                          if (data?.perCompletion != 0.0 &&
                              (data?.contentType == 'assignment' ||
                                  data?.contentType == 'assessment') &&
                              double.parse('${data?.overallScore ?? 0}') >=
                                  double.parse('${data?.perCompletion}')) {
                            isLocked = false;
                          } else if (data?.perCompletion != 0.0 &&
                              (data?.completionPercentage != null ||
                                  data?.completionPercentage != 0.0) &&
                              !(data?.contentType == 'assignment' ||
                                  data?.contentType == 'assessment') &&
                              double.parse('${data?.completionPercentage}') >=
                                  double.parse('${data?.perCompletion}')) {
                            isLocked = false;
                          }
                          if (data?.activityStatus == 2) {
                            isLocked = false;
                          }
                        }
                      }

                      return competitionCard(contentList?.data?.list![index],
                          index == ((contentList?.data?.list?.length ?? 1) - 1),
                          isLocked: isLocked, isTick: isTick);
                    })
                : SizedBox(),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    if (contentList?.data?.competitionInstructions?.whatsIn !=
                        null) ...[
                      Text(
                        'whats_foryou',
                        style: Styles.bold(size: 14, color: Color(0xff5A5F73)),
                      ).tr(),
                      SizedBox(
                        height: 8,
                      ),
                      Text(
                        '${contentList?.data?.competitionInstructions?.whatsIn ?? ''}',
                        style: Styles.textRegular(
                            color: Color(0xff5A5F73), size: 14),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                    ],
                    if (contentList
                            ?.data?.competitionInstructions?.instructions !=
                        null) ...[
                      Text(
                        'Requirements',
                        style: Styles.bold(size: 14, color: Color(0xff5A5F73)),
                      ).tr(),
                      SizedBox(
                        height: 8,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 6),
                        child: Text(
                          '${contentList?.data?.competitionInstructions?.instructions ?? ''}',
                          style: Styles.textRegular(
                              color: Color(0xff5A5F73), size: 14),
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                    ],
                    if (contentList?.data?.competitionInstructions?.faq !=
                            null &&
                        contentList?.data?.competitionInstructions?.faq !=
                            '') ...[
                      Text(
                        'job_description',
                        style: Styles.bold(size: 14, color: Color(0xff5A5F73)),
                      ).tr(),
                      SizedBox(
                        height: 8,
                      ),
                      Text(
                        '${contentList?.data?.competitionInstructions?.faq ?? ''}',
                        style: Styles.textRegular(
                            color: Color(0xff5A5F73), size: 14),
                      )
                    ],
                  ],
                ),
              ),
            )
          ] else
            ListView.builder(
                shrinkWrap: true,
                itemCount: 3,
                itemBuilder: (BuildContext context, int index) =>
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.1,
                        margin:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6)),
                      ),
                    )),
        ],
      ),
    );
  }

  Widget competitionCard(CompetitionContent? data, bool isLast,
      {bool? isLocked, bool? isTick}) {
    CardType? cardType;
    switch (data?.contentType) {
      case "video_yts":
        cardType = CardType.youtube;
        break;
      case "video":
        cardType = CardType.video;
        break;
      case "notes":
        cardType = CardType.note;
        break;
      case "assessment":
        cardType = CardType.assessment;
        break;
      case "assignment":
        cardType = CardType.assignment;
        break;
      case "zoomclass":
      case "teamsclass":
      case "otherclass":
        cardType = CardType.session;
        break;
    }
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8),
      child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  isTick == true
                      ? Container(
                          padding: EdgeInsets.all(1),
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: ColorConstants.GREEN_1),
                          child: Icon(
                            Icons.done,
                            size: 20,
                            color: ColorConstants.WHITE,
                          ))
                      : SvgPicture.asset(
                          isLocked == true
                              ? 'assets/images/lock_content.svg'
                              : 'assets/images/circular_border.svg',
                          width: 18,
                          height: 18,
                        ),
                  if (!isLast)
                    Container(
                      margin: EdgeInsets.only(top: 4),
                      height: 75,
                      width: 4,
                      decoration: BoxDecoration(
                          color: Color(0xffCECECE),
                          borderRadius: BorderRadius.circular(14)),
                    )
                ],
              ),
            ),
            Container(
              width: MediaQuery.of(context).size.width * 0.86,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                  color: ColorConstants.WHITE,
                  borderRadius: BorderRadius.circular(10)),
              child: card(data!, cardType, isLocked),
            )
          ]),
    );
  }

  Widget card(CompetitionContent data, CardType? cardType, bool? isLocked) {
    // String startDate = '${data.startDate?.split(' ').first}';
    // DateTime start = DateFormat("yyyy-MM-dd").parse(startDate);
    return InkWell(
      onTap: () async {
        if (isLocked == true) {
          ScaffoldMessenger.of(context)
              .showSnackBar(SnackBar(content: Text('content_locked').tr()));
          return;
        }
        if (cardType == CardType.video) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionVideoPlayer(
                    id: data.id!,
                    videoUrl: data.content!,
                  )));
        }
        if (cardType == CardType.youtube) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionYoutubePlayer(
                    id: data.id,
                    videoUrl: data.content,
                  )));
        } else if (cardType == CardType.note) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionNotes(
                    id: data.id,
                    notesUrl: data.content,
                  )));
        } else if (cardType == CardType.assignment)
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: ChangeNotifierProvider<AssignmentDetailProvider>(
                      create: (c) => AssignmentDetailProvider(
                          TrainingService(ApiService()), data,
                          fromCompletiton: true, id: data.programContentId),
                      child: AssignmentDetailPage(
                        id: data.id,
                        fromCompetition: true,
                        fromJob: true,
                        difficultyLevel: '${data.difficultyLevel?.capital()}',
                      ))));
        else if (cardType == CardType.assessment) {
          Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: ChangeNotifierProvider<AssessmentDetailProvider>(
                      create: (context) => AssessmentDetailProvider(
                          TrainingService(ApiService()), data,
                          fromCompletiton: true, id: data.programContentId),
                      child: AssessmentDetailPage(
                        fromCompetition: true,
                        fromJob: true,
                      ))));
        } else if (cardType == CardType.session) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionSession(
                    data: data,
                  )));
        }
        getCompetitionContentList(0);
      },
      child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${data.contentTypeLabel ?? ''}',
                style: Styles.regular(size: 12, color: ColorConstants.GREY_3)),
            if (cardType != CardType.session) ...[
              SizedBox(height: 8),
              Text('${data.title}', style: Styles.bold(size: 12)),
            ],
            SizedBox(height: 8),
            if (cardType == CardType.session)
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.network('${data.baseFileUrl}/${data.presenterImage}',
                      height: height(context) * 0.06,
                      width: height(context) * 0.06,
                      errorBuilder: (_, __, ___) {
                    return SizedBox();
                  }),
                  SizedBox(
                    width: 8,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Utility().decrypted128('${data.presenter}'),
                        style: Styles.bold(size: 14),
                      ),
                      Text(
                        '${data.title}',
                        style:
                            Styles.regular(size: 10, color: Color(0xff5A5F73)),
                      ),
                    ],
                  )
                ],
              ),
            SizedBox(
              height: 8,
            ),
            Row(
              children: [
                Icon(
                  Icons.calendar_month,
                  size: 20,
                ),
                SizedBox(
                  width: 4,
                ),
                StrToTime(
                  time: data.startDate!,
                  dateFormat: 'dd\'th\' MMMM, hh:mm a',
                  appendString: '',
                  textStyle: Styles.regular(size: 12, color: Color(0xff5A5F73)),
                )
                // Text(
                //   '${Utility.ordinal(start.day)} ${('${listOfMonths[start.month - 1]}')}',
                //   style: Styles.regular(size: 12, color: Color(0xff5A5F73)),
                // )
              ],
            ),
          ]),
    );
  }

  void _onLoadingForJob() {
    BuildContext? dialogContext;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        dialogContext = context;
        return Dialog(
          child: Container(
            padding: EdgeInsets.only(left: 20, right: 10),
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: new Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                new CircularProgressIndicator(
                  color: ColorConstants().primaryColorbtnAlways(),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: new Text("${tr('job_apply')}..."),
                ),
              ],
            ),
          ),
        );
      },
    );
    new Future.delayed(new Duration(seconds: 2), () {
      // Navigator.pop(context); //pop dialog
      Navigator.pop(dialogContext!);
    });
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}

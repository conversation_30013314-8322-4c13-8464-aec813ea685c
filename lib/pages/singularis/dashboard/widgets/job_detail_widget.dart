import 'dart:math';
import 'package:flutter/material.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class JobDetailWidget extends StatelessWidget {
  const JobDetailWidget({
    super.key,
    required this.context,
    required this.name,
    required this.value,
    required this.percent,
  });

  final BuildContext context;
  final String name;
  final dynamic value;
  final dynamic percent;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {},
      child: Container(
          width: min(width(context), 480) * 0.44,
          decoration: BoxDecoration(
            color: context.appColors.gradientRight.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(10),
          ),
          margin: EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(name),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "$value",
                    style: Styles.getRegularThemeStyle(context),
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  Text(
                    "$percent%",
                    style: Styles.regular(
                      color: context.appColors.success,
                    ),
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  Transform.translate(
                      offset: Offset(Utility().isRTL(context) ? 10.0 : 0, 0),
                      child: Icon(
                        Icons.arrow_drop_up_outlined,
                        color: context.appColors.success,
                        size: 20,
                      )),
                ],
              ),
            ],
          )),
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RenderCompetitionCard extends StatelessWidget {
  const RenderCompetitionCard({
    super.key,
    required this.context,
    required Animation<double> animation,
    required this.isEnableEvent,
    required this.competitionImg,
    required this.name,
    required this.companyName,
    required this.difficulty,
    required this.gScore,
    required this.startdate,
    required this.endDate,
    required this.enablePStatus,
    required this.progressStatus,
    required this.location,
  }) : _animation = animation;

  final BuildContext context;
  final Animation<double> _animation;
  final bool isEnableEvent;
  final String competitionImg;
  final String name;
  final String companyName;
  final String difficulty;
  final String gScore;
  final String startdate;
  final String endDate;
  final bool enablePStatus;
  final String progressStatus;
  final String? location;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, state) {
            return Container(
              height: enablePStatus == true ? 110 : 122,
              width: double.infinity,
              padding: EdgeInsets.all(8),
              margin: EdgeInsets.symmetric(vertical: 7),
              decoration: BoxDecoration(
                color: context.appColors.surface,
                //color: context.appColors.white,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                    color: context.isDarkMode
                        ? Colors.white24
                        : context.appColors.divider,
                    width: 1),
              ),
              child: Row(children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: CachedNetworkImage(
                    imageUrl: competitionImg,
                    width: enablePStatus == true ? 90 : 95,
                    height: enablePStatus == true ? 90 : 90,
                    errorWidget: (context, url, error) => SvgPicture.asset(
                      'assets/images/event_default.svg',
                      height: 10,
                      width: 10,
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
                SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  //mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    SizedBox(
                      width: width(context) * 0.5,
                      child: Text(
                        name,
                        style: Styles.getBoldThemeStyle(context, size: 16),
                        maxLines: 1,
                        softWrap: true,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    companyName.isNotEmpty
                        ? SizedBox(
                            width: MediaQuery.of(context).size.width * 0.62,
                            child: Text(
                              companyName,
                              maxLines: 1,
                              style: Styles.semibold(
                                  size: 11, color: context.appColors.grey3),
                            ),
                          )
                        : SizedBox(),
                    SizedBox(height: 2),

                    //TODO: Show Live
                    enablePStatus == true
                        ? progressStatus.toLowerCase() == 'live'
                            ? Row(
                                children: [
                                  SvgPicture.asset(
                                    'assets/images/live_icon.svg',
                                    fit: BoxFit.fitHeight,
                                  ),
                                  SizedBox(width: 3),
                                  progressStatus.toLowerCase() == 'live'
                                      ? FadeTransition(
                                          opacity: _animation,
                                          child: Text(
                                            'live',
                                            style: Styles.bold(
                                                color: context.appColors.error,
                                                size: 16),
                                          ).tr(),
                                        )
                                      : Text(progressStatus,
                                          style: Styles.bold(
                                              color: context.appColors.error,
                                              size: 16))
                                ],
                              )
                            : SizedBox()
                        : SizedBox(),

                    // TODO: Show Location
                    SizedBox(height: 3),
                    if (location != 'null' &&
                        location != '' &&
                        isEnableEvent == true)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.location_on,
                            color: context.bodyTextColor,
                            //color: context.appColors.bodyText,
                            size: 16,
                          ),
                          SizedBox(
                            width: 3,
                          ),
                          Text(location ?? '',
                              style: Styles.regular(
                                  size: 12,
                                  // lineHeight: 1,
                                  color: context.bodyTextColor
                                  //color:    context.appColors.bodyText
                                  )),
                        ],
                      ),

                    enablePStatus == true
                        ? SizedBox()
                        : Padding(
                            padding: const EdgeInsets.only(top: 3.0),
                            child: Row(
                              children: [
                                Text(difficulty.capital(),
                                    style: Styles.regular(
                                        color: context.appColors.green,
                                        size: 12)),
                                SizedBox(
                                  width: 4,
                                ),
                                Text('•',
                                    style: Styles.regular(
                                        color: context.appColors.grey2,
                                        size: 12)),
                                SizedBox(
                                  width: 4,
                                ),
                                SizedBox(
                                    height: 15,
                                    child:
                                        Image.asset('assets/images/coin.png')),
                                SizedBox(
                                  width: 4,
                                ),
                                Text('$gScore ${tr('points')}',
                                    style: Styles.regular(
                                        color: context.appColors.orange4,
                                        size: 12)),
                              ],
                            ),
                          ),
                    enablePStatus == true &&
                            progressStatus.toLowerCase() == 'live'
                        ? SizedBox()
                        : Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.calendar_month,
                                  color: context.appColors.bodyText,
                                  size: 18,
                                ),
                                SizedBox(
                                  width: 4,
                                ),

                                //TODO: Show start date Time
                                StrToTime(
                                  time: startdate,
                                  dateFormat: ' dd-MMM-yy ',
                                  //appendString: Utility().isRTL(context) ? '' : tr('to'),
                                  appendString: ',',
                                  textStyle: Styles.regular(
                                      size: 12,
                                      color: context.appColors.subHeadingTitle),
                                ),

                                StrToTime(
                                  time: startdate,
                                  dateFormat: ' hh:mm a ',
                                  //appendString: Utility().isRTL(context) ? '' : tr('to'),
                                  appendString: '',
                                  textStyle: Styles.regular(
                                      size: 12,
                                      lineHeight: 1,
                                      color: context.appColors.bodyText),
                                ),
                              ],
                            ),
                          ),
                    enablePStatus == true &&
                            progressStatus.toLowerCase() == 'live'
                        ? SizedBox()
                        : Padding(
                            padding: const EdgeInsets.only(top: 5.0),
                            child: Row(
                              children: [
                                /*Icon(
                                      Icons.access_time,
                                      color: context.appColors.bodyText,
                                      size: 16,
                                    ),*/
                                Text(
                                  'To',
                                  style: Styles.bold(
                                      size: 13,
                                      lineHeight: 1,
                                      color: context.appColors.bodyText),
                                ),
                                SizedBox(
                                  width: 4,
                                ),
                                //TODO: Show end date time
                                StrToTime(
                                  time: endDate,
                                  dateFormat: ' dd-MMM-yy ',
                                  //appendString: Utility().isRTL(context) ? tr('to') : '',
                                  appendString: ',',
                                  textStyle: Styles.regular(
                                      size: 12,
                                      color: context.appColors.subHeadingTitle),
                                ),

                                StrToTime(
                                  time: endDate,
                                  dateFormat: ' hh:mm aa ',
                                  appendString:
                                      Utility().isRTL(context) ? tr('to') : '',
                                  textStyle: Styles.regular(
                                      size: 12,
                                      lineHeight: 1,
                                      color: context.appColors.textBlack),
                                ),
                              ],
                            ),
                          ),
                  ],
                ),
              ]),
            );
          },
        ),
        Positioned(
            right: Utility().isRTL(context) ? null : 8,
            left: Utility().isRTL(context) ? 8 : null,
            top: 10,
            bottom: 10,
            child: Icon(
              (Icons.arrow_forward_ios),
              size: 20,
            )),
      ],
    );
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + substring(1);
  }
}

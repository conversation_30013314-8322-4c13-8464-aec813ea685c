// import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:shimmer/shimmer.dart';

class CompetitionBlankPage extends StatelessWidget {
  const CompetitionBlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('CompetitionBlankPage'),
        Container(
          height: 90,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.symmetric(vertical: 6),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: const Offset(5, 5),
              ),
            ],
          ),
          child: Row(children: [
            Shimmer.fromColors(
              baseColor: Color(0xffe6e4e6),
              highlightColor: Color(0xffeaf0f3),
              child: Container(
                  height: 80,
                  margin: EdgeInsets.only(left: 2),
                  width: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                  )),
            ),
            SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Color(0xffe6e4e6),
                  highlightColor: Color(0xffeaf0f3),
                  child: Container(
                      height: 12,
                      margin: EdgeInsets.only(left: 2),
                      width: 150,
                      decoration: BoxDecoration(
                        color: Colors.white,
                      )),
                ),
                SizedBox(
                  height: 7,
                ),
                Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                  ],
                ),
                SizedBox(
                  height: 7,
                ),
                Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 60,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(
                      width: 7,
                    ),
                    SizedBox(
                      width: 7,
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 70,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 70,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                  ],
                )
              ],
            ),
          ]),
        ),
      ],
    );
  }
}

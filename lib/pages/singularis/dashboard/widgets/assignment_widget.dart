import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
import 'package:masterg/data/models/response/home_response/my_assignment_response.dart';
import 'package:masterg/data/providers/my_assignment_detail_provider.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/training_pages/mg_assignment_detail_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

class Assignmentwidget extends StatelessWidget {
  const Assignmentwidget({
    super.key,
    required this.context,
    required this.item,
  });

  final BuildContext context;
  final RecentActivity? item;

  @override
  Widget build(BuildContext context) {
    return InkWell(onTap: () {
      Navigator.push(
        context,
        NextPageRoute(
            ChangeNotifierProvider<MgAssignmentDetailProvider>(
                create: (c) => MgAssignmentDetailProvider(
                    TrainingService(ApiService()),
                    AssignmentList(
                      contentId: item?.contentId,
                      title: item?.title,
                      description: item?.description,
                      startDate: item?.startDate,
                      endDate: item?.endDate,
                      allowMultiple: item?.allowMultiple,
                      isGraded: item?.isGraded,
                      submissionMode: item?.submissionMode,
                      maximumMarks: item?.maximumMarks,
                      contentType: item?.contentType,
                      languageId: item?.languageId,
                      moduleId: item?.moduleId,
                      totalAttempts: item?.totalAttempts,
                      score: item?.score,
                      status: item?.status,
                      file: item?.file,
                    )),
                child: MgAssignmentDetailPage(
                  id: item?.contentId,
                  status: item?.status?.toLowerCase(),
                )),
            isMaintainState: true),
      );
    }, child: BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Container(
            padding: EdgeInsets.only(left: 10, right: 10, top: 17, bottom: 17),
            width: MediaQuery.of(context).size.width * 0.9,
            margin: EdgeInsets.symmetric(vertical: 10, horizontal: 6),
            decoration: BoxDecoration(
              color: context.surfaceColor,
              borderRadius: BorderRadius.circular(10),
              /*border: Border.all(
                            color: Colors.green, width: 1, style: BorderStyle.solid)*/
            ),
            child:
                Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
              if (Utility.isExpired(item?.endDate, currentIndiaTime!) &&
                  item?.status != 'Completed') ...[
                SvgPicture.asset(
                  'assets/images/missed_icon.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ] else if (item?.status == 'Completed') ...[
                SvgPicture.asset(
                  'assets/images/completed_icon.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ] else if (item?.status == 'Upcoming') ...[
                SvgPicture.asset(
                  'assets/images/upcoming_live.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ] else if (item?.status == 'Pending') ...[
                SvgPicture.asset(
                  'assets/images/pending_icon.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ],
              SizedBox(width: 20),
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: Text('${item?.title}',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          softWrap: false,
                          style: Styles.getBoldThemeStyle(context, size: 16)),
                    ),
                    SizedBox(height: 5),
                    if (item?.status == 'Completed') ...[
                      Text('submitted',
                              style: Styles.getRegularThemeStyle(context,
                                  size: 12))
                          .tr(),
                      SizedBox(height: 5),
                      // Text(
                      //     '${DateFormat('MM/dd/yyyy, hh:mm a').format(DateTime.fromMillisecondsSinceEpoch(item.endDate! * 1000).toUtc())}',
                      //     style: Styles.getRegularThemeStyle(context,size: 12)),
                      // SizedBox(height: 5),
                    ] else if (item?.status == 'Upcoming') ...[
                      SizedBox(height: 5),
                      Text(
                          '${tr('submission_date')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item!.endDate! * 1000))}',
                          style: Styles.getTextRegularThemeStyle(
                            context,
                            size: 12,
                          )),
                      SizedBox(
                        height: 3,
                      ),
                      Text(
                          '${tr('deadline')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(
                            DateTime.fromMillisecondsSinceEpoch(
                                item!.endDate! * 1000),
                          )}',
                          style: Styles.getTextRegularThemeStyle(context,
                              size: 12)),
                      SizedBox(height: 5),
                    ] else if (item?.status == 'Pending') ...[
                      Text(tr('${item?.status}'.toLowerCase()),
                          style: Styles.regular(
                              size: 12, color: context.appColors.primary)),
                      SizedBox(height: 5),
                      Text(
                          '${tr('submission_date')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item!.endDate! * 1000))}',
                          style: Styles.getRegularThemeStyle(
                            context,
                            size: 12,
                          )),
                      SizedBox(
                        height: 3,
                      ),
                      Text(
                          '${tr('deadline')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item!.endDate! * 1000))}',
                          style: Styles.getRegularThemeStyle(
                            context,
                            size: 12,
                          )),
                      SizedBox(height: 5),
                    ],
                    if (item?.isGraded == 1 &&
                        item?.score != null &&
                        item?.score != 0)
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                                text: '${tr('score_earned')}: ',
                                style: Styles.getRegularThemeStyle(context,
                                    size: 12)),
                            TextSpan(
                              text: '${item?.score.toStringAsFixed(2)}',
                              style:
                                  Styles.getBoldThemeStyle(context, size: 12),
                            ),
                            TextSpan(
                                text: '/${item?.maximumMarks}',
                                style: Styles.getRegularThemeStyle(context,
                                    size: 12)),
                          ],
                        ),
                      ),
                  ]),
            ]));
      },
    ));
  }
}

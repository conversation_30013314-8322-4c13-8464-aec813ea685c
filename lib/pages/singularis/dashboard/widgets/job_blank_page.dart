// import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:shimmer/shimmer.dart';

class JobBlankPage extends StatelessWidget {
  const JobBlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 160,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.only(bottom: 6.0, top: 10.0),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: const Offset(5, 5),
              ),
            ],
          ),
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Color(0xffe6e4e6),
                  highlightColor: Color(0xffeaf0f3),
                  child: Container(
                      height: 100,
                      margin: EdgeInsets.only(left: 2),
                      width: 200,
                      decoration: BoxDecoration(
                        color: Colors.white,
                      )),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2, top: 10),
                          width: 250,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(
                      height: 7,
                    ),
                    Row(
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 12,
                              margin: EdgeInsets.only(left: 2),
                              width: 280,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              ]),
        ),
      ],
    );
  }
}

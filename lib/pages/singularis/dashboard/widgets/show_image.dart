import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class ShowImage extends StatefulWidget {
  final String? path;

  const ShowImage({super.key, this.path});

  @override
  State<ShowImage> createState() => _ShowImageState();
}

class _ShowImageState extends State<ShowImage> {
  Uint8List? imageFile;

  @override
  void initState() {
    super.initState();

    getFile();
  }

  Future<Uint8List?> getFile() async {
    final uint8list = await VideoThumbnail.thumbnailData(
      video: widget.path!,
      imageFormat: ImageFormat.JPEG,
      timeMs: Duration(seconds: 1).inMilliseconds,
    );
    if (mounted) {
      setState(() {
        imageFile = uint8list;
      });
    }
    return uint8list;
  }

  @override
  Widget build(BuildContext context) {
    return imageFile != null
        ? Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.memory(
                  imageFile!,
                  fit: BoxFit.cover,
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                ),
              ),
              Center(
                child: SvgPicture.asset(
                  'assets/images/play.svg',
                  height: 40.0,
                  width: 40.0,
                  allowDrawingOutsideViewBox: true,
                ),
              ),
            ],
          )
        : Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.2,
              margin: EdgeInsets.symmetric(horizontal: 10),
              width: MediaQuery.of(context).size.width * 0.4,
              decoration: BoxDecoration(
                  color: context.appColors.surface,
                  borderRadius: BorderRadius.circular(6)),
            ),
          );
  }
}

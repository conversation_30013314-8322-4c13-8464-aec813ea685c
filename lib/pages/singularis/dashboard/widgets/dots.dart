import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/cupertino.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class Dots extends StatelessWidget {
  const Dots({
    super.key,
    required this.index,
    required this.postCount,
  });

  final int index;
  final int postCount;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DotsIndicator(
          dotsCount: postCount,
          position: index.toDouble(),
          decorator: DotsDecorator(
            size: const Size.square(8.0),
            color: Color(0xffCCCACA),
            spacing: const EdgeInsets.only(left: 5.0),
            activeColor: context.appColors.gradientLeft,
            activeSize: const Size(22.0, 8.0),
            activeShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
        ),
      ],
    );
  }
}

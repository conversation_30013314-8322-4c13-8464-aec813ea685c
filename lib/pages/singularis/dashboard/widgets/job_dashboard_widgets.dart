import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/job_detail_widget.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:flutter_svg/flutter_svg.dart';

class JobDashboardWidgets extends StatelessWidget {
  const JobDashboardWidgets({
    super.key,
    required this.context,
    required this.dashboardContentResponse,
  });

  final BuildContext context;
  final DashboardContentResponse? dashboardContentResponse;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: context.appColors.surface),
      child: Column(
        children: [
          SizedBox(
            height: height(context) * 0.085,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: Utility().isRTL(context)
                    ? EdgeInsets.only(right: 15.0)
                    : EdgeInsets.only(left: 15.0),
                child: SvgPicture.asset('assets/images/s_careers.svg'),
              ),
              Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 10,
                  ),
                  child: Text(
                    'Oman Jobs',
                    style: Styles.bold(
                        color: context.appColors.headingPrimaryColor),
                  ).tr()),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: SizedBox(
              height: 80,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  JobDetailWidget(
                      context: context,
                      name: "Domains",
                      value: dashboardContentResponse
                          ?.data?.jobDashboard?[0].domains,
                      percent: 15),
                  JobDetailWidget(
                      context: context,
                      name: "Companies",
                      value: dashboardContentResponse
                          ?.data?.jobDashboard?[0].companies,
                      percent: 25),
                  JobDetailWidget(
                      context: context,
                      name: "JobPosting",
                      value: dashboardContentResponse
                          ?.data?.jobDashboard?[0].jobPosting,
                      percent: 15),
                  JobDetailWidget(
                      context: context,
                      name: "Job Roles",
                      value: dashboardContentResponse
                          ?.data?.jobDashboard?[0].jobRoles,
                      percent: 25),
                  JobDetailWidget(
                      context: context,
                      name: "Location",
                      value: dashboardContentResponse
                          ?.data?.jobDashboard?[0].location,
                      percent: 15),
                  JobDetailWidget(
                      context: context,
                      name: "Vacancies",
                      value: dashboardContentResponse
                          ?.data?.jobDashboard?[0].vacancies,
                      percent: 25),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

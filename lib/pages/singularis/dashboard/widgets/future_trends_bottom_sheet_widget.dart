import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/singularis/graph.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class FutureTrendsBottomSheetWidget extends StatelessWidget {
  const FutureTrendsBottomSheetWidget({
    super.key,
    required this.title,
    required this.jobsCount,
    required this.growthType,
    required this.domainId,
    required this.growth,
  });
  final String title;
  final String jobsCount;
  final String growthType;
  final String growth;
  final int domainId;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          right: 10,
          child: IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: Icon(Icons.close)),
        ),
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.7,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.6,
                decoration: BoxDecoration(
                    color:
                        context.appColors.gradientRight.withValues(alpha: 0.08),
                    border: Border.all(color: context.appColors.listColor)),
                margin: EdgeInsets.all(8),
                // color: context.appColors.error,
                child: Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                  child: Column(
                    children: [
                      Center(
                        child: Text(
                          title,
                          style: Styles.bold(
                              color: context.appColors.headingPrimaryColor,
                              size: 13),
                          softWrap: true,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '$jobsCount ${tr('job_roles')} ',
                            style: Styles.regular(
                                color: context.appColors.grey3, size: 11),
                          ),
                          Padding(
                            padding: EdgeInsets.only(
                                left: !Utility().isRTL(context) ? 0 : 8.0),
                            child: Text(
                              growthType == 'up'
                                  ? ' + $growth%'
                                  : growth == '0'
                                      ? ' $growth%'
                                      : ' - $growth%',
                              style: Styles.regular(
                                  color: growthType == 'up'
                                      ? context.appColors.green
                                      : context.appColors.error,
                                  size: 11),
                            ),
                          ),
                          Transform.translate(
                              offset: Offset(
                                  Utility().isRTL(context) ? 10.0 : 0, 0),
                              child: growthType == 'up'
                                  ? Icon(
                                      Icons.arrow_drop_up_outlined,
                                      color: Colors.green,
                                      size: 20,
                                    )
                                  : Icon(
                                      Icons.arrow_drop_down,
                                      color: context.appColors.error,
                                      size: 20,
                                    )),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                  child: LineChartWidget(
                domainid: domainId,
              )),
            ],
          ),
        ),
      ],
    );
  }
}

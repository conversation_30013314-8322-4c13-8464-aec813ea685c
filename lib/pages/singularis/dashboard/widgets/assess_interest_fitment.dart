// import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/ghome/my_assessments.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/custom_outline_button.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class AssessInterestFitment extends StatelessWidget {
  const AssessInterestFitment({
    super.key,
    // required this.context,
  });

  // final BuildContext context;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 6.0),
      child: Sized<PERSON>ox(
        height: 35,
        child: CustomOutlineButton(
          strokeWidth: 1,
          radius: 50,
          gradient: LinearGradient(
            colors: [
              ColorConstants().gradientLeft(),
              ColorConstants().gradientRight()
            ],
            begin: Alignment.topLeft,
            end: Alignment.topRight,
          ),
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0, right: 10.0),
            child: GradientText(
              '${tr('assess_interest_fitment')}',
              style: Styles.regular(size: 13),
              colors: [
                ColorConstants().gradientLeft(),
                ColorConstants().gradientRight(),
              ],
            ),
          ),
          onPressed: () {
            FirebaseAnalytics.instance
                .logScreenView(screenName: 'Dashboard_Screen');
            FirebaseAnalytics.instance.logEvent(
                name: 'user_id-${Preference.getInt(Preference.USER_ID)}',
                parameters: {
                  "set_goal_event": 'Evaluate_Suitability_Interest',
                });
            /*Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => MyAssessmentPage(
                          interestAreaID: int.parse(
                              '${Preference.getString(Preference.SETUP_GOAL_INTEREST_ID)}'),
                        )));*/

            ///New Code working on
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => MyAssessmentPage(
                          interestAreaID: '0',
                          jobRoleID: int.parse(
                              '${Preference.getString(Preference.SETUP_GOAL_INTEREST_ID) ?? 173}'),
                          skillID: 0,
                        )));
          },
          /*onPressed: () {
            assessmentWidget(
            recentActivity?[assessmentFirstIndex!], assessmentFirstIndex!);
            fromProfileCard = true;
            if (assessmentFirstIndex != null) {
              Navigator.push(
                  context,
                  NextPageRoute(
                      ChangeNotifierProvider<MgAssessmentDetailProvider>(
                          create: (context) => MgAssessmentDetailProvider(
                              TrainingService(ApiService()),
                              AssessmentList(
                                  title:
                                  recentActivity?[assessmentFirstIndex!].title,
                                  contentId: recentActivity?[assessmentFirstIndex!]
                                      .contentId,
                                  description: recentActivity?[assessmentFirstIndex!]
                                      .description,
                                  startDate: recentActivity?[assessmentFirstIndex!]
                                      .startDate,
                                  endDate: recentActivity?[assessmentFirstIndex!]
                                      .endDate,
                                  maximumMarks: recentActivity?[assessmentFirstIndex!]
                                      .maximumMarks,
                                  passingMarks: recentActivity?[assessmentFirstIndex!]
                                      .passingMarks,
                                  questionCount: recentActivity?[assessmentFirstIndex!]
                                      .questionCount,
                                  attemptAllowed:
                                  recentActivity?[assessmentFirstIndex!]
                                      .attemptAllowed,
                                  durationInMinutes:
                                  recentActivity?[assessmentFirstIndex!]
                                      .durationInMinutes,
                                  attemptCount:
                                  recentActivity?[assessmentFirstIndex!]
                                      .attemptCount,
                                  difficultyLevel:
                                  recentActivity?[assessmentFirstIndex!]
                                      .difficultyLevel,
                                  module: recentActivity?[assessmentFirstIndex!].module,
                                  skill: recentActivity?[assessmentFirstIndex!].skill,
                                  program: recentActivity?[assessmentFirstIndex!].program,
                                  attemptedOn: recentActivity?[assessmentFirstIndex!].attemptedOn,
                                  score: recentActivity?[assessmentFirstIndex!].score,
                                  status: recentActivity?[assessmentFirstIndex!].status)),
                          child: MgAssessmentDetailPage(
                            isAssessmentReport: fromProfileCard,
                            programName:
                            '${recentActivity?[assessmentFirstIndex!].title}',
                          )),
                      isMaintainState: true));
    
              // Navigator.push(
              //       context,
              //       NextPageRoute(
              //           ChangeNotifierProvider<MgAssessmentDetailProvider>(
              //               create: (context) => MgAssessmentDetailProvider(
              //                   TrainingService(ApiService()),
              //                   assessmentList?[0]),
              //               child: MgAssessmentDetailPage(
              //                 isAssessmentReport: fromProfileCard,
              //                 programName: 'Assess Interest Fitment',
              //               )),
              //           isMaintainState: true))
              //       .then((value) => _getHomeData());
            } else {
              Utility.showSnackBar(
                  scaffoldContext: context,
                  message: 'You have not any Assess Interest Fitment');
            }
          },*/
        ),
      ),
    );
  }
}

// import 'dart:convert';

import 'package:flutter/material.dart';

class CarvaanListItem extends StatelessWidget {
  final String description;

  CarvaanListItem({required this.description});

  List<TextSpan> _getTextSpans(String text) {
    List<TextSpan> spans = [];
    RegExp exp = RegExp(r'(#\w+)|([^#]+)');
    Iterable<RegExpMatch> matches = exp.allMatches(text);

    for (var match in matches) {
      String matchText = match[0] ?? '';
      spans.add(TextSpan(
        text: matchText,
        style: TextStyle(
          color: matchText.startsWith('#') ? Colors.blue : Colors.black,
        ),
      ));
    }
    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return RichText(
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: _getTextSpans(description),
        style: TextStyle(
          fontSize: 14,
          height: 1.2, // equivalent to lineHeight
        ),
      ),
    );
  }
}

// import 'dart:convert';

import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/resource/size_constants.dart';

class BuildYourPortfolioCard extends StatelessWidget {
  const BuildYourPortfolioCard({
    super.key,
    required this.context,
    required this.menuProvider,
    required this.colorBg,
    required this.strTitle,
    required this.strDes,
    required this.clickType,
  });

  final BuildContext context;
  final MenuListProvider? menuProvider;
  final ui.Color colorBg;
  final String strTitle;
  final String strDes;
  final String clickType;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      margin: const EdgeInsets.only(
          top: SizeConstants.JOB_LEFT_SCREEN_MGN,
          left: SizeConstants.JOB_LEFT_SCREEN_MGN,
          right: SizeConstants.JOB_RIGHT_SCREEN_MGN,
          bottom: SizeConstants.JOB_LEFT_SCREEN_MGN),
      width: double.infinity,
      child: InkWell(
        onTap: () {
          Navigator.push(context, NextPageRoute(NewPortfolioPage()))
              .then((value) {
            if (value != null) menuProvider?.updateCurrentIndex(value);
          });
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset('assets/images/build_portfolio.svg'),
              SizedBox(
                width: 20,
              ),
              Expanded(
                flex: 9,
                child: Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('$strTitle',
                          style: Styles.bold(
                              size: 16, color: ColorConstants.WHITE)),
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text('$strDes',
                            style: Styles.regularWhite(lineHeight: 1.4)),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Container(
                  padding: EdgeInsets.only(left: 10.0),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        gradient: LinearGradient(colors: [
          ColorConstants().gradientLeft(),
          ColorConstants().gradientRight(),
        ]),
        color: colorBg,
        boxShadow: [],
      ),
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/ghome/widget/view_widget_details_page.dart';
import 'package:masterg/pages/singularis/wow_studio.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RenderFeatureContentLimitWidget extends StatelessWidget {
  const RenderFeatureContentLimitWidget({
    super.key,
    required this.context,
    required this.featuredContentList,
  });

  final BuildContext context;
  final List<DashboardFeaturedContentLimit>? featuredContentList;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(6)),
            color: context.appColors.surface,
          ),
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    APK_DETAILS['package_name'] == 'com.singularis.mesc'
                        ? SvgPicture.asset(
                            'assets/images/mesc_wow_studio_gradient.svg')
                        : APK_DETAILS['package_name'] ==
                                'com.singularis.jumeira'
                            ? SvgPicture.asset(
                                'assets/images/jumeira_wow_studio_gradient.svg')
                            : SvgPicture.asset(
                                'assets/images/wow_studio_gradient.svg'),
                    SizedBox(width: 8),
                    Text(
                            APK_DETAILS['package_name'] == 'com.singularis.mesc'
                                ? 'mesc_studio'
                                : 'wow_studio',
                            style: Styles.bold(
                                color: context.appColors.headingTitle))
                        .tr(),
                    Expanded(child: SizedBox()),
                    InkWell(
                      onTap: () {
                        FirebaseAnalytics.instance.logEvent(
                            name: 'wow_studio_dashboard',
                            parameters: {
                              "wow_studio": 'view all',
                            });

                        Navigator.push(context, NextPageRoute(WowStudio()));
                      },
                      child: Text('view_all',
                          style: Styles.regular(
                            size: 12,
                            color: context.appColors.bodyText,
                          )).tr(),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 14, right: 14),
                child: Visibility(
                  visible: featuredContentList!.isNotEmpty,
                  child: GridView.builder(
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: featuredContentList!.length,
                    shrinkWrap: true,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        mainAxisSpacing: 0,
                        crossAxisSpacing: 20,
                        childAspectRatio: 2 / 3,
                        mainAxisExtent:
                            MediaQuery.of(context).size.height * 0.318,
                        crossAxisCount: width(context) > 700 ? 3 : 2),
                    itemBuilder: (BuildContext context, int index) {
                      return InkWell(
                        onTap: () async {
                          FirebaseAnalytics.instance.logEvent(
                              name: 'wow_studio_dashboard',
                              parameters: {
                                "wow_studio":
                                    featuredContentList![index].title ?? "",
                              });
                          showModalBottomSheet(
                              context: context,
                              backgroundColor: context.appColors.surface,
                              isScrollControlled: true,
                              builder: (context) {
                                return FractionallySizedBox(
                                  heightFactor: 1.0,
                                  child: ViewWidgetDetailsPage(
                                    currentID: featuredContentList![index].id,
                                    root: 'dashboard',
                                  ),
                                );
                              });
                        },
                        child: Column(
                          children: [
                            Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12)),
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Container(
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.25,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12)),
                                          foregroundDecoration: BoxDecoration(
                                              gradient: LinearGradient(
                                            end: const Alignment(0.0, -1),
                                            begin: const Alignment(0.0, 0.8),
                                            colors: [
                                              const Color(0x8A000000)
                                                  .withValues(alpha: 0.4),
                                              Colors.black12
                                                  .withValues(alpha: 0.0)
                                            ],
                                          )),
                                          child: CachedNetworkImage(
                                            imageUrl:
                                                '${featuredContentList![index].resourcePathThumbnail}',
                                            imageBuilder:
                                                (context, imageProvider) =>
                                                    Container(
                                              decoration: BoxDecoration(
                                                  image: DecorationImage(
                                                image: imageProvider,
                                                //fit: BoxFit.fill,
                                                fit: BoxFit.fitHeight,
                                              )),
                                            ),
                                            placeholder: (context, url) =>
                                                Image.asset(
                                              'assets/images/placeholder.png',
                                              fit: BoxFit.fill,
                                            ),
                                            errorWidget:
                                                (context, url, error) =>
                                                    Image.asset(
                                              'assets/images/placeholder.png',
                                              fit: BoxFit.fill,
                                            ),
                                          )),
                                    ),
                                    if (featuredContentList![index]
                                            .resourcePath !=
                                        null)
                                      if (featuredContentList![index]
                                          .resourcePath!
                                          .contains('.mp4'))
                                        Positioned.fill(
                                          child: Align(
                                            alignment: Alignment.center,
                                            child: SvgPicture.asset(
                                              'assets/images/play_video_icon.svg',
                                              height: 30.0,
                                              width: 30.0,
                                              allowDrawingOutsideViewBox: true,
                                            ),
                                          ),
                                        ),
                                  ],
                                )),
                            Container(
                              height: 50,
                              width: width(context),
                              margin: EdgeInsets.only(top: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 160,
                                    child: Text(
                                        featuredContentList![index].title ?? '',
                                        maxLines: 2,
                                        softWrap: true,
                                        overflow: TextOverflow.ellipsis,
                                        style: Styles.semibold(
                                            size: 14,
                                            color: context
                                                .appColors.subHeadingTitle)),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

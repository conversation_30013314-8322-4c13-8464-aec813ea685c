import 'package:flutter/material.dart';
import 'package:masterg/utils/config.dart';

extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
      '${((a * 255.0).round() & 0xff).toRadixString(16).padLeft(2, '0')}'
      '${((r * 255.0).round() & 0xff).toRadixString(16).padLeft(2, '0')}'
      '${((g * 255.0).round() & 0xff).toRadixString(16).padLeft(2, '0')}'
      '${((b * 255.0).round() & 0xff).toRadixString(16).padLeft(2, '0')}';
}

class ColorConstants {
  static const PRIMARY_COLOR_LIGHT = MaterialColor(
    0xff1A1A1D,
    <int, Color>{
      50: Color(0xff1A1A1D),
      100: Color(0xff1A1A1D),
      200: Color(0xff1A1A1D),
      300: Color(0xff1A1A1D),
      400: Color(0xff1A1A1D),
      500: Color(0xff1A1A1D),
      600: Color(0xff1A1A1D),
      700: Color(0xff1A1A1D),
      800: Color(0xff1A1A1D),
      900: Color(0xff1A1A1D),
    },
  );

  static const PRIMARY_BLUE = Color(0xff3CA4D2);
  static const BACKGROUND_COLOR = Color(0xffF2F2F2);
  static const HEADING_TITLE = Color(0xff0E1638);
  static const SUB_HEADING_TITLE = Color(0xff2F374E);
  static const BODY_TEXT = Color(0xff727C95);
  static const LEBEL_TEXT = Color(0xffCED4E7);
  static const DIVIDER_COLOR_1 = Color(0xffCED4E7);
  static const DIVIDER_COLOR_2 = Color(0xffF4F7FD);
  static const DARK_BACKGROUND = Color(0xff161616);
  static const DARK_BUTTON = Color(0xff272727);
   static const WOW_PRIMARY_COLOR = Color(0xffF94A29);



  static const PRIMARY_COLOR = Color(0xff263367);
  static const PRIMARY_COLOR_DARK = Color(0xff263367);
  static const ACCENT_COLOR = Color(0xffffffff);
  static const TEXT_FIELD_BG = Color(0xffF2F2F2);
  static const WHITE = Color(0xffffffff);
  static const BLACK = Color(0xff000000);
  static const PENDING_GREY = Color(0xffA7A7A7);
  static const GREY = Color(0xffF0F0F0);
  static const DAR_GREY = Color(0xff252525);
  static const GREEN = Color(0xff3EBDA0);
  static const GREEN_1 = Color(0xff3EBDA0);
  static const RED_BG = Color(0xFFff3d3d);
  static const RED = Color(0xFFEB5757);
  static const BOTTOM_GREY = Color(0xffF8F8F8);
  static const HINT_GREY = Color(0xffACACAC);
  static const INACTIVE_TAB = Color(0xFFBFBFBF);
  static const ACTIVE_TAB = Color(0xFFffd500);
  static const YELLOW_ACTIVE_BUTTON = Color(0xFFffd500);
  static const YELLOW = Color(0xFFFDB515);
  static const ORANGE_3 = Color(0xFFFF2452);
  static const VIEW_ALL = Color(0xFFBD9D50);
  static const ORANGE_4 = Color(0xFFFF9100);
  static const START_GREY_BG = Color(0xFFE0E0E0);
  static const ACTIVE_TAB_UNDERLINE = Color(0xFF12AAEB);
  static const TEXT_DARK_BLACK = Color(0xff1c2555);
  static const BG_COLOR = Color(0xffFAFAFA);
  static const SEARCH_FILLED = Color(0xff194250);
  static const SELECTED_PAGE = Color(0xffF6BA17);
  static const UNSELECTED_PAGE = Color(0xff11576F);
  static const CYAN = Color(0xff3CA4D2);
  static const GREY_1 = Color(0xff333333);
  static const GREY_2 = Color(0xff4F4F4F);
  static const GREY_3 = Color(0xff828282);
   static const GREY_10 = Color(0xffCED4E7);


   static const HEADING_PRIMARY_COLOR = Color(0xff0E1638);
  static const GREY_4 = Color(0xffBDBDBD);
  static const GREY_5 = Color(0xffc7c7c7);
  static const GREY_6 = Color(0xff929BA3);
  static const COURSE_BG = Color(0xff333333);
  static const SECTION_DIVIDER = Color(0xffF5F5F5);
  static const DIVIDER = Color(0xffEFEFEF);
  static const NOTIFICATION_DATE_GREY = Color(0xff9D9A9A);
  static const ORANGE = Color(0xffff8d29);
  static const DARK_BLUE = Color(0xff1c2555);
  static const GREY_OUTLINE = Color(0xff757575);
  static const PURPLE = Color(0xff2c2738);
  static const STARCOLOUR = Color(0xfff9414d);
  static const PILL_BG = Color(0xfffff4db);
  static const ALL_BG = Color(0xffD4D4D4);
  static const UNSELECTED_BUTTON = Color(0xffC8D0E3);

  //Continue button color
  static const CONTINUE_COLOR = Color(0xff043140);
  static const DARK_GREY = Color(0xff444444);
  static const BG_GREY = Color(0xffF2F2F2);

  static const ICON_BG_GREY = Color(0xffE5E5E5);
  static const BG_LIGHT_GREY = Color(0xffebeeff);

  static const TEXT_DARK_BLUE = Color(0xff043140);
  static const BG_BLUE_SCREEN = Color(0xff043140);
  static const BG_BLUE_BTN = Color(0xff12AAEB);
  static const BG_DARK_BLUE_BTN = Color(0xff104152);
  static const BG_DARK_BLUE_BTN_2 = Color(0xff043140);
  static const SHADOW_COLOR = Color(0xff0000002B);
  static const DOCUMENT_QUOTE_BG = Color(0xffFDF1D0);
  static const OTP_TEXT = Color(0xff101a5c);

  static const DISABLE_COLOR = Color(0xffe2e7ff);
  static const Color_E5E5E5 = Color(0xffe5e5e5);
  static const Color_444444 = Color(0xff444444);
  static const Color_291e53 = Color(0xff291e53);
  static const Color_GREEN = Color(0xff0ebdab);

  static const Color_5f6687 = Color(0xff5f6687);

  static const List_Color = Color(0xffFFF5F5);
  static const DASHBOARD_BG_COLOR = Color(0xffF3F3F3);

  ///For Job
  static const JOB_BG_COLOR = Color(0xffF2F2F2);
  static const HIGH_LIGHTS_CARD_COLOR1 = Color(0xff3EBDA0);
  static const HIGH_LIGHTS_CARD_COLOR2 = Color(0xffF4900C);

  static const GRADIENT_RED = Color(0xffFF2252);
  static const GRADIENT_ORANGE = Color(0xffFC7B04);
  static const PROGESSBAR_TEAL = Color(0xff3EBDA0);

  ///Tab Bar Color
  static const selected = Color(0xffFC7B04);


  //ASSESSMENT COLORS
  static const ANSWERED = Color.fromRGBO(255, 157, 92, 1);
  static const NOT_ANSWERED = Color.fromRGBO(255, 235, 59, 1);
  static const REVIEWED = Color.fromRGBO(14, 189, 171, 1);
  static const ANSWERED_REVIEWS = Color.fromRGBO(45, 117, 221, 1);
  static const SELECTED_GREEN = Color.fromRGBO(14, 189, 171, 1);

  static const NOVOICE = Color(0xFFFED944);
  static const LEARNER = Color(0xFFFF9A00);
  static const MASTER = Color(0xFFFE8B66);
  static const EXPERT = Color(0xFF61ABCA);
  static const LEADER = Color(0xFF629BCA);

  static const APPBAR_COLOR = Color(0xff2c73d9);
  static const BUTTON_COLOR = Color(0xff2c73d9);
  static const DASHBOARD_APPLY_COLOR = Color(0xff263367);

  static const BUILD_PORTFOLIO1 = Color(0xffF4900C);
  static const BUILD_PORTFOLIO2 = Color(0xffFFFFFF);


  static const primaryBlue = Color(0xff3CA4D2);
  static const backgroundColor = Color(0xffF2F2F2);
  static const headingTitle = Color(0xff0E1638);
  static const subHeadingTitle = Color(0xff2F374E);
  static const bodyText = Color(0xff727C95);
  static const lebelText = Color(0xffCED4E7);
  static const dividerColor1 = Color(0xffCED4E7);
  static const dividerColor2 = Color(0xffF4F7FD);
  static const darkBackground = Color(0xff161616);
  static const darkButton = Color(0xff272727);
  static const wowPrimaryColor = Color(0xffF94A29);
  static const white = Colors.white;
  static const primaryDark = Color(0xff263367);
  static const textFieldBg = Color(0xffF2F2F2);

  static const black = Color(0xff000000);
  static const pendingGrey = Color(0xffA7A7A7);
  static const grey = Color(0xffF0F0F0);
  static const darkGrey = Color(0xFF303030);
  static const green = Color(0xff3EBDA0);
  static const redBg = Color(0xFFff3d3d);
  static const red = Color(0xFFEB5757);
  static const bottomGrey = Color(0xffF8F8F8);
  static const hintGrey = Color(0xffACACAC);
  static const inactiveTab = Color(0xFFBFBFBF);
  static const activeTab = Color(0xFFffd500);
  static const yellowActiveTab = Color(0xFFffd500);
  static const yellow = Color(0xFFFDB515);
  static const orange3 = Color(0xFFFF2452);
  static const viewAll = Color(0xFFBD9D50);
  static const orange4 = Color(0xFFFF9100);
  static const startGreyBg = Color(0xFFE0E0E0);
  static const activeTabUnderline = Color(0xFF12AAEB);
  static const textDarkBlack = Color(0xff1c2555);
  static const bgColor = Color(0xffFAFAFA);
  static const searchFilled = Color(0xff194250);
  static const selectedPage = Color(0xffF6BA17);
  static const unselectedPage = Color(0xff11576F);
  static const grey1 = Color(0xff333333);
  static const grey2 = Color(0xff4F4F4F);
  static const grey3 = Color(0xff828282);
  static const grey10 = Color(0xffCED4E7);

  static const grey4 = Color(0xffBDBDBD);
  static const grey5 = Color(0xffc7c7c7);
  static const grey6 = Color(0xff929BA3);
  static const courseBg = Color(0xff333333);
  static const sectionDivider = Color(0xffF5F5F5);
  static const divider = Color(0xffEFEFEF);
  static const notificationDateGrey = Color(0xff9D9A9A);
  static const orange = Color(0xffff8d29); //Color(0xFFFF8D29)
  static const darkBlue = Color(0xff1c2555);
  static const greyOutline = Color(0xff757575);
  static const purple = Color(0xff2c2738);
  static const starColor = Color(0xfff9414d);
  static const pillBg = Color(0xfffff4db);
  static const allBg = Color(0xffD4D4D4);
  static const unselectedButton = Color(0xffC8D0E3);

  //Continue button color
  static const continueColor = Color(0xff043140);
  // static const darkGrey = Color(0xff444444);
  static const bgGrey = Color(0xffF2F2F2);

  static const iconBgGrey = Color(0xffE5E5E5);
  static const bgLigtGrey = Color(0xffebeeff);

  static const textDarkBlue = Color(0xff043140);
  static const bgBlueScreen = Color(0xff043140);
  static const bgBlueBtn = Color(0xff12AAEB);
  static const bgDarkBlueBtn = Color(0xff104152);
  static const bgDarkBlueBtn2 = Color(0xff043140);
  static const shadowColor = Color(0x0000002B);
  static const documentQuoteBg = Color(0xffFDF1D0);
  static const otpText = Color(0xff101a5c);

  static const disableColor = Color(0xffe2e7ff);
  static const selectedGreen = Color(0xff0ebdab);

  static const color5f6687 = Color(0xff5f6687);

  static const listColor = Color(0xffFFF5F5);
  static const dashboardBgColor = Color(0xffF3F3F3);

  ///For Job
  static const jobBgColor = Color(0xffF2F2F2);
  static const highlightsCardColor2 = Color(0xffF4900C);

  static const gradientRed = Color(0xffFF2252);
  static const gradientOrange = Color(0xffFC7B04);


  //ASSESSMENT COLORS
  static const answered = Color(0xFFFF9D5C);
  static const notAnswered = Color(0xFFFFEB3B);
  static const answeredReviews = Color(0xFF2D75DD);

  static const novoice = Color(0xFFFED944);
  static const learner = Color(0xFFFF9A00);
  static const master = Color(0xFFFE8B66);
  static const expert = Color(0xFF61ABCA);
  static const leader = Color(0xFF629BCA);

  static const appbarColor = Color(0xff2c73d9);
  static const buttoncolor = Color(0xff2c73d9);
  static const dashboardApplyColor = Color(0xff263367);

  static const buildPortfolio1 = Color(0xffF4900C);
  static const buildPortfolio2 = Color(0xffFFFFFF);


  Color? primaryColor() {
    return APK_DETAILS['gradient_right'] != APK_DETAILS['gradient_left']
        ? null
        : HexColor.fromHex(APK_DETAILS['theme_color']!);
  }

  Color primaryColorAlways() {
    return HexColor.fromHex(APK_DETAILS['theme_color']!);
  }

  Color primaryColorbtnAlways() {
    // return Colors.red;
    return HexColor.fromHex(APK_DETAILS['theme_color']!);
  }

  Color primaryColorGradient() {
    return HexColor.fromHex(APK_DETAILS['theme_color_gradient']!);
  }

  Color buttonColor() {
    return HexColor.fromHex(APK_DETAILS['theme_color']!);
  }

  Color primaryForgroundColor() {
    return HexColor.fromHex(APK_DETAILS['theme_forground_color']!);
  }

  Color gradientLeft() {
    return HexColor.fromHex(APK_DETAILS['gradient_left']!);
  }

  Color gradientRight() {
    return HexColor.fromHex(APK_DETAILS['gradient_right']!);
  }

  static Color bottomNavigation() {
    return HexColor.fromHex('ffffff');
  }

  static Color selectedColor() {
    return HexColor.fromHex(APK_DETAILS['gradient_right']!);
  }

  static Color bottomNavigationSelected() {
    return HexColor.fromHex(
        '${APK_DETAILS['gradient_right']}'); // to configuration config file
    //return HexColor.fromHex('004E90');
  }

  static Color bottomNavigationUnSelected() {
    return HexColor.fromHex('727C95');
  }

  /// Get sub-heading text color based on theme mode
  static Color subHeadingTextColor(bool isDarkMode) {
    return isDarkMode ? Colors.white70 : SUB_HEADING_TITLE;
  }
}


//VIEW_ALL , PRIMARY_COLOR, PRIMARY_COLOR_DARK , //job bottom color on dashboard DASHBOARD_APPLY_COLOR
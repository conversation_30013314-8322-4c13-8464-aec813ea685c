import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

extension SnackbarExtension on String {
  void showSnackbar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          this,
          style: TextStyle(color: context.appColors.textBlack),
        ),
        backgroundColor: context.surfaceColor,
      ),
    );
  }
}

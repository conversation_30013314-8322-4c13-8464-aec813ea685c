
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:masterg/utils/offline_data/url_model.dart';
import 'package:open_filex/open_filex.dart';



class ViewOfflineVideoPage extends StatefulWidget {
  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<ViewOfflineVideoPage> {
  //final boxName = 'videos_offline';
  final boxName = 'pdf_offline';
  Box<URLModel>? urlBox;

  @override
  void initState() {
    super.initState();
    _openBox();
  }

  Future<void> _openBox() async {
    if (urlBox == null || !urlBox!.isOpen) {
      urlBox = await Hive.openBox<URLModel>(boxName);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('You Library'),
      ),
      body: FutureBuilder(
        future: urlBox != null && urlBox!.isOpen ? Future.value(true) : Hive.openBox<URLModel>(boxName),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            return ListView.builder(
              itemCount: urlBox?.values.toList().length,
              itemBuilder: (context, index) {
                final urlModel = urlBox?.values.toList();
               //var thumbnail = urlModel?[index].url.split('::');
                return Container(
                  height: 90,
                  margin: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.grey [200]
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () async{
                          print('pdf on click object');
                          if(urlModel[index].view == 'pdf'){
                            print('PDF URL:   ${urlModel[index].url}');
                            await OpenFilex.open('${urlModel[index].url}');
                          }
                        },

                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius:
                              BorderRadius.circular(
                                  10),
                              child: CachedNetworkImage(
                                height: 60,
                                width: 60,
                                imageUrl:
                                '${urlModel![index].thumbnail}',
                                imageBuilder: (context,
                                    imageProvider) =>
                                    Container(
                                      decoration:
                                      BoxDecoration(
                                          image:
                                          DecorationImage(
                                            image:
                                            imageProvider,
                                            fit:
                                            BoxFit.fill,
                                          )),
                                    ),
                                placeholder:
                                    (context,
                                    url) =>
                                     Image.asset(
                                       urlModel[index].view != 'pdf' ? 'assets/images/placeholder.png' : 'assets/images/pdf.png',
                                      fit: BoxFit.fill,
                                    ),
                                errorWidget:
                                    (context, url,
                                    error) =>
                                    Image.asset(
                                      urlModel[index].view != 'pdf' ? 'assets/images/placeholder.png' : 'assets/images/pdf.png',
                                      fit: BoxFit.fill,
                                    ),
                              ),
                            ),

                            Container(
                              margin: EdgeInsets.only(left: 10.0, right: 10.0, top: 5.0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  //Text('${urlModel?[index].url}'),
                                  Text('${urlModel[index].des}'),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 5.0),
                                    child: Text('${urlModel[index].view} View'),
                                  ),

                                  //Text('${urlModel?[index].postid}'),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    if(urlBox != null) {
      urlBox!.close();
    }
    super.dispose();
  }
}
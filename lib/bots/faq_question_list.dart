import 'package:flutter/material.dart';
import 'package:masterg/bots/bot.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class FAQQuestionPage extends StatelessWidget {
  final List<String> questions = [
    'How to reset password?',
    'How to delete account?',
    'How to enroll in a course?',
    'How to contact customer support?',
    'How to update personal information?',
    'How to change email address?',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.WHITE,
      floatingActionButton: FloatingActionButton(
      backgroundColor: Colors.teal[400],
        child: Icon(Icons.chat_outlined),
        onPressed: (){
        Navigator.push(context, NextPageRoute(Bot()));
      }),
      appBar: AppBar(
        iconTheme: IconThemeData(color: ColorConstants.BLACK),
        titleTextStyle: Styles.bold(),
        elevation: 0,
        backgroundColor: ColorConstants.WHITE,
        title: Text(
          'How to use WOW',
          style: Styles.bold(),
        ),
      ),
      body: ListView.builder(
        itemCount: questions.length,
        itemBuilder: (context, index) {
          return FAQItem(
            question: questions[index],
            onPressed: () {
              // Handle the action when a question is clicked
              print('Question clicked: ${questions[index]}');
              Navigator.push(
                  context,
                  NextPageRoute(Bot(
                    question: questions[index],
                  )));
            },
          );
        },
      ),
    );
  }
}

class FAQItem extends StatelessWidget {
  final String question;
  final VoidCallback onPressed;

  FAQItem({required this.question, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              question,
              style: Styles.regular(size: 16),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorConstants.GREY_3,
              size: 15,
            )
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:masterg/bots/faq_question_list.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class BotFAQPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.WHITE,
      appBar: AppBar(
        iconTheme: IconThemeData(color: ColorConstants.BLACK),
        titleTextStyle: Styles.bold(),
        elevation: 0,
        backgroundColor: ColorConstants.WHITE,
        title: Text('FAQs'),
      ),
      body: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FAQContainer(
              title: 'How to use WOW',
              icon: Icons.info,
              bgColor: ColorConstants().gradientLeft(),
              onPressed: () {
                Navigator.push(context, NextPageRoute(FAQQuestionPage()));
                // Add the action when the first container is clicked
                print('How to use WOW clicked!');
              },
            ),
            SizedBox(width: 16.0),
            FAQContainer(
              title: 'Customer Support',
              icon: Icons.support,
              bgColor: ColorConstants.GREEN,
              onPressed: () {
                // Add the action when the second container is clicked
                print('Customer Support clicked!');
              },
            ),
          ],
        ),
      ),
    );
  }
}

class FAQContainer extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onPressed;
  final Color bgColor;

  FAQContainer(
      {required this.title,
      required this.icon,
      required this.onPressed,
      required this.bgColor});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 160.0,
        height: 160.0,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 3,
              offset: Offset(1, -1), // changes position of shadow
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
                width: 40,
                height: 40,
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                    color: bgColor, borderRadius: BorderRadius.circular(6)),
                child: Icon(
                  icon,
                  color: ColorConstants.WHITE,
                  size: 30.0,
                )),
            SizedBox(height: 10.0),
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

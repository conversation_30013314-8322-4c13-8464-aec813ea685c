import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/gestures.dart';

import 'graph.dart';

class BotMessagesScreen extends StatefulWidget {
  final List<Map<String, dynamic>> messages;
  final Function? sendValue;
  final String? name;
  const BotMessagesScreen(
      {Key? key, required this.messages, this.sendValue, required this.name})
      : super(key: key);

  @override
  BotMessagesScreenState createState() => BotMessagesScreenState();
}

class BotMessagesScreenState extends State<BotMessagesScreen> {
  late ScrollController listScrollController = ScrollController();
  scrollToBottom() {
    listScrollController.jumpTo(listScrollController.position.maxScrollExtent);
  }

  Future<void> scrollListToEND() async {
    await Future.delayed(const Duration(milliseconds: 700));
    listScrollController.animateTo(
      listScrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 700),
      curve: Curves.ease,
    );
  }

  String getFormattedTime() {
    DateTime now = DateTime.now();
    String formattedTime = DateFormat.Hms().format(now);
    return formattedTime;
  }

  @override
  void initState() {
    super.initState();
    listScrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    listScrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (listScrollController.offset >=
            listScrollController.position.maxScrollExtent &&
        !listScrollController.position.outOfRange) {
      // Reach the end of the list, perform any desired action here
    }
  }

  String? selectedOption;

  @override
  Widget build(BuildContext context) {
    // DateTime now = DateTime.now();

    var w = MediaQuery.of(context).size.width;
    scrollListToEND();
    return SafeArea(
      child: Column(
        children: [
          SizedBox(height: 10),
          Flexible(
            child: ListView.builder(
                controller: listScrollController,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  String msg = widget.messages[index]['msg'];

                  // if (index == widget.messages.length - 1) {
                  //   WidgetsBinding.instance.addPostFrameCallback((_) {
                  //     scrollListToEND();
                  //   });
                  // }

                  return Container(
                    margin: const EdgeInsets.all(10),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: widget.messages[index]['type'] != 'bot'
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                      children: [
                        if (widget.messages[index]['type'] == 'bot')
                          Image.asset(
                            'assets/images/chatbot.gif',
                            width: width(context) * 0.08,
                          ),
                        const SizedBox(
                          width: 8,
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                              vertical: widget.messages[index]['type'] != 'bot'
                                  ? 10
                                  : 14,
                              horizontal: 14),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                bottomLeft: const Radius.circular(
                                  20,
                                ),
                                topRight: const Radius.circular(20),
                                bottomRight: Radius.circular(
                                    widget.messages[index]['type'] != 'bot'
                                        ? 0
                                        : 20),
                                topLeft: Radius.circular(
                                    widget.messages[index]['type'] != 'bot'
                                        ? 20
                                        : 0),
                              ),
                              color: widget.messages[index]['type'] != 'bot'
                                  ? const Color(0xff007071)
                                  : const Color(0xffF2F2F2)),
                          constraints: widget.messages[index]['type'] != 'bot'
                              ? null
                              : widget.messages[index]['graph'] != null
                                  ? BoxConstraints(maxWidth: w * 2.5 / 3)
                                  : BoxConstraints(maxWidth: w * 2.5 / 3),
                          child: Column(
                            children: [
                              Text('$msg',
                                  style: Styles.regular(
                                      size: 12,
                                      color: widget.messages[index]['type'] !=
                                              'bot'
                                          ? ColorConstants.WHITE
                                          : ColorConstants.BLACK)),

                              if (widget.messages[index]['graph'] != null)
                                SizedBox(
                                  height: height(context) *
                                      (widget.messages[index]['detail'] ==
                                              'true'
                                          ? 0.305
                                          : 0.5),
                                  child: BotLineChartWidget(
                                    name: widget.name,
                                    domainid: widget.messages[index]['graph'],
                                    hideTable: true,
                                    showDetailOnly: widget.messages[index]
                                            ['detail'] ==
                                        'true',
                                  ),
                                ),
                              if (widget.messages[index]['options'] != null)
                                ListView.builder(
                                    physics: NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount: widget
                                        .messages[index]['options'].length,
                                    itemBuilder: (c, i) {
                                      return InkWell(
                                        onTap: () {
                                          if (selectedOption != null) {
                                            return;
                                          }
                                          setState(() {
                                            selectedOption =
                                                '${widget.messages[index]['options'][i]}';
                                          });
                                          widget.sendValue!(
                                              '${widget.messages[index]['options'][i]}');
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                              border: selectedOption ==
                                                      '${widget.messages[index]['options'][i]}'
                                                  ? Border.all(
                                                      color: ColorConstants()
                                                          .primaryColorbtnAlways())
                                                  : null,
                                              color: ColorConstants.WHITE,
                                              borderRadius:
                                                  BorderRadius.circular(4)),
                                          margin: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 4),
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 8),
                                          child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  '${widget.messages[index]['options'][i]}',
                                                  style:
                                                      Styles.regular(size: 12),
                                                ),
                                                Icon(
                                                  Icons.north_east,
                                                  size: 12,
                                                )
                                              ]),
                                        ),
                                      );
                                    })
                              // if(widget.messages[index]['options'].length != 0)
                            ],
                          ),
                        ),
                        if (widget.messages[index]['type'] != 'bot') ...[
                          const SizedBox(
                            width: 8,
                          ),
                          SizedBox(
                            height: width(context) * 0.08,
                            width: width(context) * 0.08,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(100),
                              child: CachedNetworkImage(
                                imageUrl:
                                    '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                width: width(context) * 0.08,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => SvgPicture.asset(
                                  'assets/images/default_user.svg',
                                  width: width(context) * 0.08,
                                ),
                                errorWidget: (context, url, error) =>
                                    SvgPicture.asset(
                                  'assets/images/default_user.svg',
                                  width: width(context) * 0.08,
                                ),
                              ),
                            ),
                          ),
                        ]
                      ],
                    ),
                  );
                },
                itemCount: widget.messages.length),
          ),
        ],
      ),
    );
  }
}

class ClickableLink extends StatelessWidget {
  final String text;
  final String time;
  final bool isUserMessage;

  const ClickableLink(
      {Key? key,
      required this.text,
      required this.isUserMessage,
      required this.time})
      : super(key: key);

  void openLink(String url) {
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = "http://$url";
      print('url is hel;lo $url');
    }
    //  launchUrl(Uri.parse(url));

    launchUrl(Uri.parse(url));
  }

  @override
  Widget build(BuildContext context) {
    final pattern = RegExp(r'https?://\S+');
    final matches = pattern.allMatches(text);
    final List<TextSpan> spans = [];

    int start = 0;

    for (final match in matches) {
      final linkText = match.group(0);
      if (linkText != null) {
        final linkStart = match.start;
        final linkEnd = match.end;

        if (start < linkStart) {
          spans.add(TextSpan(text: text.substring(start, linkStart)));
        }

        spans.add(
          TextSpan(
            text: linkText,
            style: const TextStyle(
              color: Colors.blue,
              decoration: TextDecoration.underline,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () =>
                  openLink(linkText.replaceAll(')', '').replaceAll('(', '')),
          ),
        );

        start = linkEnd;
      }
    }

    if (start < text.length) {
      spans.add(TextSpan(text: text.substring(start)));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isUserMessage)
          Text(
            'Edulyst Venture',
          ),
        const SizedBox(height: 10),
        RichText(
          text: TextSpan(children: spans),
        ),
        const SizedBox(width: 5),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              time,
              style: TextStyle(fontSize: 10, color: Colors.grey),
            ),
            SizedBox(width: 5),
            if (isUserMessage)
              Icon(
                Icons.done_all,
                color: Colors.blue,
                size: 18,
              ),
          ],
        ),
      ],
    );
  }
}

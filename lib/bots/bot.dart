//import 'package:dialog_flowtter/dialog_flowtter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/bots/messages.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class Bot extends StatefulWidget {
  final String? question;
  const Bot({Key? key, this.question}) : super(key: key);

  @override
  _BotState createState() => _BotState();
}

class _BotState extends State<Bot> {
  //DialogFlowtter? dialogFlowtter;
  final TextEditingController _controller = TextEditingController();

  List<Map<String, dynamic>> messages = [];
  Color themeColor = Color.fromARGB(255, 190, 183, 249);

  @override
  void initState() {
    // DialogFlowtter.fromFile()
    //     .then((instance) => dialogFlowtter = instance)
    //     .then((value) {
    //   if (widget.question != null)
    //     sendMessage('${widget.question}');
    //   else
    //     sendMessage(
    //         '${Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)}')}');
    // });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          titleTextStyle: Styles.bold(),
          iconTheme: IconThemeData(color: ColorConstants.BLACK),
          centerTitle: true,
          title: Transform.translate(
              offset: Offset(-20, 0),
              child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                Image.asset(
                  'assets/images/chatBot.gif',
                  width: width(context) * 0.12,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ahlam',
                      style: Styles.semibold(size: 14),
                    ),
                    Text(
                      'Active',
                      style: Styles.regular(size: 10),
                    ),
                  ],
                ),
              ])),
          elevation: 0.5,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  // Color.fromARGB(120, 146, 187, 227),
                  // Color.fromARGB(255, 28, 115, 142),
                  // themeColor,
                  // themeColor,
                  Colors.white,
                  Colors.white,
                ],
              ),
            ),
          ),
        ),
        body: Stack(
          children: [
            Positioned(
                right: -10,
                top: -150,
                child: Transform.scale(
                  scale: 2,
                  child: SvgPicture.asset(
                    'assets/images/bot_pattern.svg',
                    width: width(context),
                  ),
                )),
            Container(
              child: Column(
                mainAxisAlignment: messages.length == 1
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (messages.length == 1) ...[
                    Spacer(),
                    Image.asset(
                      'assets/images/chatBot.gif',
                      width: width(context) * 0.4,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: 
                      Text(
                        'Hello! ${Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)}')}👋\n I am Ahlam, your career guide. What career path are you considering?.',
                        // '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][0]['msg']}',
                        style: Styles.bold(),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Spacer(),
                  ] else ...[
                    Expanded(
                        child: MessagesScreen(
                           sendValue: (value) {
                      Preference.setString(Preference.SETUP_GOAL, value);
                       sendMessage(value);
                    },
                      messages: messages,
                      blankPage: widget.question == null,
                    )),
                  ],
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xff007071),
                          Color(0xff007071),
                        ],
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _controller,
                            style: const TextStyle(color: Colors.white),
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              hintText: 'Type a message...',
                              hintStyle: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Cera Pro',
                              ),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            sendMessage(_controller.text);
                            _controller.clear();
                          },
                          icon: const Icon(Icons.send_rounded),
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  sendMessage(String text) async {
    print('Message is $text');

    try {
      if (text.isEmpty) {
      } else {
        setState(() {
          //addMessage(Message(text: DialogText(text: [text])), true);
        });

        // DetectIntentResponse? response = await dialogFlowtter?.detectIntent(
        //   queryInput: QueryInput(
        //     text: TextInput(text: text),
        //   ),
        // );
        // if (response?.message == null) return;
        // setState(() {
        //   addMessage(response!.message!);
        // });
      }
    } catch (e) {
      print('Error occurred in sendMessage: $e');
    }
  }
  //
  // addMessage(Message message, [bool isUserMessage = false]) {
  //   messages.add({'message': message, 'isUserMessage': isUserMessage});
  // }
}

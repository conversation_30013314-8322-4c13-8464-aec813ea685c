
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';

class CustomError extends StatefulWidget {
  final FlutterErrorDetails errorDetails;
  const CustomError({
    Key? key,
    required this.errorDetails,
  }) : super(key: key);

  @override
  State<CustomError> createState() => _CustomErrorState();
}

class _CustomErrorState extends State<CustomError> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      height: double.infinity,
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('${widget.errorDetails}'),
          SvgPicture.asset(
            height: MediaQuery.of(context).size.height * 0.25,
            'assets/images/error.svg',
            fit: BoxFit.cover,
          ),
          SizedBox(height: height(context) * 0.05),
          Text(
            'looks_like_messed_up',
            style: Styles.bold(size: 16),
          ).tr(),
          SizedBox(height: height(context) * 0.01),
          Text(
            'plz_reconnecting_network',
            style: Styles.regular(size: 14),
          ).tr(),
        ],
      ),
    );
  }

  void logEvent() async {
    try {
      await FirebaseAnalytics.instance.logEvent(
        name: "data_error_log",
        parameters: {
          "userID": Preference.getInt(Preference.USER_ID) ?? 1,
          "error": '${widget.errorDetails}'
        },
      );
    } catch (e) {}
  }
}
